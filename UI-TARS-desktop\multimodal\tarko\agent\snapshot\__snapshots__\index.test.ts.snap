// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AgentSnapshot tests > should match snapshot for gui-agent/basic 1`] = `
{
  "snapshotName": "basic",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 4
}
`;

exports[`AgentSnapshot tests > should match snapshot for gui-agent/basic 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": [
      {
        "type": "text",
        "text": "What is Agent TARS"
      }
    ]
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "[Complex multimodal input]"
    },
    "provider": "volcengine",
    "model": "ep-20250510145437-5sxhs"
  },
  {
    "id": "<<ID>>",
    "type": "environment_input",
    "timestamp": "<<TIMESTAMP>>",
    "description": "Browser Screenshot",
    "content": [
      {
        "type": "text",
        "text": "Screenshot: "
      },
      {
        "type": "image_url",
        "image_url": "<<image_url>>"
      }
    ]
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Initiating search for 'What is Agent TARS' on Google.",
    "rawContent": "Initiating search for 'What is Agent TARS' on Google.",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "browser_action_tool",
          "arguments": "{\\"thought\\":\\"To find information about Agent TARS, the next step is to input the query into the Google search bar. The search bar is visible in the center of the page, so I need to type the query there.\\",\\"step\\":\\"Type 'What is Agent TARS' into the Google search bar located in the center of the page.\\",\\"action\\":\\"type(content='What is Agent TARS')\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "browser_action_tool",
    "arguments": {
      "thought": "To find information about Agent TARS, the next step is to input the query into the Google search bar. The search bar is visible in the center of the page, so I need to type the query there.",
      "step": "Type 'What is Agent TARS' into the Google search bar located in the center of the page.",
      "action": "type(content='What is Agent TARS')"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "browser_action_tool",
      "description": "A browser tool to perform the next action to complete the task.\\n\\n## Action Space\\n\\nclick(point='<point>x1 y1</point>')\\nleft_double(point='<point>x1 y1</point>')\\nright_single(point='<point>x1 y1</point>')\\ndrag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')\\nhotkey(key='ctrl c') # Split keys with a space and use lowercase. Also, do not use more than 3 keys in one hotkey action.\\ntype(content='xxx') # Use escape characters \\\\', \\\\\\", and \\\\n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\\\n at the end of content. \\nscroll(point='<point>x1 y1</point>', direction='down or up or right or left') # Show more information on the \`direction\` side.\\nwait() #Sleep for 5s and take a screenshot to check for any changes.\\nfinished(content='xxx') # Use escape characters \\\\', \\", and \\\\n in content part to ensure we can parse the content in normal python string format.\\n\\n\\n## Note\\n- Use English in \`Thought\` part.\\n- Describe your detailed thought in \`Thought\` part.\\n- Describe your action in \`Step\` part.\\n\\n",
      "schema": {
        "type": "object",
        "properties": {
          "thought": {
            "type": "string",
            "description": " your observation and small plan in one sentence, DO NOT include \\" characters to avoid failure to render in JSON"
          },
          "step": {
            "type": "string",
            "description": "Finally summarize the next action (with its target element) in one sentence"
          },
          "action": {
            "type": "string",
            "description": "some action in action space like clike or press"
          }
        },
        "required": [
          "thought",
          "step",
          "action"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "browser_action_tool",
    "content": {
      "action": "type(content='What is Agent TARS')",
      "status": "success"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "environment_input",
    "timestamp": "<<TIMESTAMP>>",
    "description": "Browser Screenshot",
    "content": [
      {
        "type": "text",
        "text": "Screenshot: "
      },
      {
        "type": "image_url",
        "image_url": "<<image_url>>"
      }
    ]
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Submitting search query for 'What is Agent TARS'.",
    "rawContent": "Submitting search query for 'What is Agent TARS'.",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "browser_action_tool",
          "arguments": "{\\"thought\\":\\"The search query 'What is Agent TARS' is already typed into the Google search bar, so the next logical step is to click the 'Google 搜索' button to execute the search and retrieve results.\\",\\"step\\":\\"Click on the 'Google 搜索' button located below the search bar to submit the query and display search results.\\",\\"action\\":\\"click(point='<point>454 525</point>')\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "browser_action_tool",
    "arguments": {
      "thought": "The search query 'What is Agent TARS' is already typed into the Google search bar, so the next logical step is to click the 'Google 搜索' button to execute the search and retrieve results.",
      "step": "Click on the 'Google 搜索' button located below the search bar to submit the query and display search results.",
      "action": "click(point='<point>454 525</point>')"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "browser_action_tool",
      "description": "A browser tool to perform the next action to complete the task.\\n\\n## Action Space\\n\\nclick(point='<point>x1 y1</point>')\\nleft_double(point='<point>x1 y1</point>')\\nright_single(point='<point>x1 y1</point>')\\ndrag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')\\nhotkey(key='ctrl c') # Split keys with a space and use lowercase. Also, do not use more than 3 keys in one hotkey action.\\ntype(content='xxx') # Use escape characters \\\\', \\\\\\", and \\\\n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\\\n at the end of content. \\nscroll(point='<point>x1 y1</point>', direction='down or up or right or left') # Show more information on the \`direction\` side.\\nwait() #Sleep for 5s and take a screenshot to check for any changes.\\nfinished(content='xxx') # Use escape characters \\\\', \\", and \\\\n in content part to ensure we can parse the content in normal python string format.\\n\\n\\n## Note\\n- Use English in \`Thought\` part.\\n- Describe your detailed thought in \`Thought\` part.\\n- Describe your action in \`Step\` part.\\n\\n",
      "schema": {
        "type": "object",
        "properties": {
          "thought": {
            "type": "string",
            "description": " your observation and small plan in one sentence, DO NOT include \\" characters to avoid failure to render in JSON"
          },
          "step": {
            "type": "string",
            "description": "Finally summarize the next action (with its target element) in one sentence"
          },
          "action": {
            "type": "string",
            "description": "some action in action space like clike or press"
          }
        },
        "required": [
          "thought",
          "step",
          "action"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "browser_action_tool",
    "content": {
      "action": "click(point='<point>454 525</point>')",
      "status": "success"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "environment_input",
    "timestamp": "<<TIMESTAMP>>",
    "description": "Browser Screenshot",
    "content": [
      {
        "type": "text",
        "text": "Screenshot: "
      },
      {
        "type": "image_url",
        "image_url": "<<image_url>>"
      }
    ]
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "",
    "rawContent": "",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "browser_action_tool",
          "arguments": "{\\"thought\\":\\"Wait for the page to load completely to view the search results for 'What is Agent TARS'.\\",\\"step\\":\\"Wait for 5 seconds to allow the Google search results page to load.\\",\\"action\\":\\"wait()\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "browser_action_tool",
    "arguments": {
      "thought": "Wait for the page to load completely to view the search results for 'What is Agent TARS'.",
      "step": "Wait for 5 seconds to allow the Google search results page to load.",
      "action": "wait()"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "browser_action_tool",
      "description": "A browser tool to perform the next action to complete the task.\\n\\n## Action Space\\n\\nclick(point='<point>x1 y1</point>')\\nleft_double(point='<point>x1 y1</point>')\\nright_single(point='<point>x1 y1</point>')\\ndrag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>')\\nhotkey(key='ctrl c') # Split keys with a space and use lowercase. Also, do not use more than 3 keys in one hotkey action.\\ntype(content='xxx') # Use escape characters \\\\', \\\\\\", and \\\\n in content part to ensure we can parse the content in normal python string format. If you want to submit your input, use \\\\n at the end of content. \\nscroll(point='<point>x1 y1</point>', direction='down or up or right or left') # Show more information on the \`direction\` side.\\nwait() #Sleep for 5s and take a screenshot to check for any changes.\\nfinished(content='xxx') # Use escape characters \\\\', \\", and \\\\n in content part to ensure we can parse the content in normal python string format.\\n\\n\\n## Note\\n- Use English in \`Thought\` part.\\n- Describe your detailed thought in \`Thought\` part.\\n- Describe your action in \`Step\` part.\\n\\n",
      "schema": {
        "type": "object",
        "properties": {
          "thought": {
            "type": "string",
            "description": " your observation and small plan in one sentence, DO NOT include \\" characters to avoid failure to render in JSON"
          },
          "step": {
            "type": "string",
            "description": "Finally summarize the next action (with its target element) in one sentence"
          },
          "action": {
            "type": "string",
            "description": "some action in action space like clike or press"
          }
        },
        "required": [
          "thought",
          "step",
          "action"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "browser_action_tool",
    "content": {
      "action": "wait()",
      "status": "success"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "environment_input",
    "timestamp": "<<TIMESTAMP>>",
    "description": "Browser Screenshot",
    "content": [
      {
        "type": "text",
        "text": "Screenshot: "
      },
      {
        "type": "image_url",
        "image_url": "<<image_url>>"
      }
    ]
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Agent TARS is Bytedance's new open-source approach for automating complex tasks by visually interpreting web content and interacting with the command line and file system.",
    "rawContent": "Agent TARS is Bytedance's new open-source approach for automating complex tasks by visually interpreting web content and interacting with the command line and file system.",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 4,
    "elapsedMs": "<<elapsedMs>>",
    "status": "idle"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for gui-agent/basic 3`] = `
{
  "id": "<<ID>>",
  "type": "assistant_message",
  "timestamp": "<<TIMESTAMP>>",
  "content": "Agent TARS is Bytedance's new open-source approach for automating complex tasks by visually interpreting web content and interacting with the command line and file system.",
  "rawContent": "Agent TARS is Bytedance's new open-source approach for automating complex tasks by visually interpreting web content and interacting with the command line and file system.",
  "finishReason": "stop",
  "messageId": "<<messageId>>"
}
`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls 1`] = `
{
  "snapshotName": "tool-calls",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 3
}
`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "How's the weather today?"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "How's the weather today?",
      "stream": true
    },
    "provider": "volcengine",
    "model": "ep-20250510145437-5sxhs"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "To",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " get",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " weather",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " I",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " first",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " need",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " user",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "'s",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " current",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " location",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " So",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " call",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " get",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Current",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Location",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " to",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " retrieve",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " that",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " information",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getCurrentLocation",
    "arguments": "",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getCurrentLocation",
    "arguments": "{",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getCurrentLocation",
    "arguments": "}",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getCurrentLocation",
    "arguments": "",
    "isComplete": true,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "To get the weather, I first need the user's current location. So call getCurrentLocation to retrieve that information.",
    "rawContent": "",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getCurrentLocation",
          "arguments": "{}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "arguments": {},
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getCurrentLocation",
      "description": "Get user's current location",
      "schema": {
        "type": "object",
        "properties": {}
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "content": {
      "location": "Boston"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Now",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " that",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " we",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " have",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " location",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " \\"",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Boston",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "\\",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " we",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " can",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " call",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " get",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Weather",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " with",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " this",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " location",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " to",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " get",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " weather",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " information",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "{\\"",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "location",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "\\":\\"",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "Boston",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "\\"}",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "",
    "isComplete": true,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Now that we have the location \\"Boston\\", we can call getWeather with this location to get the weather information.",
    "rawContent": "",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getWeather",
          "arguments": "{\\"location\\":\\"Boston\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "arguments": {
      "location": "Boston"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getWeather",
      "description": "Get weather information for a specified location",
      "schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location name, such as city name"
          }
        },
        "required": [
          "location"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "content": {
      "location": "Boston",
      "temperature": "70°F (21°C)",
      "condition": "Sunny",
      "precipitation": "10%",
      "humidity": "45%",
      "wind": "5 mph"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "The",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " weather",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " information",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " for",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " Boston",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " now",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " available",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " so",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " we",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " can",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " directly",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " respond",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " with",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " details",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "\\n",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "In",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " Boston",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " weather",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " today",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " Sunny",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " The",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " temperature",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "7",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "0",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "°F",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " (",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "2",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "1",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "°C",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "),",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " with",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " a",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "1",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "0",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "%",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " chance",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " of",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " precipitation",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "4",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "5",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "%",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " humidity",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " and",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " wind",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " speed",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " of",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "5",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " mph",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "</think>",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "In",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " Boston",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " weather",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " today",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " Sunny",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " The",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " temperature",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "7",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "0",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "°F",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " (",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "2",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "1",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "°C",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "),",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " with",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " a",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "1",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "0",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "%",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " chance",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " of",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " precipitation",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "4",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "5",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "%",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " humidity",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " and",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " wind",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " speed",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " of",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "5",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " mph",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "The weather information for Boston is now available, so we can directly respond with the details.\\nIn Boston, the weather today is Sunny. The temperature is 70°F (21°C), with a 10% chance of precipitation, 45% humidity, and wind speed of 5 mph.</think>In Boston, the weather today is Sunny. The temperature is 70°F (21°C), with a 10% chance of precipitation, 45% humidity, and wind speed of 5 mph.",
    "rawContent": "",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 3,
    "elapsedMs": "<<elapsedMs>>",
    "status": "executing"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls 3`] = `{}`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls-prompt-engineering-impl 1`] = `
{
  "snapshotName": "tool-calls-prompt-engineering-impl",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 3
}
`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls-prompt-engineering-impl 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "How's the weather today?"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "How's the weather today?",
      "stream": true
    },
    "provider": "volcengine",
    "model": "ep-20250510145437-5sxhs"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getCurrentLocation",
    "arguments": "",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getCurrentLocation",
    "arguments": "{}",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getCurrentLocation",
    "arguments": "",
    "isComplete": true,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "",
    "rawContent": "<tool_call>\\n{\\n  \\"name\\": \\"getCurrentLocation\\",\\n  \\"parameters\\": {}\\n}\\n</tool_call>",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getCurrentLocation",
          "arguments": "{}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "arguments": {},
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getCurrentLocation",
      "description": "Get user's current location",
      "schema": {
        "type": "object",
        "properties": {}
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "content": {
      "location": "Boston"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "{",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "\\n",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "   ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": " \\"",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "location",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "\\":",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": " \\"",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "Boston",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "\\"",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "\\n",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": " }",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "toolName": "getWeather",
    "arguments": "",
    "isComplete": true,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "",
    "rawContent": "<tool_call>\\n{\\n  \\"name\\": \\"getWeather\\",\\n  \\"parameters\\": {\\n    \\"location\\": \\"Boston\\"\\n  }\\n}\\n</tool_call>",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getWeather",
          "arguments": "{\\"location\\":\\"Boston\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "arguments": {
      "location": "Boston"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getWeather",
      "description": "Get weather information for a specified location",
      "schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location name, such as city name"
          }
        },
        "required": [
          "location"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "content": {
      "location": "Boston",
      "temperature": "70°F (21°C)",
      "condition": "Sunny",
      "precipitation": "10%",
      "humidity": "45%",
      "wind": "5 mph"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Today",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " in",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " Boston",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ",",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " weather",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " sunny",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " with",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " a",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " temperature",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " of",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "7",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "0",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "°F",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " (",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "2",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "1",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "°C",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ").",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " The",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " precipitation",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " chance",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "1",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "0",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "%,",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " humidity",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "4",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "5",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "%,",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " and",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " the",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " wind",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " blowing",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " at",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " ",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "5",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " mph",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ".",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Today in Boston, the weather is sunny with a temperature of 70°F (21°C). The precipitation chance is 10%, humidity is 45%, and the wind is blowing at 5 mph.",
    "rawContent": "Today in Boston, the weather is sunny with a temperature of 70°F (21°C). The precipitation chance is 10%, humidity is 45%, and the wind is blowing at 5 mph.",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 3,
    "elapsedMs": "<<elapsedMs>>",
    "status": "executing"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls-prompt-engineering-impl 3`] = `{}`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls-structured-outputs-impl 1`] = `
{
  "snapshotName": "tool-calls-structured-outputs-impl",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 3
}
`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls-structured-outputs-impl 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "How's the weather today?"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "How's the weather today?",
      "stream": true
    },
    "provider": "azure-openai",
    "model": "aws_sdk_claude37_sonnet"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "I",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "'ll check the weather for you. First",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ", I need to find your current location.",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "I'll check the weather for you. First, I need to find your current location.",
    "rawContent": "I'll check the weather for you. First, I need to find your current location.",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getCurrentLocation",
          "arguments": "{}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "arguments": {},
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getCurrentLocation",
      "description": "Get user's current location",
      "schema": {
        "type": "object",
        "properties": {}
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "content": {
      "location": "Boston"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "I",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "'ll",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " check the current",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " weather in",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " Boston for you.",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "I'll check the current weather in Boston for you.",
    "rawContent": "I'll check the current weather in Boston for you.",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getWeather",
          "arguments": "{\\"location\\":\\"Boston\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "arguments": {
      "location": "Boston"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getWeather",
      "description": "Get weather information for a specified location",
      "schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location name, such as city name"
          }
        },
        "required": [
          "location"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "content": {
      "location": "Boston",
      "temperature": "70°F (21°C)",
      "condition": "Sunny",
      "precipitation": "10%",
      "humidity": "45%",
      "wind": "5 mph"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "The weather in Boston today",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " is sunny with a",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": " temperature of 70°",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "F (21°C). There",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "'s low precipitation chance at 10%",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": ", humidity is at 45%, an",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_streaming_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "d a light wind of 5 mph.",
    "isComplete": false,
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "The weather in Boston today is sunny with a temperature of 70°F (21°C). There's low precipitation chance at 10%, humidity is at 45%, and a light wind of 5 mph.",
    "rawContent": "The weather in Boston today is sunny with a temperature of 70°F (21°C). There's low precipitation chance at 10%, humidity is at 45%, and a light wind of 5 mph.",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 3,
    "elapsedMs": "<<elapsedMs>>",
    "status": "executing"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for streaming/tool-calls-structured-outputs-impl 3`] = `{}`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/basic 1`] = `
{
  "snapshotName": "basic",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 3
}
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/basic 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "How's the weather today?"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "How's the weather today?",
      "toolCallEngine": "structured_outputs"
    },
    "provider": "volcengine",
    "model": "ep-20250510145437-5sxhs"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "To get the weather, I first need your current location. Retrieving your location...",
    "rawContent": "To get the weather, I first need your current location. Retrieving your location...",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getCurrentLocation",
          "arguments": "{}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "arguments": {},
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getCurrentLocation",
      "description": "Get user's current location",
      "schema": {
        "type": "object",
        "properties": {}
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "content": {
      "location": "Boston"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Fetching weather for Boston...",
    "rawContent": "Fetching weather for Boston...",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getWeather",
          "arguments": "{\\"location\\":\\"Boston\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "arguments": {
      "location": "Boston"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getWeather",
      "description": "Get weather information for a specified location",
      "schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location name, such as city name"
          }
        },
        "required": [
          "location"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "content": {
      "location": "Boston",
      "temperature": "70°F (21°C)",
      "condition": "Sunny",
      "precipitation": "10%",
      "humidity": "45%",
      "wind": "5 mph"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
    "rawContent": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 3,
    "elapsedMs": "<<elapsedMs>>",
    "status": "idle"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/basic 3`] = `
{
  "id": "<<ID>>",
  "type": "assistant_message",
  "timestamp": "<<TIMESTAMP>>",
  "content": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
  "rawContent": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
  "finishReason": "stop",
  "messageId": "<<messageId>>"
}
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/prompt-engineering-impl 1`] = `
{
  "snapshotName": "prompt-engineering-impl",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 3
}
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/prompt-engineering-impl 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "How's the weather today?"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "How's the weather today?"
    },
    "provider": "volcengine",
    "model": "ep-20250510145437-5sxhs"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "",
    "rawContent": "<tool_call>\\n{\\n  \\"name\\": \\"getCurrentLocation\\",\\n  \\"parameters\\": {}\\n}\\n</tool_call>",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getCurrentLocation",
          "arguments": "{}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "arguments": {},
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getCurrentLocation",
      "description": "Get user's current location",
      "schema": {
        "type": "object",
        "properties": {}
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "content": {
      "location": "Boston"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "",
    "rawContent": "<tool_call>\\n{\\n  \\"name\\": \\"getWeather\\",\\n  \\"parameters\\": {\\n    \\"location\\": \\"Boston\\"\\n  }\\n}\\n</tool_call>",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getWeather",
          "arguments": "{\\"location\\":\\"Boston\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "arguments": {
      "location": "Boston"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getWeather",
      "description": "Get weather information for a specified location",
      "schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location name, such as city name"
          }
        },
        "required": [
          "location"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "content": {
      "location": "Boston",
      "temperature": "70°F (21°C)",
      "condition": "Sunny",
      "precipitation": "10%",
      "humidity": "45%",
      "wind": "5 mph"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "In Boston today, the weather is sunny with a temperature of 70°F (21°C). The precipitation chance is 10%, humidity is 45%, and the wind speed is 5 mph.",
    "rawContent": "In Boston today, the weather is sunny with a temperature of 70°F (21°C). The precipitation chance is 10%, humidity is 45%, and the wind speed is 5 mph.",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 3,
    "elapsedMs": "<<elapsedMs>>",
    "status": "idle"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/prompt-engineering-impl 3`] = `
{
  "id": "<<ID>>",
  "type": "assistant_message",
  "timestamp": "<<TIMESTAMP>>",
  "content": "In Boston today, the weather is sunny with a temperature of 70°F (21°C). The precipitation chance is 10%, humidity is 45%, and the wind speed is 5 mph.",
  "rawContent": "In Boston today, the weather is sunny with a temperature of 70°F (21°C). The precipitation chance is 10%, humidity is 45%, and the wind speed is 5 mph.",
  "finishReason": "stop",
  "messageId": "<<messageId>>"
}
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/structured-outputs-impl 1`] = `
{
  "snapshotName": "structured-outputs-impl",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 3
}
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/structured-outputs-impl 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "How's the weather today?"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "How's the weather today?"
    },
    "provider": "volcengine",
    "model": "ep-20250510145437-5sxhs"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "To get the weather, I first need your current location. Retrieving location...",
    "rawContent": "To get the weather, I first need your current location. Retrieving location...",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getCurrentLocation",
          "arguments": "{}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "arguments": {},
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getCurrentLocation",
      "description": "Get user's current location",
      "schema": {
        "type": "object",
        "properties": {}
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "content": {
      "location": "Boston"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Fetching weather for Boston...",
    "rawContent": "Fetching weather for Boston...",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getWeather",
          "arguments": "{\\"location\\":\\"Boston\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "arguments": {
      "location": "Boston"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getWeather",
      "description": "Get weather information for a specified location",
      "schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location name, such as city name"
          }
        },
        "required": [
          "location"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "content": {
      "location": "Boston",
      "temperature": "70°F (21°C)",
      "condition": "Sunny",
      "precipitation": "10%",
      "humidity": "45%",
      "wind": "5 mph"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
    "rawContent": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 3,
    "elapsedMs": "<<elapsedMs>>",
    "status": "idle"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/structured-outputs-impl 3`] = `
{
  "id": "<<ID>>",
  "type": "assistant_message",
  "timestamp": "<<TIMESTAMP>>",
  "content": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
  "rawContent": "Today in Boston, the weather is Sunny with a temperature of 70°F (21°C). Precipitation is 10%, humidity is 45%, and wind speed is 5 mph.",
  "finishReason": "stop",
  "messageId": "<<messageId>>"
}
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/structured-outputs-impl-claude 1`] = `
{
  "snapshotName": "structured-outputs-impl-claude",
  "executionTime": "<<TIMESTAMP>>",
  "loopCount": 3
}
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/structured-outputs-impl-claude 2`] = `
[
  {
    "id": "<<ID>>",
    "type": "user_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "How's the weather today?"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_start",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "runOptions": {
      "input": "How's the weather today?"
    },
    "provider": "azure-openai",
    "model": "aws_sdk_claude37_sonnet"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "I'll check the weather for you. First, I need to determine your location.",
    "rawContent": "I'll check the weather for you. First, I need to determine your location.",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getCurrentLocation",
          "arguments": "{}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "arguments": {},
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getCurrentLocation",
      "description": "Get user's current location",
      "schema": {
        "type": "object",
        "properties": {}
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getCurrentLocation",
    "content": {
      "location": "Boston"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "I'll check the current weather in Boston for you.",
    "rawContent": "I'll check the current weather in Boston for you.",
    "toolCalls": [
      {
        "id": "<<ID>>",
        "type": "function",
        "function": {
          "name": "getWeather",
          "arguments": "{\\"location\\":\\"Boston\\"}"
        }
      }
    ],
    "finishReason": "tool_calls",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "tool_call",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "arguments": {
      "location": "Boston"
    },
    "startTime": "<<TIMESTAMP>>",
    "tool": {
      "name": "getWeather",
      "description": "Get weather information for a specified location",
      "schema": {
        "type": "object",
        "properties": {
          "location": {
            "type": "string",
            "description": "Location name, such as city name"
          }
        },
        "required": [
          "location"
        ]
      }
    }
  },
  {
    "id": "<<ID>>",
    "type": "tool_result",
    "timestamp": "<<TIMESTAMP>>",
    "toolCallId": "<<toolCallId>>",
    "name": "getWeather",
    "content": {
      "location": "Boston",
      "temperature": "70°F (21°C)",
      "condition": "Sunny",
      "precipitation": "10%",
      "humidity": "45%",
      "wind": "5 mph"
    },
    "elapsedMs": "<<elapsedMs>>"
  },
  {
    "id": "<<ID>>",
    "type": "assistant_message",
    "timestamp": "<<TIMESTAMP>>",
    "content": "Today in Boston, it's 70°F (21°C) and sunny with only a 10% chance of precipitation. The humidity is at 45% with light winds at 5 mph. It's a beautiful day!",
    "rawContent": "Today in Boston, it's 70°F (21°C) and sunny with only a 10% chance of precipitation. The humidity is at 45% with light winds at 5 mph. It's a beautiful day!",
    "finishReason": "stop",
    "messageId": "<<messageId>>"
  },
  {
    "id": "<<ID>>",
    "type": "agent_run_end",
    "timestamp": "<<TIMESTAMP>>",
    "sessionId": "<<sessionId>>",
    "iterations": 3,
    "elapsedMs": "<<elapsedMs>>",
    "status": "idle"
  }
]
`;

exports[`AgentSnapshot tests > should match snapshot for tool-calls/structured-outputs-impl-claude 3`] = `
{
  "id": "<<ID>>",
  "type": "assistant_message",
  "timestamp": "<<TIMESTAMP>>",
  "content": "Today in Boston, it's 70°F (21°C) and sunny with only a 10% chance of precipitation. The humidity is at 45% with light winds at 5 mph. It's a beautiful day!",
  "rawContent": "Today in Boston, it's 70°F (21°C) and sunny with only a 10% chance of precipitation. The humidity is at 45% with light winds at 5 mph. It's a beautiful day!",
  "finishReason": "stop",
  "messageId": "<<messageId>>"
}
`;
