{"version": 3, "sources": ["../../../../../../../node_modules/retry/lib/retry_operation.js", "../../../../../../../node_modules/retry/lib/retry.js", "../../../../../../../node_modules/retry/index.js", "../../../../../../../node_modules/async-retry/lib/index.js"], "sourcesContent": ["function RetryOperation(timeouts, options) {\n  // Compatibility for the old (timeouts, retryForever) signature\n  if (typeof options === 'boolean') {\n    options = { forever: options };\n  }\n\n  this._originalTimeouts = JSON.parse(JSON.stringify(timeouts));\n  this._timeouts = timeouts;\n  this._options = options || {};\n  this._maxRetryTime = options && options.maxRetryTime || Infinity;\n  this._fn = null;\n  this._errors = [];\n  this._attempts = 1;\n  this._operationTimeout = null;\n  this._operationTimeoutCb = null;\n  this._timeout = null;\n  this._operationStart = null;\n  this._timer = null;\n\n  if (this._options.forever) {\n    this._cachedTimeouts = this._timeouts.slice(0);\n  }\n}\nmodule.exports = RetryOperation;\n\nRetryOperation.prototype.reset = function() {\n  this._attempts = 1;\n  this._timeouts = this._originalTimeouts.slice(0);\n}\n\nRetryOperation.prototype.stop = function() {\n  if (this._timeout) {\n    clearTimeout(this._timeout);\n  }\n  if (this._timer) {\n    clearTimeout(this._timer);\n  }\n\n  this._timeouts       = [];\n  this._cachedTimeouts = null;\n};\n\nRetryOperation.prototype.retry = function(err) {\n  if (this._timeout) {\n    clearTimeout(this._timeout);\n  }\n\n  if (!err) {\n    return false;\n  }\n  var currentTime = new Date().getTime();\n  if (err && currentTime - this._operationStart >= this._maxRetryTime) {\n    this._errors.push(err);\n    this._errors.unshift(new Error('RetryOperation timeout occurred'));\n    return false;\n  }\n\n  this._errors.push(err);\n\n  var timeout = this._timeouts.shift();\n  if (timeout === undefined) {\n    if (this._cachedTimeouts) {\n      // retry forever, only keep last error\n      this._errors.splice(0, this._errors.length - 1);\n      timeout = this._cachedTimeouts.slice(-1);\n    } else {\n      return false;\n    }\n  }\n\n  var self = this;\n  this._timer = setTimeout(function() {\n    self._attempts++;\n\n    if (self._operationTimeoutCb) {\n      self._timeout = setTimeout(function() {\n        self._operationTimeoutCb(self._attempts);\n      }, self._operationTimeout);\n\n      if (self._options.unref) {\n          self._timeout.unref();\n      }\n    }\n\n    self._fn(self._attempts);\n  }, timeout);\n\n  if (this._options.unref) {\n      this._timer.unref();\n  }\n\n  return true;\n};\n\nRetryOperation.prototype.attempt = function(fn, timeoutOps) {\n  this._fn = fn;\n\n  if (timeoutOps) {\n    if (timeoutOps.timeout) {\n      this._operationTimeout = timeoutOps.timeout;\n    }\n    if (timeoutOps.cb) {\n      this._operationTimeoutCb = timeoutOps.cb;\n    }\n  }\n\n  var self = this;\n  if (this._operationTimeoutCb) {\n    this._timeout = setTimeout(function() {\n      self._operationTimeoutCb();\n    }, self._operationTimeout);\n  }\n\n  this._operationStart = new Date().getTime();\n\n  this._fn(this._attempts);\n};\n\nRetryOperation.prototype.try = function(fn) {\n  console.log('Using RetryOperation.try() is deprecated');\n  this.attempt(fn);\n};\n\nRetryOperation.prototype.start = function(fn) {\n  console.log('Using RetryOperation.start() is deprecated');\n  this.attempt(fn);\n};\n\nRetryOperation.prototype.start = RetryOperation.prototype.try;\n\nRetryOperation.prototype.errors = function() {\n  return this._errors;\n};\n\nRetryOperation.prototype.attempts = function() {\n  return this._attempts;\n};\n\nRetryOperation.prototype.mainError = function() {\n  if (this._errors.length === 0) {\n    return null;\n  }\n\n  var counts = {};\n  var mainError = null;\n  var mainErrorCount = 0;\n\n  for (var i = 0; i < this._errors.length; i++) {\n    var error = this._errors[i];\n    var message = error.message;\n    var count = (counts[message] || 0) + 1;\n\n    counts[message] = count;\n\n    if (count >= mainErrorCount) {\n      mainError = error;\n      mainErrorCount = count;\n    }\n  }\n\n  return mainError;\n};\n", "var RetryOperation = require('./retry_operation');\n\nexports.operation = function(options) {\n  var timeouts = exports.timeouts(options);\n  return new RetryOperation(timeouts, {\n      forever: options && (options.forever || options.retries === Infinity),\n      unref: options && options.unref,\n      maxRetryTime: options && options.maxRetryTime\n  });\n};\n\nexports.timeouts = function(options) {\n  if (options instanceof Array) {\n    return [].concat(options);\n  }\n\n  var opts = {\n    retries: 10,\n    factor: 2,\n    minTimeout: 1 * 1000,\n    maxTimeout: Infinity,\n    randomize: false\n  };\n  for (var key in options) {\n    opts[key] = options[key];\n  }\n\n  if (opts.minTimeout > opts.maxTimeout) {\n    throw new Error('minTimeout is greater than maxTimeout');\n  }\n\n  var timeouts = [];\n  for (var i = 0; i < opts.retries; i++) {\n    timeouts.push(this.createTimeout(i, opts));\n  }\n\n  if (options && options.forever && !timeouts.length) {\n    timeouts.push(this.createTimeout(i, opts));\n  }\n\n  // sort the array numerically ascending\n  timeouts.sort(function(a,b) {\n    return a - b;\n  });\n\n  return timeouts;\n};\n\nexports.createTimeout = function(attempt, opts) {\n  var random = (opts.randomize)\n    ? (Math.random() + 1)\n    : 1;\n\n  var timeout = Math.round(random * Math.max(opts.minTimeout, 1) * Math.pow(opts.factor, attempt));\n  timeout = Math.min(timeout, opts.maxTimeout);\n\n  return timeout;\n};\n\nexports.wrap = function(obj, options, methods) {\n  if (options instanceof Array) {\n    methods = options;\n    options = null;\n  }\n\n  if (!methods) {\n    methods = [];\n    for (var key in obj) {\n      if (typeof obj[key] === 'function') {\n        methods.push(key);\n      }\n    }\n  }\n\n  for (var i = 0; i < methods.length; i++) {\n    var method   = methods[i];\n    var original = obj[method];\n\n    obj[method] = function retryWrapper(original) {\n      var op       = exports.operation(options);\n      var args     = Array.prototype.slice.call(arguments, 1);\n      var callback = args.pop();\n\n      args.push(function(err) {\n        if (op.retry(err)) {\n          return;\n        }\n        if (err) {\n          arguments[0] = op.mainError();\n        }\n        callback.apply(this, arguments);\n      });\n\n      op.attempt(function() {\n        original.apply(obj, args);\n      });\n    }.bind(obj, original);\n    obj[method].options = options;\n  }\n};\n", "module.exports = require('./lib/retry');", "// Packages\nvar retrier = require('retry');\n\nfunction retry(fn, opts) {\n  function run(resolve, reject) {\n    var options = opts || {};\n    var op;\n\n    // Default `randomize` to true\n    if (!('randomize' in options)) {\n      options.randomize = true;\n    }\n\n    op = retrier.operation(options);\n\n    // We allow the user to abort retrying\n    // this makes sense in the cases where\n    // knowledge is obtained that retrying\n    // would be futile (e.g.: auth errors)\n\n    function bail(err) {\n      reject(err || new Error('Aborted'));\n    }\n\n    function onError(err, num) {\n      if (err.bail) {\n        bail(err);\n        return;\n      }\n\n      if (!op.retry(err)) {\n        reject(op.mainError());\n      } else if (options.onRetry) {\n        options.onRetry(err, num);\n      }\n    }\n\n    function runAttempt(num) {\n      var val;\n\n      try {\n        val = fn(bail, num);\n      } catch (err) {\n        onError(err, num);\n        return;\n      }\n\n      Promise.resolve(val)\n        .then(resolve)\n        .catch(function catchIt(err) {\n          onError(err, num);\n        });\n    }\n\n    op.attempt(runAttempt);\n  }\n\n  return new Promise(run);\n}\n\nmodule.exports = retry;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,aAAS,eAAe,UAAU,SAAS;AAEzC,UAAI,OAAO,YAAY,WAAW;AAChC,kBAAU,EAAE,SAAS,QAAQ;AAAA,MAC/B;AAEA,WAAK,oBAAoB,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AAC5D,WAAK,YAAY;AACjB,WAAK,WAAW,WAAW,CAAC;AAC5B,WAAK,gBAAgB,WAAW,QAAQ,gBAAgB;AACxD,WAAK,MAAM;AACX,WAAK,UAAU,CAAC;AAChB,WAAK,YAAY;AACjB,WAAK,oBAAoB;AACzB,WAAK,sBAAsB;AAC3B,WAAK,WAAW;AAChB,WAAK,kBAAkB;AACvB,WAAK,SAAS;AAEd,UAAI,KAAK,SAAS,SAAS;AACzB,aAAK,kBAAkB,KAAK,UAAU,MAAM,CAAC;AAAA,MAC/C;AAAA,IACF;AACA,WAAO,UAAU;AAEjB,mBAAe,UAAU,QAAQ,WAAW;AAC1C,WAAK,YAAY;AACjB,WAAK,YAAY,KAAK,kBAAkB,MAAM,CAAC;AAAA,IACjD;AAEA,mBAAe,UAAU,OAAO,WAAW;AACzC,UAAI,KAAK,UAAU;AACjB,qBAAa,KAAK,QAAQ;AAAA,MAC5B;AACA,UAAI,KAAK,QAAQ;AACf,qBAAa,KAAK,MAAM;AAAA,MAC1B;AAEA,WAAK,YAAkB,CAAC;AACxB,WAAK,kBAAkB;AAAA,IACzB;AAEA,mBAAe,UAAU,QAAQ,SAAS,KAAK;AAC7C,UAAI,KAAK,UAAU;AACjB,qBAAa,KAAK,QAAQ;AAAA,MAC5B;AAEA,UAAI,CAAC,KAAK;AACR,eAAO;AAAA,MACT;AACA,UAAI,eAAc,oBAAI,KAAK,GAAE,QAAQ;AACrC,UAAI,OAAO,cAAc,KAAK,mBAAmB,KAAK,eAAe;AACnE,aAAK,QAAQ,KAAK,GAAG;AACrB,aAAK,QAAQ,QAAQ,IAAI,MAAM,iCAAiC,CAAC;AACjE,eAAO;AAAA,MACT;AAEA,WAAK,QAAQ,KAAK,GAAG;AAErB,UAAI,UAAU,KAAK,UAAU,MAAM;AACnC,UAAI,YAAY,QAAW;AACzB,YAAI,KAAK,iBAAiB;AAExB,eAAK,QAAQ,OAAO,GAAG,KAAK,QAAQ,SAAS,CAAC;AAC9C,oBAAU,KAAK,gBAAgB,MAAM,EAAE;AAAA,QACzC,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,OAAO;AACX,WAAK,SAAS,WAAW,WAAW;AAClC,aAAK;AAEL,YAAI,KAAK,qBAAqB;AAC5B,eAAK,WAAW,WAAW,WAAW;AACpC,iBAAK,oBAAoB,KAAK,SAAS;AAAA,UACzC,GAAG,KAAK,iBAAiB;AAEzB,cAAI,KAAK,SAAS,OAAO;AACrB,iBAAK,SAAS,MAAM;AAAA,UACxB;AAAA,QACF;AAEA,aAAK,IAAI,KAAK,SAAS;AAAA,MACzB,GAAG,OAAO;AAEV,UAAI,KAAK,SAAS,OAAO;AACrB,aAAK,OAAO,MAAM;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAEA,mBAAe,UAAU,UAAU,SAAS,IAAI,YAAY;AAC1D,WAAK,MAAM;AAEX,UAAI,YAAY;AACd,YAAI,WAAW,SAAS;AACtB,eAAK,oBAAoB,WAAW;AAAA,QACtC;AACA,YAAI,WAAW,IAAI;AACjB,eAAK,sBAAsB,WAAW;AAAA,QACxC;AAAA,MACF;AAEA,UAAI,OAAO;AACX,UAAI,KAAK,qBAAqB;AAC5B,aAAK,WAAW,WAAW,WAAW;AACpC,eAAK,oBAAoB;AAAA,QAC3B,GAAG,KAAK,iBAAiB;AAAA,MAC3B;AAEA,WAAK,mBAAkB,oBAAI,KAAK,GAAE,QAAQ;AAE1C,WAAK,IAAI,KAAK,SAAS;AAAA,IACzB;AAEA,mBAAe,UAAU,MAAM,SAAS,IAAI;AAC1C,cAAQ,IAAI,0CAA0C;AACtD,WAAK,QAAQ,EAAE;AAAA,IACjB;AAEA,mBAAe,UAAU,QAAQ,SAAS,IAAI;AAC5C,cAAQ,IAAI,4CAA4C;AACxD,WAAK,QAAQ,EAAE;AAAA,IACjB;AAEA,mBAAe,UAAU,QAAQ,eAAe,UAAU;AAE1D,mBAAe,UAAU,SAAS,WAAW;AAC3C,aAAO,KAAK;AAAA,IACd;AAEA,mBAAe,UAAU,WAAW,WAAW;AAC7C,aAAO,KAAK;AAAA,IACd;AAEA,mBAAe,UAAU,YAAY,WAAW;AAC9C,UAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,CAAC;AACd,UAAI,YAAY;AAChB,UAAI,iBAAiB;AAErB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,YAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,YAAI,UAAU,MAAM;AACpB,YAAI,SAAS,OAAO,OAAO,KAAK,KAAK;AAErC,eAAO,OAAO,IAAI;AAElB,YAAI,SAAS,gBAAgB;AAC3B,sBAAY;AACZ,2BAAiB;AAAA,QACnB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjKA;AAAA;AAAA,QAAI,iBAAiB;AAErB,YAAQ,YAAY,SAAS,SAAS;AACpC,UAAI,WAAW,QAAQ,SAAS,OAAO;AACvC,aAAO,IAAI,eAAe,UAAU;AAAA,QAChC,SAAS,YAAY,QAAQ,WAAW,QAAQ,YAAY;AAAA,QAC5D,OAAO,WAAW,QAAQ;AAAA,QAC1B,cAAc,WAAW,QAAQ;AAAA,MACrC,CAAC;AAAA,IACH;AAEA,YAAQ,WAAW,SAAS,SAAS;AACnC,UAAI,mBAAmB,OAAO;AAC5B,eAAO,CAAC,EAAE,OAAO,OAAO;AAAA,MAC1B;AAEA,UAAI,OAAO;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY,IAAI;AAAA,QAChB,YAAY;AAAA,QACZ,WAAW;AAAA,MACb;AACA,eAAS,OAAO,SAAS;AACvB,aAAK,GAAG,IAAI,QAAQ,GAAG;AAAA,MACzB;AAEA,UAAI,KAAK,aAAa,KAAK,YAAY;AACrC,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,WAAW,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,KAAK;AACrC,iBAAS,KAAK,KAAK,cAAc,GAAG,IAAI,CAAC;AAAA,MAC3C;AAEA,UAAI,WAAW,QAAQ,WAAW,CAAC,SAAS,QAAQ;AAClD,iBAAS,KAAK,KAAK,cAAc,GAAG,IAAI,CAAC;AAAA,MAC3C;AAGA,eAAS,KAAK,SAAS,GAAE,GAAG;AAC1B,eAAO,IAAI;AAAA,MACb,CAAC;AAED,aAAO;AAAA,IACT;AAEA,YAAQ,gBAAgB,SAAS,SAAS,MAAM;AAC9C,UAAI,SAAU,KAAK,YACd,KAAK,OAAO,IAAI,IACjB;AAEJ,UAAI,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,KAAK,YAAY,CAAC,IAAI,KAAK,IAAI,KAAK,QAAQ,OAAO,CAAC;AAC/F,gBAAU,KAAK,IAAI,SAAS,KAAK,UAAU;AAE3C,aAAO;AAAA,IACT;AAEA,YAAQ,OAAO,SAAS,KAAK,SAAS,SAAS;AAC7C,UAAI,mBAAmB,OAAO;AAC5B,kBAAU;AACV,kBAAU;AAAA,MACZ;AAEA,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AACX,iBAAS,OAAO,KAAK;AACnB,cAAI,OAAO,IAAI,GAAG,MAAM,YAAY;AAClC,oBAAQ,KAAK,GAAG;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,SAAW,QAAQ,CAAC;AACxB,YAAI,WAAW,IAAI,MAAM;AAEzB,YAAI,MAAM,KAAI,SAAS,aAAaA,WAAU;AAC5C,cAAI,KAAW,QAAQ,UAAU,OAAO;AACxC,cAAI,OAAW,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACtD,cAAI,WAAW,KAAK,IAAI;AAExB,eAAK,KAAK,SAAS,KAAK;AACtB,gBAAI,GAAG,MAAM,GAAG,GAAG;AACjB;AAAA,YACF;AACA,gBAAI,KAAK;AACP,wBAAU,CAAC,IAAI,GAAG,UAAU;AAAA,YAC9B;AACA,qBAAS,MAAM,MAAM,SAAS;AAAA,UAChC,CAAC;AAED,aAAG,QAAQ,WAAW;AACpB,YAAAA,UAAS,MAAM,KAAK,IAAI;AAAA,UAC1B,CAAC;AAAA,QACH,GAAE,KAAK,KAAK,QAAQ;AACpB,YAAI,MAAM,EAAE,UAAU;AAAA,MACxB;AAAA,IACF;AAAA;AAAA;;;ACnGA,IAAAC,iBAAA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AACA,QAAI,UAAU;AAEd,aAAS,MAAM,IAAI,MAAM;AACvB,eAAS,IAAI,SAAS,QAAQ;AAC5B,YAAI,UAAU,QAAQ,CAAC;AACvB,YAAI;AAGJ,YAAI,EAAE,eAAe,UAAU;AAC7B,kBAAQ,YAAY;AAAA,QACtB;AAEA,aAAK,QAAQ,UAAU,OAAO;AAO9B,iBAAS,KAAK,KAAK;AACjB,iBAAO,OAAO,IAAI,MAAM,SAAS,CAAC;AAAA,QACpC;AAEA,iBAAS,QAAQ,KAAK,KAAK;AACzB,cAAI,IAAI,MAAM;AACZ,iBAAK,GAAG;AACR;AAAA,UACF;AAEA,cAAI,CAAC,GAAG,MAAM,GAAG,GAAG;AAClB,mBAAO,GAAG,UAAU,CAAC;AAAA,UACvB,WAAW,QAAQ,SAAS;AAC1B,oBAAQ,QAAQ,KAAK,GAAG;AAAA,UAC1B;AAAA,QACF;AAEA,iBAAS,WAAW,KAAK;AACvB,cAAI;AAEJ,cAAI;AACF,kBAAM,GAAG,MAAM,GAAG;AAAA,UACpB,SAAS,KAAK;AACZ,oBAAQ,KAAK,GAAG;AAChB;AAAA,UACF;AAEA,kBAAQ,QAAQ,GAAG,EAChB,KAAK,OAAO,EACZ,MAAM,SAAS,QAAQ,KAAK;AAC3B,oBAAQ,KAAK,GAAG;AAAA,UAClB,CAAC;AAAA,QACL;AAEA,WAAG,QAAQ,UAAU;AAAA,MACvB;AAEA,aAAO,IAAI,QAAQ,GAAG;AAAA,IACxB;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["original", "require_retry"]}