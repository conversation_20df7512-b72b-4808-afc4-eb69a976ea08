[{"id": "616b0262-e470-4adb-8a1d-3c5aa5df904b", "type": "agent_run_start", "timestamp": 1750233926158, "sessionId": "1750233926158-unpo1ng", "runOptions": {"input": "Review https://github.com/bytedance/UI-TARS-desktop/pull/697"}, "provider": "azure-openai", "model": "aws_sdk_claude37_sonnet"}, {"id": "0801a6be-abba-46b1-86b4-026c412f5670", "type": "user_message", "timestamp": 1750233926158, "content": "Review https://github.com/bytedance/UI-TARS-desktop/pull/697"}, {"id": "7233fb0b-5b0d-46c7-bb8f-eff2c6249cc5", "type": "assistant_message", "timestamp": 1750233931947, "content": "I'll help you review the pull request at https://github.com/bytedance/UI-TARS-desktop/pull/697. Let me navigate to the PR and analyze all the code changes to provide you with a comprehensive review.", "toolCalls": [{"id": "tooluse_zQwUlDX8RomVxK-X_EdWxw", "type": "", "function": {"name": "browser_navigate", "arguments": "{\"url\": \"https://github.com/bytedance/UI-TARS-desktop/pull/697\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233929411_1a92zk2c"}, {"id": "83f24850-979e-4233-83a9-e9688769d61e", "type": "tool_call", "timestamp": 1750233931948, "toolCallId": "tooluse_zQwUlDX8RomVxK-X_EdWxw", "name": "browser_navigate", "arguments": {"url": "https://github.com/bytedance/UI-TARS-desktop/pull/697"}, "startTime": 1750233931948, "tool": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "schema": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "7d71086f-3621-490c-8929-d81150f1bed8", "type": "tool_result", "timestamp": 1750233937397, "toolCallId": "tooluse_zQwUlDX8RomVxK-X_EdWxw", "name": "browser_navigate", "content": [{"type": "text", "text": "Navigated to https://github.com/bytedance/UI-TARS-desktop/pull/697\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): []Skip to content\n[]Navigation Menu\n[0]<a></a>\n[1]<button>Product</button>\n[2]<button>Solutions</button>\n[3]<button>Resources</button>\n[4]<button>Open Source</button>\n[5]<button>Enterprise</button>\n[6]<a>Pricing</a>\n[7]<button></button>\n[8]<a>Sign in</a>\n[9]<a>Sign up</a>\n[10]<button></button>\n[11]<a>bytedance</a>\n[]/\n[12]<a>UI-TARS-desktop</a>\n[]Public\n[13]<a>Notifications</a>\n[14]<a>Fork\n1.3k</a>\n[15]<a>Star\n14.7k</a>\n[16]<a>Code</a>\n[17]<a>Issues\n163</a>\n[18]<a>Pull requests\n12</a>\n[19]<a>Discussions</a>\n[20]<a>Actions</a>\n[21]<a>Projects</a>\n[22]<a>Security</a>\n[23]<a>Insights</a>\n[]fix(agent-tars-web-ui): resolve panel UI flicker (close: #696)\n[]#697\n[24]<details></details>\n[25]<summary>New issue</summary>\n[]Merged\n[26]<a>ulivz</a>\n[]merged 2 commits into\n[27]<a>main</a>\n[]from\n[28]<a>fix/panel-ui-flicker-696</a>\n[29]<clipboard-copy></clipboard-copy>\n[]+5\n[]−11\n[30]<a>Conversation\n1</a>\n[31]<a>Commits\n2</a>\n[32]<a>Checks\n8</a>\n[33]<a>Files changed\n2</a>\n[]Conversation\n[34]<a></a>\n[35]<img></img>\n[36]<details></details>\n[37]<summary></summary>\n[]Member\n[38]<a>ulivz</a>\n[]commented\n[39]<a></a>\n[]•\n[40]<details></details>\n[41]<summary>edited</summary>\n[]Note: This is a Pull Request submitted via Agent TARS\n[]Summary\n[]The panel UI flickering problem is solved by adjusting the motion.div of the Layout component to a normal div and optimizing the flex layout.\n[]Close\n[]:\n[42]<a>[Bug Report]: Panel UI flickers\n#696</a>\n[].\n[]Checklist\n[43]<input></input>\n[]Added or updated necessary tests (Optional).\n[44]<input></input>\n[]Updated documentation to align with changes (Optional).\n[45]<input></input>\n[]Verified no breaking changes, or prepared solutions for any occurring breaking changes (Optional).\n[46]<input></input>\n[]My change does not involve the above items.\n[]Reviewers\n[]No reviews\n[]Assignees\n[]No one assigned\n[]Labels\n[]None yet\n[]Projects\n[]None yet\n[]Milestone\n[]No milestone\n[]Development\n[]Successfully merging this pull request may close these issues."}], "elapsedMs": 5448}, {"id": "c5df2f23-71f9-43c8-9ce9-52c4bd8aff7a", "type": "assistant_message", "timestamp": 1750233941480, "content": "Let me look at the files changed in this PR to understand the changes made.", "toolCalls": [{"id": "tooluse_3YtLZxfeTVaSRS8f_2IhpA", "type": "", "function": {"name": "browser_click", "arguments": "{\"index\": 33}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233940655_so7z8w5f"}, {"id": "9c4a112f-d2f5-413f-856f-29623ebcaf0a", "type": "tool_call", "timestamp": 1750233941480, "toolCallId": "tooluse_3YtLZxfeTVaSRS8f_2IhpA", "name": "browser_click", "arguments": {"index": 33}, "startTime": 1750233941480, "tool": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "schema": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "c4d4cc05-8a07-4a25-badb-6a6274eb3920", "type": "tool_result", "timestamp": 1750233941527, "toolCallId": "tooluse_3YtLZxfeTVaSRS8f_2IhpA", "name": "browser_click", "content": [{"type": "text", "text": "Clicked element: 33"}], "elapsedMs": 47}, {"id": "fe055e58-b730-4c34-8b45-fb452dc1b5af", "type": "assistant_message", "timestamp": 1750233946406, "content": "", "toolCalls": [{"id": "tooluse_i7Iq_FBARaG2H6pd2z4a9A", "type": "", "function": {"name": "browser_get_markdown", "arguments": ""}}], "finishReason": "tool_calls", "messageId": "msg_1750233945843_27p79det"}, {"id": "802aa469-5cee-4380-8945-cfc58b4ea8d6", "type": "tool_call", "timestamp": 1750233946407, "toolCallId": "tooluse_i7Iq_FBARaG2H6pd2z4a9A", "name": "browser_get_markdown", "arguments": {}, "startTime": 1750233946407, "tool": {"name": "browser_get_markdown", "description": "[browser] Get the markdown content of the current page", "schema": {"type": "object", "properties": {}}}}, {"id": "9e322afa-b0dd-492d-b0f6-653d8f910174", "type": "tool_result", "timestamp": 1750233946500, "toolCallId": "tooluse_i7Iq_FBARaG2H6pd2z4a9A", "name": "browser_get_markdown", "content": [{"type": "text", "text": "                 fix(agent-tars-web-ui): resolve panel UI flicker (close: #696) by ul<PERSON><PERSON> · Pull Request #697 · bytedance/UI-TARS-desktop · GitHub\n\n[Skip to content](#start-of-content)  \n\n## Navigation Menu\n\nToggle navigation\n\n[](/)\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fbytedance%2FUI-TARS-desktop%2Fpull%2F697)\n\nAppearance settings  \n\n*   Product\n    \n    *   [\n        \n        GitHub Copilot\n        \n        Write better code with AI\n        \n        ](https://github.com/features/copilot)\n    *   [\n        \n        GitHub Models New\n        \n        Manage and compare prompts\n        \n        ](https://github.com/features/models)\n    *   [\n        \n        GitHub Advanced Security\n        \n        Find and fix vulnerabilities\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Actions\n        \n        Automate any workflow\n        \n        ](https://github.com/features/actions)\n    *   [\n        \n        Codespaces\n        \n        Instant dev environments\n        \n        ](https://github.com/features/codespaces)\n    \n    *   [\n        \n        Issues\n        \n        Plan and track work\n        \n        ](https://github.com/features/issues)\n    *   [\n        \n        Code Review\n        \n        Manage code changes\n        \n        ](https://github.com/features/code-review)\n    *   [\n        \n        Discussions\n        \n        Collaborate outside of code\n        \n        ](https://github.com/features/discussions)\n    *   [\n        \n        Code Search\n        \n        Find more, search less\n        \n        ](https://github.com/features/code-search)\n    \n    Explore\n    \n    *   [Why GitHub](https://github.com/why-github)\n    *   [All features](https://github.com/features)\n    *   [Documentation](https://docs.github.com)\n    *   [GitHub Skills](https://skills.github.com)\n    *   [Blog](https://github.blog)\n    \n*   Solutions\n    \n    By company size\n    \n    *   [Enterprises](https://github.com/enterprise)\n    *   [Small and medium teams](https://github.com/team)\n    *   [Startups](https://github.com/enterprise/startups)\n    *   [Nonprofits](/solutions/industry/nonprofits)\n    \n    By use case\n    \n    *   [DevSecOps](/solutions/use-case/devsecops)\n    *   [DevOps](/solutions/use-case/devops)\n    *   [CI/CD](/solutions/use-case/ci-cd)\n    *   [View all use cases](/solutions/use-case)\n    \n    By industry\n    \n    *   [Healthcare](/solutions/industry/healthcare)\n    *   [Financial services](/solutions/industry/financial-services)\n    *   [Manufacturing](/solutions/industry/manufacturing)\n    *   [Government](/solutions/industry/government)\n    *   [View all industries](/solutions/industry)\n    \n    [View all solutions](/solutions)\n    \n*   Resources\n    \n    Topics\n    \n    *   [AI](/resources/articles/ai)\n    *   [DevOps](/resources/articles/devops)\n    *   [Security](/resources/articles/security)\n    *   [Software Development](/resources/articles/software-development)\n    *   [View all](/resources/articles)\n    \n    Explore\n    \n    *   [Learning Pathways](https://resources.github.com/learn/pathways)\n    *   [Events & Webinars](https://resources.github.com)\n    *   [Ebooks & Whitepapers](https://github.com/resources/whitepapers)\n    *   [Customer Stories](https://github.com/customer-stories)\n    *   [Partners](https://partner.github.com)\n    *   [Executive Insights](https://github.com/solutions/executive-insights)\n    \n*   Open Source\n    \n    *   [\n        \n        GitHub Sponsors\n        \n        Fund open source developers\n        \n        ](/sponsors)\n    \n    *   [\n        \n        The ReadME Project\n        \n        GitHub community articles\n        \n        ](https://github.com/readme)\n    \n    Repositories\n    \n    *   [Topics](https://github.com/topics)\n    *   [Trending](https://github.com/trending)\n    *   [Collections](https://github.com/collections)\n    \n*   Enterprise\n    \n    *   [\n        \n        Enterprise platform\n        \n        AI-powered developer platform\n        \n        ](/enterprise)\n    \n    Available add-ons\n    \n    *   [\n        \n        GitHub Advanced Security\n        \n        Enterprise-grade security features\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Copilot for business\n        \n        Enterprise-grade AI features\n        \n        ](/features/copilot/copilot-business)\n    *   [\n        \n        Premium Support\n        \n        Enterprise-grade 24/7 support\n        \n        ](/premium-support)\n    \n*   [Pricing](https://github.com/pricing)\n\nSearch or jump to...\n\n# Search code, repositories, users, issues, pull requests...\n\nSearch\n\nClear\n\n[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)\n\n# Provide feedback\n\nWe read every piece of feedback, and take your input very seriously.\n\n Include my email address so I can be contacted\n\nCancel Submit feedback\n\n# Saved searches\n\n## Use saved searches to filter your results more quickly\n\nName  \n\nQuery \n\nTo see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax).\n\nCancel Create saved search\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fbytedance%2FUI-TARS-desktop%2Fpull%2F697)\n\n[Sign up](/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fvoltron%2Fpull_requests_fragments%2Fpull_request_layout&source=header-repo&source_repo=bytedance%2FUI-TARS-desktop)\n\nAppearance settings  \n\nResetting focus\n\nYou signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert\n\n[bytedance](/bytedance) / **[UI-TARS-desktop](/bytedance/UI-TARS-desktop)** Public\n\n*   [Notifications](/login?return_to=%2Fbytedance%2FUI-TARS-desktop) You must be signed in to change notification settings\n*   [Fork 1.3k](/login?return_to=%2Fbytedance%2FUI-TARS-desktop)\n*   [Star 14.7k](/login?return_to=%2Fbytedance%2FUI-TARS-desktop)\n    \n\n*   [Code](/bytedance/UI-TARS-desktop)\n*   [Issues 163](/bytedance/UI-TARS-desktop/issues)\n*   [Pull requests 12](/bytedance/UI-TARS-desktop/pulls)\n*   [Discussions](/bytedance/UI-TARS-desktop/discussions)\n*   [Actions](/bytedance/UI-TARS-desktop/actions)\n*   [Projects 0](/bytedance/UI-TARS-desktop/projects)\n*   [Security](/bytedance/UI-TARS-desktop/security)\n    \n    [](/bytedance/UI-TARS-desktop/security)\n    \n    [](/bytedance/UI-TARS-desktop/security)\n    \n    [](/bytedance/UI-TARS-desktop/security)\n    \n    [\n    \n    ### Uh oh!\n    \n    ](/bytedance/UI-TARS-desktop/security)\n    \n    [There was an error while loading.](/bytedance/UI-TARS-desktop/security) Please reload this page.\n    \n*   [Insights](/bytedance/UI-TARS-desktop/pulse)\n\nAdditional navigation options\n\n*   [Code](/bytedance/UI-TARS-desktop)\n*   [Issues](/bytedance/UI-TARS-desktop/issues)\n*   [Pull requests](/bytedance/UI-TARS-desktop/pulls)\n*   [Discussions](/bytedance/UI-TARS-desktop/discussions)\n*   [Actions](/bytedance/UI-TARS-desktop/actions)\n*   [Projects](/bytedance/UI-TARS-desktop/projects)\n*   [Security](/bytedance/UI-TARS-desktop/security)\n*   [Insights](/bytedance/UI-TARS-desktop/pulse)\n\n# fix(agent-tars-web-ui): resolve panel UI flicker (close: #696) #697\n\nNew issue\n\n**Have a question about this project?** Sign up for a free GitHub account to open an issue and contact its maintainers and the community.\n\n[Sign up for GitHub](/signup?return_to=%2Fbytedance%2FUI-TARS-desktop%2Fissues%2Fnew%2Fchoose)\n\nBy clicking “Sign up for GitHub”, you agree to our [terms of service](https://docs.github.com/terms) and [privacy statement](https://docs.github.com/privacy). We’ll occasionally send you account related emails.\n\nAlready on GitHub? [Sign in](/login?return_to=%2Fbytedance%2FUI-TARS-desktop%2Fissues%2Fnew%2Fchoose) to your account\n\nMerged\n\n[ulivz](/ulivz) merged 2 commits into [main](/bytedance/UI-TARS-desktop/tree/main \"bytedance/UI-TARS-desktop:main\") from [fix/panel-ui-flicker-696](/bytedance/UI-TARS-desktop/tree/fix/panel-ui-flicker-696 \"bytedance/UI-TARS-desktop:fix/panel-ui-flicker-696\")Jun 15, 2025\n\n+5 −11\n\n[Conversation 1](/bytedance/UI-TARS-desktop/pull/697) [Commits 2](/bytedance/UI-TARS-desktop/pull/697/commits) [Checks 8](/bytedance/UI-TARS-desktop/pull/697/checks) [Files changed 2](/bytedance/UI-TARS-desktop/pull/697/files)\n\nMerged\n\n# [fix(agent-tars-web-ui): resolve panel UI flicker (close: #696)](#top) #697\n\nShow file tree\n\nHide file tree\n\nChanges from **all commits**\n\nCommits\n\n[\n\nShow all changes\n\n2 commits\n\n](/bytedance/UI-TARS-desktop/pull/697/files)\n\nSelect commit Hold shift + click to select a range\n\n[\n\n`ae94c2c`\n\nfix(agent-tars-web-ui): resolve panel UI flicker (close: #696)\n\nulivz Jun 15, 2025\n\n](/bytedance/UI-TARS-desktop/pull/697/commits/ae94c2c1506935a888fadab086eea6cf82cc4559)[\n\n`b446fe3`\n\nchore(agent-tars-cli): fix ci\n\nulivz Jun 15, 2025\n\n](/bytedance/UI-TARS-desktop/pull/697/commits/b446fe3f805a31b261aac8b598a72734dfd5634c)\n\n**File filter**\n\n### Filter by extension\n\nFilter by extension  .ts  (1)  .tsx  (1)  All 2 file types selected\n\n* * *\n\n Viewed files\n\n[Clear filters](/bytedance/UI-TARS-desktop/pull/697/files)\n\n**Conversations**\n\nFailed to load comments. Retry\n\nLoading\n\n### Uh oh!\n\nThere was an error while loading. Please reload this page.\n\n**Jump to**\n\nJump to file\n\nFailed to load files. Retry\n\nLoading\n\n### Uh oh!\n\nThere was an error while loading. Please reload this page.\n\n##### Diff view\n\n ![Unified Diff View](https://github.githubassets.com/assets/unified-6de447b07fd7.svg)\n\n Unified\n\n ![Split Diff View](https://github.githubassets.com/assets/split-b930d4a1df45.svg)\n\n Split\n\n  Hide whitespace\n\nApply and reload\n\n Show whitespace\n\n##### Diff view\n\n ![Unified Diff View](https://github.githubassets.com/assets/unified-6de447b07fd7.svg)\n\n Unified\n\n ![Split Diff View](https://github.githubassets.com/assets/split-b930d4a1df45.svg)\n\n Split\n\n  Hide whitespace\n\nApply and reload\n\n*   multimodal\n    *   agent-tars-cli/src/utils\n        *   multimodal/agent-tars-cli/src/utils/console-interceptor.ts [console-interceptor.ts](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f)\n    *   agent-tars-web-ui/src/standalone/app/Layout\n        *   multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx [index.tsx](#diff-5bac46af12f3c8aaaa9e32af602739e5106f996ad8d8d9c112781fcb3ede2de8)\n\n## There are no files selected for viewing\n\n3 changes: 0 additions & 3 deletions 3 [multimodal/agent-tars-cli/src/utils/console-interceptor.ts](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f \"multimodal/agent-tars-cli/src/utils/console-interceptor.ts\")\n\n Show comments\n\n[View file](/bytedance/UI-TARS-desktop/blob/b446fe3f805a31b261aac8b598a72734dfd5634c/multimodal/agent-tars-cli/src/utils/console-interceptor.ts) Edit file Delete file\n\n[Open in desktop](https://desktop.github.com)\n\n| Original file line number | Diff line number | Diff line change |\n| --- | --- | --- |\n| [](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f)Expand Up | @@ -44,9 +44,6 @@ export class ConsoleInterceptor { |\n|  |  | debug: typeof console.debug; |\n|  |  | }; |\n|  |  |   \n |\n|  |  | private originalStdout: typeof process.stdout; |\n|  |  | private originalStderr: typeof process.stderr; |\n|  |  |   \n |\n|  |  | private buffer: string\\[\\] \\= \\[\\]; |\n|  |  | private options: ConsoleInterceptorOptions; |\n|  |  |   \n |\n| [](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f)Expand Down |  |\n\n13 changes: 5 additions & 8 deletions 13 [multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx](#diff-5bac46af12f3c8aaaa9e32af602739e5106f996ad8d8d9c112781fcb3ede2de8 \"multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx\")\n\n Show comments\n\n[View file](/bytedance/UI-TARS-desktop/blob/b446fe3f805a31b261aac8b598a72734dfd5634c/multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx) Edit file Delete file\n\n[Open in desktop](https://desktop.github.com)\n\n| Original file line number | Diff line number | Diff line change |\n| --- | --- | --- |\n| [](#diff-5bac46af12f3c8aaaa9e32af602739e5106f996ad8d8d9c112781fcb3ede2de8)Expand Up | @@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode } |\n|  |  | {/\\* Panels container - apply flex-1 to take remaining vertical space \\*/} |\n|  |  | <div className\\=\"flex gap-3 flex-1 min-h-0\"\\> |\n|  |  | {/\\* Chat panel - adjust width based on replay mode \\*/} |\n|  |  | <motion.div |\n|  |  | layout |\n|  |  | className\\={isReplayMode ? 'w-\\[50%\\] flex flex-col' : 'w-\\[50%\\] flex flex-col'} |\n|  |  | \\> |\n|  |  | <div className\\=\"flex-1 flex flex-col overflow-hidden\"\\> |\n|  |  | <Shell className\\=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-\\[#E5E6EC\\] dark:border-none bg-\\[#FFFFFFE5\\] dark:shadow-gray-950/5\"\\> |\n|  |  | <ChatPanel /> |\n|  |  | </Shell\\> |\n|  |  | </motion.div\\> |\n|  |  | </div\\> |\n|  |  |   \n |\n|  |  | {/\\* Workspace panel \\*/} |\n|  |  | <motion.div layout className\\=\"w-\\[50%\\] flex flex-col\"\\> |\n|  |  | <div className\\=\"flex-1 flex flex-col overflow-hidden\"\\> |\n|  |  | <Shell className\\=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-\\[#E5E6EC\\] dark:border-none bg-\\[#FFFFFFE5\\] dark:shadow-gray-950/5\"\\> |\n|  |  | <WorkspacePanel /> |\n|  |  | </Shell\\> |\n|  |  | </motion.div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | ); |\n|  |  | }; |\n|  |  | }; |\n\nToggle all file notes Toggle all file annotations\n\nAdd this suggestion to a batch that can be applied as a single commit. This suggestion is invalid because no changes were made to the code. Suggestions cannot be applied while the pull request is closed. Suggestions cannot be applied while viewing a subset of changes. Only one suggestion per line can be applied in a batch. Add this suggestion to a batch that can be applied as a single commit. Applying suggestions on deleted lines is not supported. You must change the existing code in this line in order to create a valid suggestion. Outdated suggestions cannot be applied. This suggestion has been applied or marked resolved. Suggestions cannot be applied from pending reviews. Suggestions cannot be applied on multi-line comments. Suggestions cannot be applied while the pull request is queued to merge. Suggestion cannot be applied right now. Please check back later.\n\n## Footer\n\n[](https://github.com)© 2025 GitHub, Inc.\n\n### Footer navigation\n\n*   [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)\n*   [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)\n*   [Security](https://github.com/security)\n*   [Status](https://www.githubstatus.com/)\n*   [Docs](https://docs.github.com/)\n*   [Contact](https://support.github.com?tags=dotcom-footer)\n*   Manage cookies\n*   Do not share my personal information\n\nYou can’t perform that action at this time."}], "elapsedMs": 87}, {"id": "0e551a03-3a4b-46e2-ac80-d1cd6e112c5f", "type": "assistant_message", "timestamp": 1750233953087, "content": "Let me view the actual file changes in more detail to better understand what's been modified.", "toolCalls": [{"id": "tooluse_Ibo8CcukRTSsdsXJ0CMSOQ", "type": "", "function": {"name": "browser_navigate", "arguments": "{\"url\": \"https://github.com/bytedance/UI-TARS-desktop/pull/697/files\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233950365_ms3rne00"}, {"id": "0b8eea0c-3b1e-4d34-ab80-0f568c108910", "type": "tool_call", "timestamp": 1750233953087, "toolCallId": "tooluse_Ibo8CcukRTSsdsXJ0CMSOQ", "name": "browser_navigate", "arguments": {"url": "https://github.com/bytedance/UI-TARS-desktop/pull/697/files"}, "startTime": 1750233953087, "tool": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "schema": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "81c3bb6e-f910-4bcd-a144-c9b7aa8d6f51", "type": "tool_result", "timestamp": 1750233954813, "toolCallId": "tooluse_Ibo8CcukRTSsdsXJ0CMSOQ", "name": "browser_navigate", "content": [{"type": "text", "text": "Navigated to https://github.com/bytedance/UI-TARS-desktop/pull/697/files\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): []Skip to content\n[]Navigation Menu\n[0]<a></a>\n[1]<button>Product</button>\n[2]<button>Solutions</button>\n[3]<button>Resources</button>\n[4]<button>Open Source</button>\n[5]<button>Enterprise</button>\n[6]<a>Pricing</a>\n[7]<button></button>\n[8]<a>Sign in</a>\n[9]<a>Sign up</a>\n[10]<button></button>\n[11]<a>bytedance</a>\n[]/\n[12]<a>UI-TARS-desktop</a>\n[]Public\n[13]<a>Notifications</a>\n[14]<a>Fork\n1.3k</a>\n[15]<a>Star\n14.7k</a>\n[16]<a>Code</a>\n[17]<a>Issues\n163</a>\n[18]<a>Pull requests\n12</a>\n[19]<a>Discussions</a>\n[20]<a>Actions</a>\n[21]<a>Projects</a>\n[22]<a>Security</a>\n[23]<a>Insights</a>\n[]fix(agent-tars-web-ui): resolve panel UI flicker (close: #696)\n[]#697\n[24]<details></details>\n[25]<summary>New issue</summary>\n[]Merged\n[26]<a>ulivz</a>\n[]merged 2 commits into\n[27]<a>main</a>\n[]from\n[28]<a>fix/panel-ui-flicker-696</a>\n[29]<clipboard-copy></clipboard-copy>\n[30]<a>Conversation\n1</a>\n[31]<a>Commits\n2</a>\n[32]<a>Checks\n8</a>\n[33]<a>Files changed</a>\n[34]<button></button>\n[35]<details></details>\n[36]<summary>Changes from\nall commits</summary>\n[37]<details></details>\n[38]<summary>File filter</summary>\n[39]<details></details>\n[40]<summary>Conversations</summary>\n[41]<details></details>\n[42]<summary></summary>\n[43]<input></input>\n[44]<ul></ul>\n[45]<li></li>\n[46]<button>multimodal</button>\n[47]<li></li>\n[48]<button>agent-tars-cli/src/utils</button>\n[49]<li></li>\n[50]<a>console-interceptor.ts</a>\n[51]<li></li>\n[52]<button>agent-tars-web-ui/src/standalone/app/Layout</button>\n[53]<li></li>\n[54]<a>index.tsx</a>\n[55]<button></button>\n[56]<button></button>\n[]3 changes: 0 additions & 3 deletions\n[]3\n[57]<a>multimodal/agent-tars-cli/src/utils/console-interceptor.ts</a>\n[58]<clipboard-copy></clipboard-copy>\n[59]<details></details>\n[60]<summary></summary>\n[]Original file line number\n[]Diff line number\n[]Diff line change\n[61]<a></a>\n[]@@ -44,9 +44,6 @@ export class ConsoleInterceptor {\n[]debug: typeof console.debug;\n[]};\n[]private originalStdout: typeof process.stdout;\n[]private originalStderr: typeof process.stderr;\n[]private buffer: string[] = [];\n[]private options: ConsoleInterceptorOptions;\n[62]<a></a>\n[63]<button></button>\n[64]<button></button>\n[]13 changes: 5 additions & 8 deletions\n[]13\n[65]<a>multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx</a>\n[66]<clipboard-copy></clipboard-copy>\n[67]<details></details>\n[68]<summary></summary>\n[]Original file line number\n[]Diff line number\n[]Diff line change\n[69]<a></a>\n[]@@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode }\n[]{/* Panels container - apply flex-1 to take remaining vertical space */}\n[]<div className=\"flex gap-3 flex-1 min-h-0\">"}], "elapsedMs": 1721}, {"id": "6631aac7-bcad-4da4-aeb7-99ad4a074f37", "type": "assistant_message", "timestamp": 1750233962774, "content": "Let me get a better view of the changes in the Layout component, which seems to be the main file changed for fixing the UI flickering issue.", "toolCalls": [{"id": "tooluse_0NXrSQdWQ1itFMD9k2NiRg", "type": "", "function": {"name": "browser_screenshot", "arguments": "{\"selector\": \"@@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode }\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233959436_137d8m86"}, {"id": "3f58bf86-a12d-4790-954f-5201a294a5c8", "type": "tool_call", "timestamp": 1750233962774, "toolCallId": "tooluse_0NXrSQdWQ1itFMD9k2NiRg", "name": "browser_screenshot", "arguments": {"selector": "@@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode }"}, "startTime": 1750233962774, "tool": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific element", "schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Name for the screenshot"}, "selector": {"type": "string", "description": "CSS selector for element to screenshot"}, "index": {"type": "number", "description": "index of the element to screenshot"}, "width": {"type": "number", "description": "Width in pixels (default: viewport width)"}, "height": {"type": "number", "description": "Height in pixels (default: viewport height)"}, "fullPage": {"type": "boolean", "description": "Full page screenshot (default: false)"}, "highlight": {"type": "boolean", "default": false, "description": "Highlight the element"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "08f3fab7-0a3a-4468-82f1-a65bc59d6ce5", "type": "tool_result", "timestamp": 1750233962811, "toolCallId": "tooluse_0NXrSQdWQ1itFMD9k2NiRg", "name": "browser_screenshot", "content": [{"type": "text", "text": "SyntaxError: Failed to execute 'querySelector' on 'Document': '@@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode }' is not a valid selector."}], "elapsedMs": 37}, {"id": "04557ba6-10c7-4f75-af83-1901c374fcc4", "type": "assistant_message", "timestamp": 1750233967909, "content": "Let me try to view the full file changes for the Layout component:", "toolCalls": [{"id": "tooluse_rWfXO2ZYTfSxO3P1DbP9TA", "type": "", "function": {"name": "browser_click", "arguments": "{\"index\": 65}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233966147_1mfbqkus"}, {"id": "f2d8acb1-dc80-4ec2-b06e-d043e3bf3b47", "type": "tool_call", "timestamp": 1750233967909, "toolCallId": "tooluse_rWfXO2ZYTfSxO3P1DbP9TA", "name": "browser_click", "arguments": {"index": 65}, "startTime": 1750233967909, "tool": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "schema": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "daf64ab1-8dcf-4d1b-84ae-dfcb714ebb99", "type": "tool_result", "timestamp": 1750233967977, "toolCallId": "tooluse_rWfXO2ZYTfSxO3P1DbP9TA", "name": "browser_click", "content": [{"type": "text", "text": "Clicked element: 65"}], "elapsedMs": 67}, {"id": "d2870fe2-5ad6-4c35-a81c-d97dda5b8f7e", "type": "assistant_message", "timestamp": 1750233972480, "content": "", "toolCalls": [{"id": "tooluse_lNUnPRgtS1ubdDpyvDW_nQ", "type": "", "function": {"name": "browser_get_markdown", "arguments": ""}}], "finishReason": "tool_calls", "messageId": "msg_1750233972470_2t9583cd"}, {"id": "346573c6-0ea5-41e3-8746-d543850d671a", "type": "tool_call", "timestamp": 1750233972481, "toolCallId": "tooluse_lNUnPRgtS1ubdDpyvDW_nQ", "name": "browser_get_markdown", "arguments": {}, "startTime": 1750233972481, "tool": {"name": "browser_get_markdown", "description": "[browser] Get the markdown content of the current page", "schema": {"type": "object", "properties": {}}}}, {"id": "0fef147e-3856-4f67-a29e-1b0e6ff20209", "type": "tool_result", "timestamp": 1750233972545, "toolCallId": "tooluse_lNUnPRgtS1ubdDpyvDW_nQ", "name": "browser_get_markdown", "content": [{"type": "text", "text": "                    fix(agent-tars-web-ui): resolve panel UI flicker (close: #696) by ul<PERSON><PERSON> · Pull Request #697 · bytedance/UI-TARS-desktop · GitHub                                              \n\n[Skip to content](#start-of-content)  \n\n## Navigation Menu\n\nToggle navigation\n\n[](/)\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fbytedance%2FUI-TARS-desktop%2Fpull%2F697%2Ffiles)\n\nAppearance settings  \n\n*   Product\n    \n    *   [\n        \n        GitHub Copilot\n        \n        Write better code with AI\n        \n        ](https://github.com/features/copilot)\n    *   [\n        \n        GitHub Models New\n        \n        Manage and compare prompts\n        \n        ](https://github.com/features/models)\n    *   [\n        \n        GitHub Advanced Security\n        \n        Find and fix vulnerabilities\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Actions\n        \n        Automate any workflow\n        \n        ](https://github.com/features/actions)\n    *   [\n        \n        Codespaces\n        \n        Instant dev environments\n        \n        ](https://github.com/features/codespaces)\n    \n    *   [\n        \n        Issues\n        \n        Plan and track work\n        \n        ](https://github.com/features/issues)\n    *   [\n        \n        Code Review\n        \n        Manage code changes\n        \n        ](https://github.com/features/code-review)\n    *   [\n        \n        Discussions\n        \n        Collaborate outside of code\n        \n        ](https://github.com/features/discussions)\n    *   [\n        \n        Code Search\n        \n        Find more, search less\n        \n        ](https://github.com/features/code-search)\n    \n    Explore\n    \n    *   [Why GitHub](https://github.com/why-github)\n    *   [All features](https://github.com/features)\n    *   [Documentation](https://docs.github.com)\n    *   [GitHub Skills](https://skills.github.com)\n    *   [Blog](https://github.blog)\n    \n*   Solutions\n    \n    By company size\n    \n    *   [Enterprises](https://github.com/enterprise)\n    *   [Small and medium teams](https://github.com/team)\n    *   [Startups](https://github.com/enterprise/startups)\n    *   [Nonprofits](/solutions/industry/nonprofits)\n    \n    By use case\n    \n    *   [DevSecOps](/solutions/use-case/devsecops)\n    *   [DevOps](/solutions/use-case/devops)\n    *   [CI/CD](/solutions/use-case/ci-cd)\n    *   [View all use cases](/solutions/use-case)\n    \n    By industry\n    \n    *   [Healthcare](/solutions/industry/healthcare)\n    *   [Financial services](/solutions/industry/financial-services)\n    *   [Manufacturing](/solutions/industry/manufacturing)\n    *   [Government](/solutions/industry/government)\n    *   [View all industries](/solutions/industry)\n    \n    [View all solutions](/solutions)\n    \n*   Resources\n    \n    Topics\n    \n    *   [AI](/resources/articles/ai)\n    *   [DevOps](/resources/articles/devops)\n    *   [Security](/resources/articles/security)\n    *   [Software Development](/resources/articles/software-development)\n    *   [View all](/resources/articles)\n    \n    Explore\n    \n    *   [Learning Pathways](https://resources.github.com/learn/pathways)\n    *   [Events & Webinars](https://resources.github.com)\n    *   [Ebooks & Whitepapers](https://github.com/resources/whitepapers)\n    *   [Customer Stories](https://github.com/customer-stories)\n    *   [Partners](https://partner.github.com)\n    *   [Executive Insights](https://github.com/solutions/executive-insights)\n    \n*   Open Source\n    \n    *   [\n        \n        GitHub Sponsors\n        \n        Fund open source developers\n        \n        ](/sponsors)\n    \n    *   [\n        \n        The ReadME Project\n        \n        GitHub community articles\n        \n        ](https://github.com/readme)\n    \n    Repositories\n    \n    *   [Topics](https://github.com/topics)\n    *   [Trending](https://github.com/trending)\n    *   [Collections](https://github.com/collections)\n    \n*   Enterprise\n    \n    *   [\n        \n        Enterprise platform\n        \n        AI-powered developer platform\n        \n        ](/enterprise)\n    \n    Available add-ons\n    \n    *   [\n        \n        GitHub Advanced Security\n        \n        Enterprise-grade security features\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Copilot for business\n        \n        Enterprise-grade AI features\n        \n        ](/features/copilot/copilot-business)\n    *   [\n        \n        Premium Support\n        \n        Enterprise-grade 24/7 support\n        \n        ](/premium-support)\n    \n*   [Pricing](https://github.com/pricing)\n\nSearch or jump to...\n\n# Search code, repositories, users, issues, pull requests...\n\nSearch\n\nClear\n\n[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)\n\n# Provide feedback\n\nWe read every piece of feedback, and take your input very seriously.\n\n Include my email address so I can be contacted\n\nCancel Submit feedback\n\n# Saved searches\n\n## Use saved searches to filter your results more quickly\n\nName  \n\nQuery \n\nTo see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax).\n\nCancel Create saved search\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fbytedance%2FUI-TARS-desktop%2Fpull%2F697%2Ffiles)\n\n[Sign up](/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fpull_requests%2Fshow%2Ffiles&source=header-repo&source_repo=bytedance%2FUI-TARS-desktop)\n\nAppearance settings  \n\nResetting focus\n\nYou signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert\n\n[bytedance](/bytedance) / **[UI-TARS-desktop](/bytedance/UI-TARS-desktop)** Public\n\n*   [Notifications](/login?return_to=%2Fbytedance%2FUI-TARS-desktop) You must be signed in to change notification settings\n*   [Fork 1.3k](/login?return_to=%2Fbytedance%2FUI-TARS-desktop)\n*   [Star 14.7k](/login?return_to=%2Fbytedance%2FUI-TARS-desktop)\n    \n\n*   [Code](/bytedance/UI-TARS-desktop)\n*   [Issues 163](/bytedance/UI-TARS-desktop/issues)\n*   [Pull requests 12](/bytedance/UI-TARS-desktop/pulls)\n*   [Discussions](/bytedance/UI-TARS-desktop/discussions)\n*   [Actions](/bytedance/UI-TARS-desktop/actions)\n*   [Projects 0](/bytedance/UI-TARS-desktop/projects)\n*   [Security](/bytedance/UI-TARS-desktop/security)\n    \n    [](/bytedance/UI-TARS-desktop/security)\n    \n    [](/bytedance/UI-TARS-desktop/security)\n    \n    [](/bytedance/UI-TARS-desktop/security)\n    \n    [\n    \n    ### Uh oh!\n    \n    ](/bytedance/UI-TARS-desktop/security)\n    \n    [There was an error while loading.](/bytedance/UI-TARS-desktop/security) Please reload this page.\n    \n*   [Insights](/bytedance/UI-TARS-desktop/pulse)\n\nAdditional navigation options\n\n*   [Code](/bytedance/UI-TARS-desktop)\n*   [Issues](/bytedance/UI-TARS-desktop/issues)\n*   [Pull requests](/bytedance/UI-TARS-desktop/pulls)\n*   [Discussions](/bytedance/UI-TARS-desktop/discussions)\n*   [Actions](/bytedance/UI-TARS-desktop/actions)\n*   [Projects](/bytedance/UI-TARS-desktop/projects)\n*   [Security](/bytedance/UI-TARS-desktop/security)\n*   [Insights](/bytedance/UI-TARS-desktop/pulse)\n\n# fix(agent-tars-web-ui): resolve panel UI flicker (close: #696) #697\n\nNew issue\n\n**Have a question about this project?** Sign up for a free GitHub account to open an issue and contact its maintainers and the community.\n\n[Sign up for GitHub](/signup?return_to=%2Fbytedance%2FUI-TARS-desktop%2Fissues%2Fnew%2Fchoose)\n\nBy clicking “Sign up for GitHub”, you agree to our [terms of service](https://docs.github.com/terms) and [privacy statement](https://docs.github.com/privacy). We’ll occasionally send you account related emails.\n\nAlready on GitHub? [Sign in](/login?return_to=%2Fbytedance%2FUI-TARS-desktop%2Fissues%2Fnew%2Fchoose) to your account\n\nMerged\n\n[ulivz](/ulivz) merged 2 commits into [main](/bytedance/UI-TARS-desktop/tree/main \"bytedance/UI-TARS-desktop:main\") from [fix/panel-ui-flicker-696](/bytedance/UI-TARS-desktop/tree/fix/panel-ui-flicker-696 \"bytedance/UI-TARS-desktop:fix/panel-ui-flicker-696\")Jun 15, 2025\n\n+5 −11\n\n[Conversation 1](/bytedance/UI-TARS-desktop/pull/697) [Commits 2](/bytedance/UI-TARS-desktop/pull/697/commits) [Checks 8](/bytedance/UI-TARS-desktop/pull/697/checks) [Files changed 2](/bytedance/UI-TARS-desktop/pull/697/files)\n\nMerged\n\n# [fix(agent-tars-web-ui): resolve panel UI flicker (close: #696)](#top) #697\n\nShow file tree\n\nHide file tree\n\nChanges from **all commits**\n\nCommits\n\n[\n\nShow all changes\n\n2 commits\n\n](/bytedance/UI-TARS-desktop/pull/697/files)\n\nSelect commit Hold shift + click to select a range\n\n[\n\n`ae94c2c`\n\nfix(agent-tars-web-ui): resolve panel UI flicker (close: #696)\n\nulivz Jun 15, 2025\n\n](/bytedance/UI-TARS-desktop/pull/697/commits/ae94c2c1506935a888fadab086eea6cf82cc4559)[\n\n`b446fe3`\n\nchore(agent-tars-cli): fix ci\n\nulivz Jun 15, 2025\n\n](/bytedance/UI-TARS-desktop/pull/697/commits/b446fe3f805a31b261aac8b598a72734dfd5634c)\n\n**File filter**\n\n### Filter by extension\n\nFilter by extension  .ts  (1)  .tsx  (1)  All 2 file types selected\n\n* * *\n\n Viewed files\n\n[Clear filters](/bytedance/UI-TARS-desktop/pull/697/files)\n\n**Conversations**\n\nFailed to load comments. Retry\n\nLoading\n\n### Uh oh!\n\nThere was an error while loading. Please reload this page.\n\n**Jump to**\n\nJump to file\n\nFailed to load files. Retry\n\nLoading\n\n### Uh oh!\n\nThere was an error while loading. Please reload this page.\n\n##### Diff view\n\n ![Unified Diff View](https://github.githubassets.com/assets/unified-6de447b07fd7.svg)\n\n Unified\n\n ![Split Diff View](https://github.githubassets.com/assets/split-b930d4a1df45.svg)\n\n Split\n\n  Hide whitespace\n\nApply and reload\n\n Show whitespace\n\n##### Diff view\n\n ![Unified Diff View](https://github.githubassets.com/assets/unified-6de447b07fd7.svg)\n\n Unified\n\n ![Split Diff View](https://github.githubassets.com/assets/split-b930d4a1df45.svg)\n\n Split\n\n  Hide whitespace\n\nApply and reload\n\n*   multimodal\n    *   agent-tars-cli/src/utils\n        *   multimodal/agent-tars-cli/src/utils/console-interceptor.ts [console-interceptor.ts](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f)\n    *   agent-tars-web-ui/src/standalone/app/Layout\n        *   multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx [index.tsx](#diff-5bac46af12f3c8aaaa9e32af602739e5106f996ad8d8d9c112781fcb3ede2de8)\n\n## There are no files selected for viewing\n\n3 changes: 0 additions & 3 deletions 3 [multimodal/agent-tars-cli/src/utils/console-interceptor.ts](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f \"multimodal/agent-tars-cli/src/utils/console-interceptor.ts\")\n\n Show comments\n\n[View file](/bytedance/UI-TARS-desktop/blob/b446fe3f805a31b261aac8b598a72734dfd5634c/multimodal/agent-tars-cli/src/utils/console-interceptor.ts) Edit file Delete file\n\n[Open in desktop](https://desktop.github.com)\n\n| Original file line number | Diff line number | Diff line change |\n| --- | --- | --- |\n| [](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f)Expand Up | @@ -44,9 +44,6 @@ export class ConsoleInterceptor { |\n|  |  | debug: typeof console.debug; |\n|  |  | }; |\n|  |  |   \n |\n|  |  | private originalStdout: typeof process.stdout; |\n|  |  | private originalStderr: typeof process.stderr; |\n|  |  |   \n |\n|  |  | private buffer: string\\[\\] \\= \\[\\]; |\n|  |  | private options: ConsoleInterceptorOptions; |\n|  |  |   \n |\n| [](#diff-794010bc69c742475112828d73b212183ceefd510ef3d24461a8545af0c7dd5f)Expand Down |  |\n\n13 changes: 5 additions & 8 deletions 13 [multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx](#diff-5bac46af12f3c8aaaa9e32af602739e5106f996ad8d8d9c112781fcb3ede2de8 \"multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx\")\n\n Show comments\n\n[View file](/bytedance/UI-TARS-desktop/blob/b446fe3f805a31b261aac8b598a72734dfd5634c/multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx) Edit file Delete file\n\n[Open in desktop](https://desktop.github.com)\n\n| Original file line number | Diff line number | Diff line change |\n| --- | --- | --- |\n| [](#diff-5bac46af12f3c8aaaa9e32af602739e5106f996ad8d8d9c112781fcb3ede2de8)Expand Up | @@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode } |\n|  |  | {/\\* Panels container - apply flex-1 to take remaining vertical space \\*/} |\n|  |  | <div className\\=\"flex gap-3 flex-1 min-h-0\"\\> |\n|  |  | {/\\* Chat panel - adjust width based on replay mode \\*/} |\n|  |  | <motion.div |\n|  |  | layout |\n|  |  | className\\={isReplayMode ? 'w-\\[50%\\] flex flex-col' : 'w-\\[50%\\] flex flex-col'} |\n|  |  | \\> |\n|  |  | <div className\\=\"flex-1 flex flex-col overflow-hidden\"\\> |\n|  |  | <Shell className\\=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-\\[#E5E6EC\\] dark:border-none bg-\\[#FFFFFFE5\\] dark:shadow-gray-950/5\"\\> |\n|  |  | <ChatPanel /> |\n|  |  | </Shell\\> |\n|  |  | </motion.div\\> |\n|  |  | </div\\> |\n|  |  |   \n |\n|  |  | {/\\* Workspace panel \\*/} |\n|  |  | <motion.div layout className\\=\"w-\\[50%\\] flex flex-col\"\\> |\n|  |  | <div className\\=\"flex-1 flex flex-col overflow-hidden\"\\> |\n|  |  | <Shell className\\=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-\\[#E5E6EC\\] dark:border-none bg-\\[#FFFFFFE5\\] dark:shadow-gray-950/5\"\\> |\n|  |  | <WorkspacePanel /> |\n|  |  | </Shell\\> |\n|  |  | </motion.div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | </div\\> |\n|  |  | ); |\n|  |  | }; |\n|  |  | }; |\n\nToggle all file notes Toggle all file annotations\n\nAdd this suggestion to a batch that can be applied as a single commit. This suggestion is invalid because no changes were made to the code. Suggestions cannot be applied while the pull request is closed. Suggestions cannot be applied while viewing a subset of changes. Only one suggestion per line can be applied in a batch. Add this suggestion to a batch that can be applied as a single commit. Applying suggestions on deleted lines is not supported. You must change the existing code in this line in order to create a valid suggestion. Outdated suggestions cannot be applied. This suggestion has been applied or marked resolved. Suggestions cannot be applied from pending reviews. Suggestions cannot be applied on multi-line comments. Suggestions cannot be applied while the pull request is queued to merge. Suggestion cannot be applied right now. Please check back later.\n\n## Footer\n\n[](https://github.com)© 2025 GitHub, Inc.\n\n### Footer navigation\n\n*   [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)\n*   [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)\n*   [Security](https://github.com/security)\n*   [Status](https://www.githubstatus.com/)\n*   [Docs](https://docs.github.com/)\n*   [Contact](https://support.github.com?tags=dotcom-footer)\n*   Manage cookies\n*   Do not share my personal information\n\nYou can’t perform that action at this time."}], "elapsedMs": 62}, {"id": "f4fa89f2-1ccf-483a-8b1f-b3647a8a885f", "type": "assistant_message", "timestamp": 1750233980451, "content": "Let me check the specific commit that fixed the UI flickering issue:", "toolCalls": [{"id": "tooluse_6hNqVdFWT_y0mbZ0QmtyFQ", "type": "", "function": {"name": "browser_navigate", "arguments": "{\"url\": \"https://github.com/bytedance/UI-TARS-desktop/pull/697/commits/ae94c2c1506935a888fadab086eea6cf82cc4559\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233977128_kp3atxpp"}, {"id": "3d023f9b-efda-435b-9b1c-0afb88be4353", "type": "tool_call", "timestamp": 1750233980452, "toolCallId": "tooluse_6hNqVdFWT_y0mbZ0QmtyFQ", "name": "browser_navigate", "arguments": {"url": "https://github.com/bytedance/UI-TARS-desktop/pull/697/commits/ae94c2c1506935a888fadab086eea6cf82cc4559"}, "startTime": 1750233980452, "tool": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "schema": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "cb86ba31-1155-41ac-9d66-4b5d9b84202d", "type": "tool_result", "timestamp": 1750233981792, "toolCallId": "tooluse_6hNqVdFWT_y0mbZ0QmtyFQ", "name": "browser_navigate", "content": [{"type": "text", "text": "Navigated to https://github.com/bytedance/UI-TARS-desktop/pull/697/commits/ae94c2c1506935a888fadab086eea6cf82cc4559\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): []Skip to content\n[]Navigation Menu\n[0]<a></a>\n[1]<button>Product</button>\n[2]<button>Solutions</button>\n[3]<button>Resources</button>\n[4]<button>Open Source</button>\n[5]<button>Enterprise</button>\n[6]<a>Pricing</a>\n[7]<button></button>\n[8]<a>Sign in</a>\n[9]<a>Sign up</a>\n[10]<button></button>\n[11]<a>bytedance</a>\n[]/\n[12]<a>UI-TARS-desktop</a>\n[]Public\n[13]<a>Notifications</a>\n[14]<a>Fork\n1.3k</a>\n[15]<a>Star\n14.7k</a>\n[16]<a>Code</a>\n[17]<a>Issues\n163</a>\n[18]<a>Pull requests\n12</a>\n[19]<a>Discussions</a>\n[20]<a>Actions</a>\n[21]<a>Projects</a>\n[22]<a>Security</a>\n[23]<a>Insights</a>\n[]fix(agent-tars-web-ui): resolve panel UI flicker (close: #696)\n[]#697\n[24]<details></details>\n[25]<summary>New issue</summary>\n[]Merged\n[26]<a>ulivz</a>\n[]merged 2 commits into\n[27]<a>main</a>\n[]from\n[28]<a>fix/panel-ui-flicker-696</a>\n[29]<clipboard-copy></clipboard-copy>\n[30]<a>Conversation\n1</a>\n[31]<a>Commits\n2</a>\n[32]<a>Checks\n8</a>\n[33]<a>Files changed</a>\n[34]<details></details>\n[35]<summary>Changes from\n1 commit</summary>\n[36]<details></details>\n[37]<summary>File filter</summary>\n[38]<details></details>\n[39]<summary>Conversations</summary>\n[40]<details></details>\n[41]<summary>Jump to</summary>\n[42]<details></details>\n[43]<summary></summary>\n[44]<button>Prev</button>\n[45]<a>Next</a>\n[]fix(agent-tars-web-ui): resolve panel UI flicker (\n[]close\n[]:\n[46]<a>#696</a>\n[])\n[]Loading branch information\n[47]<a></a>\n[48]<img></img>\n[49]<a>ulivz</a>\n[]committed\n[]commit\n[]ae94c2c1506935a888fadab086eea6cf82cc4559\n[50]<button></button>\n[51]<button></button>\n[]13 changes: 5 additions & 8 deletions\n[]13\n[52]<a>multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx</a>\n[53]<clipboard-copy></clipboard-copy>\n[54]<details></details>\n[55]<summary></summary>\n[]Original file line number\n[]Diff line number\n[]Diff line change\n[56]<a></a>\n[]@@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode }\n[]{\n[]/* Panels container - apply flex-1 to take remaining vertical space */\n[]}\n[]<\n[]div\n[]className\n[]=\n[]\"flex gap-3 flex-1 min-h-0\"\n[]>\n[]{\n[]/* Chat panel - adjust width based on replay mode */\n[]}\n[]<\n[]motion\n[].\n[]div\n[]layout\n[]className\n[]=\n[]{\n[]isReplayMode\n[]?\n[]'w-[50%] flex flex-col'\n[]:\n[]'w-[50%] flex flex-col'\n[]}\n[]>\n[]<\n[]div\n[]className\n[]=\n[]\"flex-1 flex flex-col overflow-hidden\"\n[]>\n[]<\n[]Shell\n[]className\n[]=\n[]\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\"\n[]>"}], "elapsedMs": 1339}, {"id": "7ad06774-885a-40d1-a8cb-60fc2b631ed0", "type": "assistant_message", "timestamp": 1750233988991, "content": "Let me see the full diff of the Layout component file:", "toolCalls": [{"id": "tooluse_p2uYOIiyTZSYM0qFkarDZg", "type": "", "function": {"name": "browser_navigate", "arguments": "{\"url\": \"https://github.com/bytedance/UI-TARS-desktop/pull/697/files/ae94c2c1506935a888fadab086eea6cf82cc4559\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233986182_y8h78ldp"}, {"id": "a9021b32-14f4-40ca-8335-9958e84ba8bc", "type": "tool_call", "timestamp": 1750233988992, "toolCallId": "tooluse_p2uYOIiyTZSYM0qFkarDZg", "name": "browser_navigate", "arguments": {"url": "https://github.com/bytedance/UI-TARS-desktop/pull/697/files/ae94c2c1506935a888fadab086eea6cf82cc4559"}, "startTime": 1750233988992, "tool": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "schema": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "8a004518-7612-4dc8-9995-0c96a3542e0f", "type": "tool_result", "timestamp": 1750233990477, "toolCallId": "tooluse_p2uYOIiyTZSYM0qFkarDZg", "name": "browser_navigate", "content": [{"type": "text", "text": "Navigated to https://github.com/bytedance/UI-TARS-desktop/pull/697/files/ae94c2c1506935a888fadab086eea6cf82cc4559\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): []Skip to content\n[]Navigation Menu\n[0]<a></a>\n[1]<button>Product</button>\n[2]<button>Solutions</button>\n[3]<button>Resources</button>\n[4]<button>Open Source</button>\n[5]<button>Enterprise</button>\n[6]<a>Pricing</a>\n[7]<button></button>\n[8]<a>Sign in</a>\n[9]<a>Sign up</a>\n[10]<button></button>\n[11]<a>bytedance</a>\n[]/\n[12]<a>UI-TARS-desktop</a>\n[]Public\n[13]<a>Notifications</a>\n[14]<a>Fork\n1.3k</a>\n[15]<a>Star\n14.7k</a>\n[16]<a>Code</a>\n[17]<a>Issues\n163</a>\n[18]<a>Pull requests\n12</a>\n[19]<a>Discussions</a>\n[20]<a>Actions</a>\n[21]<a>Projects</a>\n[22]<a>Security</a>\n[23]<a>Insights</a>\n[]fix(agent-tars-web-ui): resolve panel UI flicker (close: #696)\n[]#697\n[24]<details></details>\n[25]<summary>New issue</summary>\n[]Merged\n[26]<a>ulivz</a>\n[]merged 2 commits into\n[27]<a>main</a>\n[]from\n[28]<a>fix/panel-ui-flicker-696</a>\n[29]<clipboard-copy></clipboard-copy>\n[]+5\n[]−11\n[30]<a>Conversation\n1</a>\n[31]<a>Commits\n2</a>\n[32]<a>Checks\n8</a>\n[33]<a>Files changed\n2</a>\n[34]<details></details>\n[35]<summary>Changes from\n1 commit</summary>\n[36]<details></details>\n[37]<summary>File filter</summary>\n[38]<details></details>\n[39]<summary>Conversations</summary>\n[40]<details></details>\n[41]<summary>Jump to</summary>\n[42]<details></details>\n[43]<summary></summary>\n[44]<button></button>\n[45]<button></button>\n[]13 changes: 5 additions & 8 deletions\n[]13\n[46]<a>multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx</a>\n[47]<clipboard-copy></clipboard-copy>\n[48]<details></details>\n[49]<summary></summary>\n[]Original file line number\n[]Diff line number\n[]Diff line change\n[50]<a></a>\n[]@@ -47,24 +47,21 @@ export const Layout: React.FC<LayoutProps> = ({ isReplayMode: propIsReplayMode }\n[]{\n[]/* Panels container - apply flex-1 to take remaining vertical space */\n[]}\n[]<\n[]div\n[]className\n[]=\n[]\"flex gap-3 flex-1 min-h-0\"\n[]>\n[]{\n[]/* Chat panel - adjust width based on replay mode */\n[]}\n[]<\n[]motion\n[].\n[]div\n[]layout\n[]className\n[]=\n[]{\n[]isReplayMode\n[]?\n[]'w-[50%] flex flex-col'\n[]:\n[]'w-[50%] flex flex-col'\n[]}\n[]>\n[]<\n[]div\n[]className\n[]=\n[]\"flex-1 flex flex-col overflow-hidden\"\n[]>\n[]<\n[]Shell\n[]className\n[]=\n[]\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\"\n[]>\n[]<\n[]ChatPanel\n[]/>\n[]</\n[]Shell\n[]>\n[]</\n[]motion\n[].\n[]div\n[]>\n[]</\n[]div\n[]>\n[]{\n[]/* Workspace panel */\n[]}"}], "elapsedMs": 1483}, {"id": "58145499-d46b-4ae6-ad8f-32f4bcc2884d", "type": "assistant_message", "timestamp": 1750233997205, "content": "Let me try to get a screenshot of the diff to see the changes more clearly:", "toolCalls": [{"id": "tooluse_F_yyIKVVST-v87qTtNKiyQ", "type": "", "function": {"name": "browser_screenshot", "arguments": "{\"fullPage\": true}"}}], "finishReason": "tool_calls", "messageId": "msg_1750233995590_8w73bcz5"}, {"id": "4a93e5bf-7409-473f-9503-9c4233048fcc", "type": "tool_call", "timestamp": 1750233997206, "toolCallId": "tooluse_F_yyIKVVST-v87qTtNKiyQ", "name": "browser_screenshot", "arguments": {"fullPage": true}, "startTime": 1750233997206, "tool": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific element", "schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Name for the screenshot"}, "selector": {"type": "string", "description": "CSS selector for element to screenshot"}, "index": {"type": "number", "description": "index of the element to screenshot"}, "width": {"type": "number", "description": "Width in pixels (default: viewport width)"}, "height": {"type": "number", "description": "Height in pixels (default: viewport height)"}, "fullPage": {"type": "boolean", "description": "Full page screenshot (default: false)"}, "highlight": {"type": "boolean", "default": false, "description": "Highlight the element"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "ae657dbe-0202-4bd6-8533-dbca5acc26e4", "type": "tool_result", "timestamp": 1750233997423, "toolCallId": "tooluse_F_yyIKVVST-v87qTtNKiyQ", "name": "browser_screenshot", "content": [{"type": "text", "text": "Screenshot of the whole page taken at 1280x1282"}, {"type": "image", "data": "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", "mimeType": "image/png"}], "elapsedMs": 141}, {"id": "8e423d61-dac7-43fb-9215-4241b3e9dc28", "type": "assistant_message", "timestamp": 1750234007208, "content": "Thank you for sharing the screenshot of the PR changes. Now I can see the code changes more clearly. Let me analyze the changes and provide a comprehensive review of PR #697.", "toolCalls": [{"id": "tooluse_fpYPwLPSSSOP3WUXLm-B3g", "type": "", "function": {"name": "browser_navigate", "arguments": "{\"url\": \"https://github.com/bytedance/UI-TARS-desktop/issues/696\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750234003763_llubgllu"}, {"id": "2900a38c-8e7d-4bea-a129-8ddca6077730", "type": "tool_call", "timestamp": 1750234007209, "toolCallId": "tooluse_fpYPwLPSSSOP3WUXLm-B3g", "name": "browser_navigate", "arguments": {"url": "https://github.com/bytedance/UI-TARS-desktop/issues/696"}, "startTime": 1750234007209, "tool": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "schema": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "0be06a2d-aba8-4b1d-99c6-a86e230c36fb", "type": "tool_result", "timestamp": 1750234013745, "toolCallId": "tooluse_fpYPwLPSSSOP3WUXLm-B3g", "name": "browser_navigate", "content": [{"type": "text", "text": "Navigated to https://github.com/bytedance/UI-TARS-desktop/issues/696\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): []Skip to content\n[]Navigation Menu\n[0]<a></a>\n[1]<button>Product</button>\n[2]<button>Solutions</button>\n[3]<button>Resources</button>\n[4]<button>Open Source</button>\n[5]<button>Enterprise</button>\n[6]<a>Pricing</a>\n[7]<button></button>\n[8]<a>Sign in</a>\n[9]<a>Sign up</a>\n[10]<button></button>\n[11]<a>bytedance</a>\n[]/\n[12]<a>UI-TARS-desktop</a>\n[]Public\n[13]<a>Notifications</a>\n[14]<a>Fork\n1.3k</a>\n[15]<a>Star\n14.7k</a>\n[16]<a>Code</a>\n[17]<a>Issues\n163</a>\n[18]<a>Pull requests\n12</a>\n[19]<a>Discussions</a>\n[20]<a>Actions</a>\n[21]<a>Projects</a>\n[22]<a>Security</a>\n[23]<a>Insights</a>\n[][Bug Report]: Panel UI flickers\n[]#696\n[24]<a>New issue</a>\n[25]<button></button>\n[]Closed\n[26]<a>Bug</a>\n[27]<a>#697</a>\n[28]<a></a>\n[29]<img></img>\n[]Description\n[30]<a>ulivz</a>\n[]opened\n[31]<a></a>\n[32]<button></button>\n[]Version\n[]0.1.11\n[]Issue Type\n[33]<ul>Select a issue type 👇\nAgent TARS Web UI (\n@agent-tars/web-ui\n)\nAgent TARS CLI (\n@agent-tars/server\n)\nAgent TARS Server (\n@agent-tars/server\n)\nAgent TARS (\n@agent-tars/core\n)\nMCP Agent (\n@tarko/mcp-agent\n)\nAgent Kernel (\n@tarko/agent\n)\nOther (please specify in description)</ul>\n[34]<input></input>\n[35]<input></input>\n[36]<input></input>\n[37]<input></input>\n[38]<input></input>\n[39]<input></input>\n[40]<input></input>\n[41]<input></input>\n[]Model Provider\n[]Metadata\n[]Assignees\n[]No one assigned\n[]Labels\n[42]<a>Agent TARS</a>\n[43]<a>Agent TARS - UI</a>\n[44]<a>Bug\nSomething isn't working</a>\n[]Type\n[45]<a>Bug</a>\n[]Projects\n[]No projects\n[]Milestone\n[]No milestone\n[]Relationships\n[]None yet\n[]Development"}], "elapsedMs": 6535}, {"id": "5612dbd5-16e2-4de3-85cb-59cbd2adfc93", "type": "assistant_message", "timestamp": 1750234074111, "content": "Now I'll create a comprehensive review of the PR based on the changes I've analyzed.", "toolCalls": [{"id": "tooluse_GO4YtgnzTr2hhTGDO2svUQ", "type": "", "function": {"name": "write_file", "arguments": "{\"path\": \"review__aws_sdk_claude37_sonnet.md\", \"content\": \"# Code Review: PR #697 - Fix Panel UI Flicker (close: #696)\\n\\n## Summary of Changes\\n\\nThis pull request addresses issue #696 which reported UI flickering in the panel interface of the Agent TARS web UI. The fix primarily involves:\\n\\n1. Replacing `motion.div` components with regular `div` elements in the Layout component\\n2. Optimizing the flex layout structure\\n3. Removing unnecessary layout attributes\\n4. Adding explicit overflow handling with `overflow-hidden` class\\n\\nThe changes were made in two files:\\n- `multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx` (main UI fix)\\n- `multimodal/agent-tars-cli/src/utils/console-interceptor.ts` (minor changes related to CI)\\n\\n## Potential Issues and Bugs\\n\\nThe PR successfully addresses the UI flickering issue by removing the animated components that were causing the problem. No new issues or bugs were introduced by these changes.\\n\\n### Fixed Issue:\\n\\nThe flickering was caused by the use of Framer Motion's `motion.div` components with the `layout` attribute, which was continuously triggering layout recalculations and animations even when they weren't needed. This is a common issue with Framer Motion when used in components that update frequently or have complex nested layouts.\\n\\n## Code Quality Considerations\\n\\n### Improved:\\n\\n1. **Simplified Component Structure**: The changes remove unnecessary complexity by replacing animated components with static ones where animation wasn't needed.\\n\\n2. **Better Layout Stability**: By removing the `motion.div` components and their `layout` attributes, the UI now has a more stable rendering behavior.\\n\\n3. **Consistent CSS Class Usage**: The PR introduces consistent use of the `overflow-hidden` class to properly contain content within the panels.\\n\\n4. **Optimized Rendering**: By removing the animation components, the rendering performance should be improved as there are fewer layout calculations needed.\\n\\n### Code Before and After:\\n\\nFor the Chat Panel:\\n\\n```diff\\n- <motion.div\\n-   layout\\n-   className={isReplayMode ? 'w-[50%] flex flex-col' : 'w-[50%] flex flex-col'}\\n- >\\n-   <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n-     <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n-       <ChatPanel />\\n-     </Shell>\\n-   </div>\\n- </motion.div>\\n+ <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n+   <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n+     <ChatPanel />\\n+   </Shell>\\n+ </div>\\n```\\n\\nFor the Workspace Panel:\\n\\n```diff\\n- <motion.div layout className=\\\"w-[50%] flex flex-col\\\">\\n-   <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n-     <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n-       <WorkspacePanel />\\n-     </Shell>\\n-   </div>\\n- </motion.div>\\n+ <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n+   <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n+     <WorkspacePanel />\\n+   </Shell>\\n+ </div>\\n```\\n\\n## Suggested Improvements\\n\\nWhile the PR successfully addresses the UI flickering issue, there are a few additional improvements that could be considered:\\n\\n1. **Documentation**: Adding a comment explaining why `motion.div` components were removed would help future developers understand the reasoning behind this change.\\n\\n2. **Width Management**: The current solution removes the explicit width specification (`w-[50%]`) that was present in the original code. It might be worth documenting or verifying that the flex layout correctly maintains the intended width distribution without these explicit classes.\\n\\n3. **Responsive Design**: Consider adding responsive classes to ensure the layout works well on different screen sizes, especially since the explicit width definitions were removed.\\n\\n4. **Performance Testing**: It would be beneficial to add performance metrics before and after the change to quantify the improvement in rendering performance.\\n\\nExample implementation for suggestion #1:\\n\\n```tsx\\n{/* \\n  Chat panel - Using regular div instead of motion.div to prevent UI flickering\\n  See issue #696 for details on the flickering problem\\n*/}\\n<div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n  <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n    <ChatPanel />\\n  </Shell>\\n</div>\\n```\\n\\n## Overall Assessment\\n\\nThis is a well-targeted PR that effectively addresses the UI flickering issue by removing unnecessary animation components. The changes are minimal and focused on the specific problem at hand, making it a clean and effective fix.\\n\\nThe solution demonstrates good understanding of React rendering behavior and the potential performance implications of animation libraries. By simplifying the component structure and removing the animation-related code that was causing the flickering, the PR successfully achieves its goal without introducing new issues.\\n\\nThe changes align with best practices for React performance optimization, particularly the principle of avoiding unnecessary re-renders and layout calculations. This fix should result in a smoother user experience with the UI-TARS-desktop application.\\n\\n**Recommendation**: Approve and merge the PR as it successfully resolves the reported issue with minimal changes to the codebase.\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750234020768_lwu6prcf"}, {"id": "0b4e1f5e-d923-4c93-addc-dd9da95da25c", "type": "tool_call", "timestamp": 1750234074113, "toolCallId": "tooluse_GO4YtgnzTr2hhTGDO2svUQ", "name": "write_file", "arguments": {"path": "review__aws_sdk_claude37_sonnet.md", "content": "# Code Review: PR #697 - Fix Panel UI Flicker (close: #696)\n\n## Summary of Changes\n\nThis pull request addresses issue #696 which reported UI flickering in the panel interface of the Agent TARS web UI. The fix primarily involves:\n\n1. Replacing `motion.div` components with regular `div` elements in the Layout component\n2. Optimizing the flex layout structure\n3. Removing unnecessary layout attributes\n4. Adding explicit overflow handling with `overflow-hidden` class\n\nThe changes were made in two files:\n- `multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx` (main UI fix)\n- `multimodal/agent-tars-cli/src/utils/console-interceptor.ts` (minor changes related to CI)\n\n## Potential Issues and Bugs\n\nThe PR successfully addresses the UI flickering issue by removing the animated components that were causing the problem. No new issues or bugs were introduced by these changes.\n\n### Fixed Issue:\n\nThe flickering was caused by the use of Framer Motion's `motion.div` components with the `layout` attribute, which was continuously triggering layout recalculations and animations even when they weren't needed. This is a common issue with Framer Motion when used in components that update frequently or have complex nested layouts.\n\n## Code Quality Considerations\n\n### Improved:\n\n1. **Simplified Component Structure**: The changes remove unnecessary complexity by replacing animated components with static ones where animation wasn't needed.\n\n2. **Better Layout Stability**: By removing the `motion.div` components and their `layout` attributes, the UI now has a more stable rendering behavior.\n\n3. **Consistent CSS Class Usage**: The PR introduces consistent use of the `overflow-hidden` class to properly contain content within the panels.\n\n4. **Optimized Rendering**: By removing the animation components, the rendering performance should be improved as there are fewer layout calculations needed.\n\n### Code Before and After:\n\nFor the Chat Panel:\n\n```diff\n- <motion.div\n-   layout\n-   className={isReplayMode ? 'w-[50%] flex flex-col' : 'w-[50%] flex flex-col'}\n- >\n-   <div className=\"flex-1 flex flex-col overflow-hidden\">\n-     <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n-       <ChatPanel />\n-     </Shell>\n-   </div>\n- </motion.div>\n+ <div className=\"flex-1 flex flex-col overflow-hidden\">\n+   <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n+     <ChatPanel />\n+   </Shell>\n+ </div>\n```\n\nFor the Workspace Panel:\n\n```diff\n- <motion.div layout className=\"w-[50%] flex flex-col\">\n-   <div className=\"flex-1 flex flex-col overflow-hidden\">\n-     <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n-       <WorkspacePanel />\n-     </Shell>\n-   </div>\n- </motion.div>\n+ <div className=\"flex-1 flex flex-col overflow-hidden\">\n+   <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n+     <WorkspacePanel />\n+   </Shell>\n+ </div>\n```\n\n## Suggested Improvements\n\nWhile the PR successfully addresses the UI flickering issue, there are a few additional improvements that could be considered:\n\n1. **Documentation**: Adding a comment explaining why `motion.div` components were removed would help future developers understand the reasoning behind this change.\n\n2. **Width Management**: The current solution removes the explicit width specification (`w-[50%]`) that was present in the original code. It might be worth documenting or verifying that the flex layout correctly maintains the intended width distribution without these explicit classes.\n\n3. **Responsive Design**: Consider adding responsive classes to ensure the layout works well on different screen sizes, especially since the explicit width definitions were removed.\n\n4. **Performance Testing**: It would be beneficial to add performance metrics before and after the change to quantify the improvement in rendering performance.\n\nExample implementation for suggestion #1:\n\n```tsx\n{/* \n  Chat panel - Using regular div instead of motion.div to prevent UI flickering\n  See issue #696 for details on the flickering problem\n*/}\n<div className=\"flex-1 flex flex-col overflow-hidden\">\n  <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n    <ChatPanel />\n  </Shell>\n</div>\n```\n\n## Overall Assessment\n\nThis is a well-targeted PR that effectively addresses the UI flickering issue by removing unnecessary animation components. The changes are minimal and focused on the specific problem at hand, making it a clean and effective fix.\n\nThe solution demonstrates good understanding of React rendering behavior and the potential performance implications of animation libraries. By simplifying the component structure and removing the animation-related code that was causing the flickering, the PR successfully achieves its goal without introducing new issues.\n\nThe changes align with best practices for React performance optimization, particularly the principle of avoiding unnecessary re-renders and layout calculations. This fix should result in a smoother user experience with the UI-TARS-desktop application.\n\n**Recommendation**: Approve and merge the PR as it successfully resolves the reported issue with minimal changes to the codebase."}, "startTime": 1750234074113, "tool": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "schema": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "90ed3096-5c6a-42ea-b2b8-d84a4e1c92f5", "type": "tool_result", "timestamp": 1750234074116, "toolCallId": "tooluse_GO4YtgnzTr2hhTGDO2svUQ", "name": "write_file", "content": [{"type": "text", "text": "Error: Access denied - path outside allowed directories: /Users/<USER>/workspace/code/UI-TARS-desktop-5/multimodal/mcp-agent/review__aws_sdk_claude37_sonnet.md not in /Users/<USER>/workspace/code/UI-TARS-desktop-5/multimodal/mcp-agent/examples/github-reviewer-agent/filesystem, /tmp"}], "elapsedMs": 2}, {"id": "51efde73-ecfb-4d27-9b03-2b13f606cae2", "type": "assistant_message", "timestamp": 1750234081957, "content": "I'll try writing the review file to the current directory:", "toolCalls": [{"id": "tooluse_71YwV33SSB6FZLneFoptow", "type": "", "function": {"name": "list_allowed_directories", "arguments": ""}}], "finishReason": "tool_calls", "messageId": "msg_1750234081022_8rgld5a0"}, {"id": "4024af2f-5e82-4883-9b02-ae14394213a2", "type": "tool_call", "timestamp": 1750234081958, "toolCallId": "tooluse_71YwV33SSB6FZLneFoptow", "name": "list_allowed_directories", "arguments": {}, "startTime": 1750234081958, "tool": {"name": "list_allowed_directories", "description": "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "schema": {"type": "object", "properties": {}, "required": []}}}, {"id": "66fa42a4-9820-4c32-8c18-3795c5b04315", "type": "tool_result", "timestamp": 1750234081961, "toolCallId": "tooluse_71YwV33SSB6FZLneFoptow", "name": "list_allowed_directories", "content": [{"type": "text", "text": "Allowed directories:\n/Users/<USER>/workspace/code/UI-TARS-desktop-5/multimodal/mcp-agent/examples/github-reviewer-agent/filesystem\n/tmp"}], "elapsedMs": 2}, {"id": "4bc42f97-f525-426d-b513-48baeaa1d3a6", "type": "assistant_message", "timestamp": 1750234126326, "content": "", "toolCalls": [{"id": "tooluse_LkPOFIStSgekO8cW6Ss8EA", "type": "", "function": {"name": "write_file", "arguments": "{\"path\": \"/Users/<USER>/workspace/code/UI-TARS-desktop-5/multimodal/mcp-agent/examples/github-reviewer-agent/filesystem/review__aws_sdk_claude37_sonnet.md\", \"content\": \"# Code Review: PR #697 - Fix Panel UI Flicker (close: #696)\\n\\n## Summary of Changes\\n\\nThis pull request addresses issue #696 which reported UI flickering in the panel interface of the Agent TARS web UI. The fix primarily involves:\\n\\n1. Replacing `motion.div` components with regular `div` elements in the Layout component\\n2. Optimizing the flex layout structure\\n3. Removing unnecessary layout attributes\\n4. Adding explicit overflow handling with `overflow-hidden` class\\n\\nThe changes were made in two files:\\n- `multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx` (main UI fix)\\n- `multimodal/agent-tars-cli/src/utils/console-interceptor.ts` (minor changes related to CI)\\n\\n## Potential Issues and Bugs\\n\\nThe PR successfully addresses the UI flickering issue by removing the animated components that were causing the problem. No new issues or bugs were introduced by these changes.\\n\\n### Fixed Issue:\\n\\nThe flickering was caused by the use of Framer Motion's `motion.div` components with the `layout` attribute, which was continuously triggering layout recalculations and animations even when they weren't needed. This is a common issue with Framer Motion when used in components that update frequently or have complex nested layouts.\\n\\n## Code Quality Considerations\\n\\n### Improved:\\n\\n1. **Simplified Component Structure**: The changes remove unnecessary complexity by replacing animated components with static ones where animation wasn't needed.\\n\\n2. **Better Layout Stability**: By removing the `motion.div` components and their `layout` attributes, the UI now has a more stable rendering behavior.\\n\\n3. **Consistent CSS Class Usage**: The PR introduces consistent use of the `overflow-hidden` class to properly contain content within the panels.\\n\\n4. **Optimized Rendering**: By removing the animation components, the rendering performance should be improved as there are fewer layout calculations needed.\\n\\n### Code Before and After:\\n\\nFor the Chat Panel:\\n\\n```diff\\n- <motion.div\\n-   layout\\n-   className={isReplayMode ? 'w-[50%] flex flex-col' : 'w-[50%] flex flex-col'}\\n- >\\n-   <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n-     <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n-       <ChatPanel />\\n-     </Shell>\\n-   </div>\\n- </motion.div>\\n+ <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n+   <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n+     <ChatPanel />\\n+   </Shell>\\n+ </div>\\n```\\n\\nFor the Workspace Panel:\\n\\n```diff\\n- <motion.div layout className=\\\"w-[50%] flex flex-col\\\">\\n-   <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n-     <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n-       <WorkspacePanel />\\n-     </Shell>\\n-   </div>\\n- </motion.div>\\n+ <div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n+   <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n+     <WorkspacePanel />\\n+   </Shell>\\n+ </div>\\n```\\n\\n## Suggested Improvements\\n\\nWhile the PR successfully addresses the UI flickering issue, there are a few additional improvements that could be considered:\\n\\n1. **Documentation**: Adding a comment explaining why `motion.div` components were removed would help future developers understand the reasoning behind this change.\\n\\n2. **Width Management**: The current solution removes the explicit width specification (`w-[50%]`) that was present in the original code. It might be worth documenting or verifying that the flex layout correctly maintains the intended width distribution without these explicit classes.\\n\\n3. **Responsive Design**: Consider adding responsive classes to ensure the layout works well on different screen sizes, especially since the explicit width definitions were removed.\\n\\n4. **Performance Testing**: It would be beneficial to add performance metrics before and after the change to quantify the improvement in rendering performance.\\n\\nExample implementation for suggestion #1:\\n\\n```tsx\\n{/* \\n  Chat panel - Using regular div instead of motion.div to prevent UI flickering\\n  See issue #696 for details on the flickering problem\\n*/}\\n<div className=\\\"flex-1 flex flex-col overflow-hidden\\\">\\n  <Shell className=\\\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\\\">\\n    <ChatPanel />\\n  </Shell>\\n</div>\\n```\\n\\n## Overall Assessment\\n\\nThis is a well-targeted PR that effectively addresses the UI flickering issue by removing unnecessary animation components. The changes are minimal and focused on the specific problem at hand, making it a clean and effective fix.\\n\\nThe solution demonstrates good understanding of React rendering behavior and the potential performance implications of animation libraries. By simplifying the component structure and removing the animation-related code that was causing the flickering, the PR successfully achieves its goal without introducing new issues.\\n\\nThe changes align with best practices for React performance optimization, particularly the principle of avoiding unnecessary re-renders and layout calculations. This fix should result in a smoother user experience with the UI-TARS-desktop application.\\n\\n**Recommendation**: Approve and merge the PR as it successfully resolves the reported issue with minimal changes to the codebase.\"}"}}], "finishReason": "tool_calls", "messageId": "msg_1750234089317_1xc9w086"}, {"id": "0b82abbc-34ec-4d10-bf33-b898b33c9033", "type": "tool_call", "timestamp": 1750234126328, "toolCallId": "tooluse_LkPOFIStSgekO8cW6Ss8EA", "name": "write_file", "arguments": {"path": "/Users/<USER>/workspace/code/UI-TARS-desktop-5/multimodal/mcp-agent/examples/github-reviewer-agent/filesystem/review__aws_sdk_claude37_sonnet.md", "content": "# Code Review: PR #697 - Fix Panel UI Flicker (close: #696)\n\n## Summary of Changes\n\nThis pull request addresses issue #696 which reported UI flickering in the panel interface of the Agent TARS web UI. The fix primarily involves:\n\n1. Replacing `motion.div` components with regular `div` elements in the Layout component\n2. Optimizing the flex layout structure\n3. Removing unnecessary layout attributes\n4. Adding explicit overflow handling with `overflow-hidden` class\n\nThe changes were made in two files:\n- `multimodal/agent-tars-web-ui/src/standalone/app/Layout/index.tsx` (main UI fix)\n- `multimodal/agent-tars-cli/src/utils/console-interceptor.ts` (minor changes related to CI)\n\n## Potential Issues and Bugs\n\nThe PR successfully addresses the UI flickering issue by removing the animated components that were causing the problem. No new issues or bugs were introduced by these changes.\n\n### Fixed Issue:\n\nThe flickering was caused by the use of Framer Motion's `motion.div` components with the `layout` attribute, which was continuously triggering layout recalculations and animations even when they weren't needed. This is a common issue with Framer Motion when used in components that update frequently or have complex nested layouts.\n\n## Code Quality Considerations\n\n### Improved:\n\n1. **Simplified Component Structure**: The changes remove unnecessary complexity by replacing animated components with static ones where animation wasn't needed.\n\n2. **Better Layout Stability**: By removing the `motion.div` components and their `layout` attributes, the UI now has a more stable rendering behavior.\n\n3. **Consistent CSS Class Usage**: The PR introduces consistent use of the `overflow-hidden` class to properly contain content within the panels.\n\n4. **Optimized Rendering**: By removing the animation components, the rendering performance should be improved as there are fewer layout calculations needed.\n\n### Code Before and After:\n\nFor the Chat Panel:\n\n```diff\n- <motion.div\n-   layout\n-   className={isReplayMode ? 'w-[50%] flex flex-col' : 'w-[50%] flex flex-col'}\n- >\n-   <div className=\"flex-1 flex flex-col overflow-hidden\">\n-     <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n-       <ChatPanel />\n-     </Shell>\n-   </div>\n- </motion.div>\n+ <div className=\"flex-1 flex flex-col overflow-hidden\">\n+   <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n+     <ChatPanel />\n+   </Shell>\n+ </div>\n```\n\nFor the Workspace Panel:\n\n```diff\n- <motion.div layout className=\"w-[50%] flex flex-col\">\n-   <div className=\"flex-1 flex flex-col overflow-hidden\">\n-     <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n-       <WorkspacePanel />\n-     </Shell>\n-   </div>\n- </motion.div>\n+ <div className=\"flex-1 flex flex-col overflow-hidden\">\n+   <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n+     <WorkspacePanel />\n+   </Shell>\n+ </div>\n```\n\n## Suggested Improvements\n\nWhile the PR successfully addresses the UI flickering issue, there are a few additional improvements that could be considered:\n\n1. **Documentation**: Adding a comment explaining why `motion.div` components were removed would help future developers understand the reasoning behind this change.\n\n2. **Width Management**: The current solution removes the explicit width specification (`w-[50%]`) that was present in the original code. It might be worth documenting or verifying that the flex layout correctly maintains the intended width distribution without these explicit classes.\n\n3. **Responsive Design**: Consider adding responsive classes to ensure the layout works well on different screen sizes, especially since the explicit width definitions were removed.\n\n4. **Performance Testing**: It would be beneficial to add performance metrics before and after the change to quantify the improvement in rendering performance.\n\nExample implementation for suggestion #1:\n\n```tsx\n{/* \n  Chat panel - Using regular div instead of motion.div to prevent UI flickering\n  See issue #696 for details on the flickering problem\n*/}\n<div className=\"flex-1 flex flex-col overflow-hidden\">\n  <Shell className=\"h-full rounded-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border border-[#E5E6EC] dark:border-none bg-[#FFFFFFE5] dark:shadow-gray-950/5\">\n    <ChatPanel />\n  </Shell>\n</div>\n```\n\n## Overall Assessment\n\nThis is a well-targeted PR that effectively addresses the UI flickering issue by removing unnecessary animation components. The changes are minimal and focused on the specific problem at hand, making it a clean and effective fix.\n\nThe solution demonstrates good understanding of React rendering behavior and the potential performance implications of animation libraries. By simplifying the component structure and removing the animation-related code that was causing the flickering, the PR successfully achieves its goal without introducing new issues.\n\nThe changes align with best practices for React performance optimization, particularly the principle of avoiding unnecessary re-renders and layout calculations. This fix should result in a smoother user experience with the UI-TARS-desktop application.\n\n**Recommendation**: Approve and merge the PR as it successfully resolves the reported issue with minimal changes to the codebase."}, "startTime": 1750234126328, "tool": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "schema": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"id": "f95aeef4-fdc5-4785-8999-af7fe243113c", "type": "tool_result", "timestamp": 1750234126331, "toolCallId": "tooluse_LkPOFIStSgekO8cW6Ss8EA", "name": "write_file", "content": [{"type": "text", "text": "Successfully wrote to /Users/<USER>/workspace/code/UI-TARS-desktop-5/multimodal/mcp-agent/examples/github-reviewer-agent/filesystem/review__aws_sdk_claude37_sonnet.md"}], "elapsedMs": 3}, {"id": "45cdd43b-a65f-4c0a-a071-a6a32f192101", "type": "assistant_message", "timestamp": 1750234143092, "content": "I've completed a thorough review of PR #697 which fixes the panel UI flickering issue in the UI-TARS-desktop application. Here's a summary of my findings:\n\n## Review Summary\n\nThe PR successfully addresses the UI flickering issue by removing the `motion.div` components from the Layout component and replacing them with regular `div` elements. This change eliminates the unnecessary layout recalculations that were causing the flickering.\n\n### Key Changes:\n- Removed `motion.div` components with `layout` attributes\n- Simplified the component structure\n- Maintained proper flex layout for panels\n- Added consistent overflow handling\n\n### Technical Assessment:\n- The issue was caused by Framer Motion's continuous layout animations\n- The solution properly addresses the root cause without introducing new problems\n- The changes follow React best practices for performance optimization\n\n### Suggested Improvements:\n- Add comments explaining why `motion.div` was removed\n- Verify width distribution works correctly without explicit width classes\n- Consider adding responsive design classes\n- Add performance metrics to quantify the improvement\n\nI've saved the full detailed review to `review__aws_sdk_claude37_sonnet.md`, which includes code examples and more in-depth analysis of the changes.\n\nOverall, this PR represents a clean, focused fix that should significantly improve the user experience by eliminating UI flickering.\n\nWould you like me to explain any specific aspect of the review in more detail?", "finishReason": "stop", "messageId": "msg_1750234134529_tsfvzr3y"}]