{"version": 3, "sources": ["../../../../../../../node_modules/idb-keyval/dist/index.js"], "sourcesContent": ["function promisifyRequest(request) {\n    return new Promise((resolve, reject) => {\n        // @ts-ignore - file size hacks\n        request.oncomplete = request.onsuccess = () => resolve(request.result);\n        // @ts-ignore - file size hacks\n        request.onabort = request.onerror = () => reject(request.error);\n    });\n}\nfunction createStore(dbName, storeName) {\n    const request = indexedDB.open(dbName);\n    request.onupgradeneeded = () => request.result.createObjectStore(storeName);\n    const dbp = promisifyRequest(request);\n    return (txMode, callback) => dbp.then((db) => callback(db.transaction(storeName, txMode).objectStore(storeName)));\n}\nlet defaultGetStoreFunc;\nfunction defaultGetStore() {\n    if (!defaultGetStoreFunc) {\n        defaultGetStoreFunc = createStore('keyval-store', 'keyval');\n    }\n    return defaultGetStoreFunc;\n}\n/**\n * Get a value by its key.\n *\n * @param key\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction get(key, customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => promisifyRequest(store.get(key)));\n}\n/**\n * Set a value with a key.\n *\n * @param key\n * @param value\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction set(key, value, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        store.put(value, key);\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Set multiple values at once. This is faster than calling set() multiple times.\n * It's also atomic – if one of the pairs can't be added, none will be added.\n *\n * @param entries Array of entries, where each entry is an array of `[key, value]`.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction setMany(entries, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        entries.forEach((entry) => store.put(entry[1], entry[0]));\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Get multiple values by their keys\n *\n * @param keys\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction getMany(keys, customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => Promise.all(keys.map((key) => promisifyRequest(store.get(key)))));\n}\n/**\n * Update a value. This lets you see the old value and update it as an atomic operation.\n *\n * @param key\n * @param updater A callback that takes the old value and returns a new value.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction update(key, updater, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => \n    // Need to create the promise manually.\n    // If I try to chain promises, the transaction closes in browsers\n    // that use a promise polyfill (IE10/11).\n    new Promise((resolve, reject) => {\n        store.get(key).onsuccess = function () {\n            try {\n                store.put(updater(this.result), key);\n                resolve(promisifyRequest(store.transaction));\n            }\n            catch (err) {\n                reject(err);\n            }\n        };\n    }));\n}\n/**\n * Delete a particular key from the store.\n *\n * @param key\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction del(key, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        store.delete(key);\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Delete multiple keys at once.\n *\n * @param keys List of keys to delete.\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction delMany(keys, customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        keys.forEach((key) => store.delete(key));\n        return promisifyRequest(store.transaction);\n    });\n}\n/**\n * Clear all values in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction clear(customStore = defaultGetStore()) {\n    return customStore('readwrite', (store) => {\n        store.clear();\n        return promisifyRequest(store.transaction);\n    });\n}\nfunction eachCursor(store, callback) {\n    store.openCursor().onsuccess = function () {\n        if (!this.result)\n            return;\n        callback(this.result);\n        this.result.continue();\n    };\n    return promisifyRequest(store.transaction);\n}\n/**\n * Get all keys in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction keys(customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => {\n        // Fast path for modern browsers\n        if (store.getAllKeys) {\n            return promisifyRequest(store.getAllKeys());\n        }\n        const items = [];\n        return eachCursor(store, (cursor) => items.push(cursor.key)).then(() => items);\n    });\n}\n/**\n * Get all values in the store.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction values(customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => {\n        // Fast path for modern browsers\n        if (store.getAll) {\n            return promisifyRequest(store.getAll());\n        }\n        const items = [];\n        return eachCursor(store, (cursor) => items.push(cursor.value)).then(() => items);\n    });\n}\n/**\n * Get all entries in the store. Each entry is an array of `[key, value]`.\n *\n * @param customStore Method to get a custom store. Use with caution (see the docs).\n */\nfunction entries(customStore = defaultGetStore()) {\n    return customStore('readonly', (store) => {\n        // Fast path for modern browsers\n        // (although, hopefully we'll get a simpler path some day)\n        if (store.getAll && store.getAllKeys) {\n            return Promise.all([\n                promisifyRequest(store.getAllKeys()),\n                promisifyRequest(store.getAll()),\n            ]).then(([keys, values]) => keys.map((key, i) => [key, values[i]]));\n        }\n        const items = [];\n        return customStore('readonly', (store) => eachCursor(store, (cursor) => items.push([cursor.key, cursor.value])).then(() => items));\n    });\n}\n\nexport { clear, createStore, del, delMany, entries, get, getMany, keys, promisifyRequest, set, setMany, update, values };\n"], "mappings": ";;;AAAA,SAAS,iBAAiB,SAAS;AAC/B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEpC,YAAQ,aAAa,QAAQ,YAAY,MAAM,QAAQ,QAAQ,MAAM;AAErE,YAAQ,UAAU,QAAQ,UAAU,MAAM,OAAO,QAAQ,KAAK;AAAA,EAClE,CAAC;AACL;AACA,SAAS,YAAY,QAAQ,WAAW;AACpC,QAAM,UAAU,UAAU,KAAK,MAAM;AACrC,UAAQ,kBAAkB,MAAM,QAAQ,OAAO,kBAAkB,SAAS;AAC1E,QAAM,MAAM,iBAAiB,OAAO;AACpC,SAAO,CAAC,QAAQ,aAAa,IAAI,KAAK,CAAC,OAAO,SAAS,GAAG,YAAY,WAAW,MAAM,EAAE,YAAY,SAAS,CAAC,CAAC;AACpH;AACA,IAAI;AACJ,SAAS,kBAAkB;AACvB,MAAI,CAAC,qBAAqB;AACtB,0BAAsB,YAAY,gBAAgB,QAAQ;AAAA,EAC9D;AACA,SAAO;AACX;AAOA,SAAS,IAAI,KAAK,cAAc,gBAAgB,GAAG;AAC/C,SAAO,YAAY,YAAY,CAAC,UAAU,iBAAiB,MAAM,IAAI,GAAG,CAAC,CAAC;AAC9E;AAQA,SAAS,IAAI,KAAK,OAAO,cAAc,gBAAgB,GAAG;AACtD,SAAO,YAAY,aAAa,CAAC,UAAU;AACvC,UAAM,IAAI,OAAO,GAAG;AACpB,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC7C,CAAC;AACL;AAQA,SAAS,QAAQA,UAAS,cAAc,gBAAgB,GAAG;AACvD,SAAO,YAAY,aAAa,CAAC,UAAU;AACvC,IAAAA,SAAQ,QAAQ,CAAC,UAAU,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACxD,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC7C,CAAC;AACL;AAOA,SAAS,QAAQC,OAAM,cAAc,gBAAgB,GAAG;AACpD,SAAO,YAAY,YAAY,CAAC,UAAU,QAAQ,IAAIA,MAAK,IAAI,CAAC,QAAQ,iBAAiB,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9G;AAQA,SAAS,OAAO,KAAK,SAAS,cAAc,gBAAgB,GAAG;AAC3D,SAAO,YAAY,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA,IAIjC,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7B,YAAM,IAAI,GAAG,EAAE,YAAY,WAAY;AACnC,YAAI;AACA,gBAAM,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;AACnC,kBAAQ,iBAAiB,MAAM,WAAW,CAAC;AAAA,QAC/C,SACO,KAAK;AACR,iBAAO,GAAG;AAAA,QACd;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,GAAC;AACN;AAOA,SAAS,IAAI,KAAK,cAAc,gBAAgB,GAAG;AAC/C,SAAO,YAAY,aAAa,CAAC,UAAU;AACvC,UAAM,OAAO,GAAG;AAChB,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC7C,CAAC;AACL;AAOA,SAAS,QAAQA,OAAM,cAAc,gBAAgB,GAAG;AACpD,SAAO,YAAY,aAAa,CAAC,UAAU;AACvC,IAAAA,MAAK,QAAQ,CAAC,QAAQ,MAAM,OAAO,GAAG,CAAC;AACvC,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC7C,CAAC;AACL;AAMA,SAAS,MAAM,cAAc,gBAAgB,GAAG;AAC5C,SAAO,YAAY,aAAa,CAAC,UAAU;AACvC,UAAM,MAAM;AACZ,WAAO,iBAAiB,MAAM,WAAW;AAAA,EAC7C,CAAC;AACL;AACA,SAAS,WAAW,OAAO,UAAU;AACjC,QAAM,WAAW,EAAE,YAAY,WAAY;AACvC,QAAI,CAAC,KAAK;AACN;AACJ,aAAS,KAAK,MAAM;AACpB,SAAK,OAAO,SAAS;AAAA,EACzB;AACA,SAAO,iBAAiB,MAAM,WAAW;AAC7C;AAMA,SAAS,KAAK,cAAc,gBAAgB,GAAG;AAC3C,SAAO,YAAY,YAAY,CAAC,UAAU;AAEtC,QAAI,MAAM,YAAY;AAClB,aAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,IAC9C;AACA,UAAM,QAAQ,CAAC;AACf,WAAO,WAAW,OAAO,CAAC,WAAW,MAAM,KAAK,OAAO,GAAG,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,EACjF,CAAC;AACL;AAMA,SAAS,OAAO,cAAc,gBAAgB,GAAG;AAC7C,SAAO,YAAY,YAAY,CAAC,UAAU;AAEtC,QAAI,MAAM,QAAQ;AACd,aAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,IAC1C;AACA,UAAM,QAAQ,CAAC;AACf,WAAO,WAAW,OAAO,CAAC,WAAW,MAAM,KAAK,OAAO,KAAK,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,EACnF,CAAC;AACL;AAMA,SAAS,QAAQ,cAAc,gBAAgB,GAAG;AAC9C,SAAO,YAAY,YAAY,CAAC,UAAU;AAGtC,QAAI,MAAM,UAAU,MAAM,YAAY;AAClC,aAAO,QAAQ,IAAI;AAAA,QACf,iBAAiB,MAAM,WAAW,CAAC;AAAA,QACnC,iBAAiB,MAAM,OAAO,CAAC;AAAA,MACnC,CAAC,EAAE,KAAK,CAAC,CAACA,OAAMC,OAAM,MAAMD,MAAK,IAAI,CAAC,KAAK,MAAM,CAAC,KAAKC,QAAO,CAAC,CAAC,CAAC,CAAC;AAAA,IACtE;AACA,UAAM,QAAQ,CAAC;AACf,WAAO,YAAY,YAAY,CAACC,WAAU,WAAWA,QAAO,CAAC,WAAW,MAAM,KAAK,CAAC,OAAO,KAAK,OAAO,KAAK,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK,CAAC;AAAA,EACrI,CAAC;AACL;", "names": ["entries", "keys", "values", "store"]}