on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]

name: secretlint

jobs:
  secretlint:
    permissions:
      contents: read
    name: "Run secretlint to diff files"
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # fetch history to get all changed files on push or pull_request event
          fetch-depth: 0
      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@2d756ea4c53f7f6b397767d8723b3a10a9f35bf2 # v44
      - name: setup Node ${{ matrix.node-version }}
        uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
      - name: Show changed files
        run: echo "${{ steps.changed-files.outputs.all_changed_files }}"
      - name: Install
        if: steps.changed-files.outputs.any_changed == 'true'
        run: pnpm i
      - name: Run secretlint
        if: steps.changed-files.outputs.any_changed == 'true'
        run: npx secretlint ${{ steps.changed-files.outputs.all_changed_files }}
