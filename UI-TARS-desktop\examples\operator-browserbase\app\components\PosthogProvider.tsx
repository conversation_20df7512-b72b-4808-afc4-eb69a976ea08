'use client';

import posthog from 'posthog-js';
import { PostHog<PERSON><PERSON>ider as PHProvider } from 'posthog-js/react';
import { useEffect } from 'react';

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    try {
      if (
        typeof window === 'undefined' ||
        !process.env.NEXT_PUBLIC_POSTHOG_KEY ||
        !process.env.NEXT_PUBLIC_POSTHOG_HOST
      )
        return;
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
        api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
        person_profiles: 'always',
      });
    } catch (e) {
      console.error(e);
    }
  }, []);

  return <PHProvider client={posthog}>{children}</PHProvider>;
}
