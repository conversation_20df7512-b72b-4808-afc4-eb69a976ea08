{"name": "@agent-tars/cli", "version": "0.3.0-beta.1", "description": "CLI for Agent TARS.", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"agent-tars": "bin/cli.js"}, "engines": {"node": ">=22.15.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "files": ["dist", "static"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "build:sea": "pkg --config package.json dist/cli.js --experimental-require-module", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@tarko/agent-cli": "workspace:*", "@agent-tars/core": "workspace:*"}, "devDependencies": {"@rslib/core": "0.10.0", "@agent-tars/interface": "workspace:*", "@types/node": "^20.14.8", "typescript": "^5.7.2", "rimraf": "^6.0.1"}, "pkg": {"assets": ["static/**/*"], "targets": ["node22-linux-arm64", "node22-macos-arm64", "node22-win-arm64"]}}