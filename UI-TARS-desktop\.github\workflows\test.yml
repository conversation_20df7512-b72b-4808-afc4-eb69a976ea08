on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - '**'
    paths-ignore:
      - 'multimodal/**'
  push:
    branches:
      - 'main'
    paths-ignore:
      - 'multimodal/**'

permissions:
  id-token: write
  contents: read
  attestations: read

name: CI Test, Typecheck
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-test
  cancel-in-progress: true
env:
  CI: true
  NODE_OPTIONS: --max-old-space-size=8192
  HUSKY: 0

jobs:
  test:
    name: Test & Typecheck
    runs-on: macos-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Cache turbo
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Install pnpm
        run: npm install -g pnpm@9
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
          cache: 'pnpm'
      - uses: browser-actions/setup-chrome@v1
        with:
          chrome-version: 120
          install-dependencies: true
      - name: Install dependencies
        run: pnpm install
      - name: Run typecheck
        run: npm exec turbo run typecheck
      - name: Run test
        run: npm exec turbo run coverage
      - name: Upload coverage data
        uses: codecov/codecov-action@13ce06bfc6bbe3ecf90edbbf1bc32fe5978ca1d3 # v5.3.1
        with:
          fail_ci_if_error: true
          disable_search: true
          files: ./coverage/lcov.info
          token: ${{ secrets.CODECOV_TOKEN }}
          verbose: false
