name: "🐞 Agent TARS Bug Report"
description: "Report an issue with Agent TARS"
title: "[Bug Report]: "
type: Bug
labels: ["Agent TARS", "Bug"]
body:
- type: input
  attributes:
    label: Version
    description: |
      Please specify the version of Agent TARS CLI you are using when encountering the issue.
    placeholder: e.g. 1.0.0
  validations:
    required: true
- type: checkboxes
  attributes:
    label: Issue Type
    description: |
      Select the type of issue you are experiencing.
    options:
      - label: Select a issue type 👇
        required: false
      - label: Agent TARS Web UI (`@agent-tars/web-ui`)
      - label: Agent TARS CLI (`@agent-tars/server`)
      - label: Agent TARS Server (`@agent-tars/server`)
      - label: Agent TARS (`@agent-tars/core`)
      - label: MCP Agent (`@tarko/mcp-agent`)
      - label: Agent <PERSON> (`@tarko/agent`)
      - label: Other (please specify in description)
- type: checkboxes
  attributes:
    label: Model Provider
    description: |
      Select the planning model provider you were using when the issue occurred.
    options:
      - label: Select a model provider 👇
        required: false
      - label: Volcengine
      - label: Anthropic
      - label: OpenAI
      - label: Azure OpenAI
      - label: Other (please specify in description)
- type: textarea
  attributes:
    label: Problem Description
    description: |
      Please describe the issue, including both the expected behavior and the actual behavior. If possible, attach a link to relevant screenshots that illustrate the issue.
    placeholder: |
      Example:
      - Expected: The application should display search results.
      - Actual: The application shows an empty screen with no errors.
      [Attach actual screenshots here, if available.]
  validations:
    required: true
- type: textarea
  attributes:
    label: Error Logs
    description: |
      Please provide the error logs..
    placeholder: |
      Copy and paste the relevant logs here.
  validations:
    required: false
