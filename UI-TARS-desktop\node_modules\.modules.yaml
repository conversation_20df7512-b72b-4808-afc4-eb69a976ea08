hoistPattern:
  - '*'
hoistedLocations:
  '@actions/core@1.11.1':
    - node_modules\@actions\core
  '@actions/exec@1.1.1':
    - node_modules\@actions\exec
  '@actions/github@5.1.1(encoding@0.1.13)':
    - node_modules\@actions\github
  '@actions/http-client@2.2.3':
    - node_modules\@actions\http-client
  '@actions/io@1.1.3':
    - node_modules\@actions\io
  '@agent-infra/browser-context@0.1.2(bufferutil@4.0.9)(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5))(utf-8-validate@6.0.5)':
    - node_modules\@agent-infra\browser-context
  '@ampproject/remapping@2.3.0':
    - node_modules\@ampproject\remapping
  '@anthropic-ai/sdk@0.19.2(encoding@0.1.13)':
    - node_modules\@anthropic-ai\sdk
  '@anthropic-ai/sdk@0.27.3(encoding@0.1.13)':
    - node_modules\@browserbasehq\stagehand\node_modules\@anthropic-ai\sdk
  '@asamuzakjp/css-color@3.1.1':
    - node_modules\@asamuzakjp\css-color
  '@ast-grep/napi-win32-x64-msvc@0.35.0':
    - node_modules\@ast-grep\napi-win32-x64-msvc
  '@ast-grep/napi-win32-x64-msvc@0.36.0':
    - packages\common\configs\node_modules\@ast-grep\napi-win32-x64-msvc
  '@ast-grep/napi-win32-x64-msvc@0.37.0':
    - packages\common\electron-build\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\action-parser\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\cli\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\electron-ipc\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\operators\adb\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\operators\browser-operator\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\operators\browserbase\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\operators\nut-js\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\sdk\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\shared\node_modules\@ast-grep\napi-win32-x64-msvc
    - packages\ui-tars\utio\node_modules\@ast-grep\napi-win32-x64-msvc
  '@ast-grep/napi@0.35.0':
    - node_modules\@ast-grep\napi
  '@ast-grep/napi@0.36.0':
    - packages\common\configs\node_modules\@ast-grep\napi
  '@ast-grep/napi@0.37.0':
    - packages\common\electron-build\node_modules\@ast-grep\napi
    - packages\ui-tars\action-parser\node_modules\@ast-grep\napi
    - packages\ui-tars\cli\node_modules\@ast-grep\napi
    - packages\ui-tars\electron-ipc\node_modules\@ast-grep\napi
    - packages\ui-tars\operators\adb\node_modules\@ast-grep\napi
    - packages\ui-tars\operators\browser-operator\node_modules\@ast-grep\napi
    - packages\ui-tars\operators\browserbase\node_modules\@ast-grep\napi
    - packages\ui-tars\operators\nut-js\node_modules\@ast-grep\napi
    - packages\ui-tars\sdk\node_modules\@ast-grep\napi
    - packages\ui-tars\shared\node_modules\@ast-grep\napi
    - packages\ui-tars\utio\node_modules\@ast-grep\napi
  '@aws-crypto/crc32@5.2.0':
    - node_modules\@aws-crypto\crc32
  '@aws-crypto/sha256-browser@5.2.0':
    - node_modules\@aws-crypto\sha256-browser
  '@aws-crypto/sha256-js@5.2.0':
    - node_modules\@aws-crypto\sha256-js
  '@aws-crypto/supports-web-crypto@5.2.0':
    - node_modules\@aws-crypto\supports-web-crypto
  '@aws-crypto/util@5.2.0':
    - node_modules\@aws-crypto\util
  '@aws-sdk/client-bedrock-agent-runtime@3.765.0':
    - node_modules\@aws-sdk\client-bedrock-agent-runtime
  '@aws-sdk/client-bedrock-runtime@3.758.0':
    - node_modules\@aws-sdk\client-bedrock-runtime
  '@aws-sdk/client-kendra@3.758.0':
    - node_modules\@aws-sdk\client-kendra
  '@aws-sdk/client-sso@3.758.0':
    - node_modules\@aws-sdk\client-sso
  '@aws-sdk/core@3.758.0':
    - node_modules\@aws-sdk\core
  '@aws-sdk/credential-provider-env@3.758.0':
    - node_modules\@aws-sdk\credential-provider-env
  '@aws-sdk/credential-provider-http@3.758.0':
    - node_modules\@aws-sdk\credential-provider-http
  '@aws-sdk/credential-provider-ini@3.758.0':
    - node_modules\@aws-sdk\credential-provider-ini
  '@aws-sdk/credential-provider-node@3.758.0':
    - node_modules\@aws-sdk\credential-provider-node
  '@aws-sdk/credential-provider-process@3.758.0':
    - node_modules\@aws-sdk\credential-provider-process
  '@aws-sdk/credential-provider-sso@3.758.0':
    - node_modules\@aws-sdk\credential-provider-sso
  '@aws-sdk/credential-provider-web-identity@3.758.0':
    - node_modules\@aws-sdk\credential-provider-web-identity
  '@aws-sdk/middleware-host-header@3.734.0':
    - node_modules\@aws-sdk\middleware-host-header
  '@aws-sdk/middleware-logger@3.734.0':
    - node_modules\@aws-sdk\middleware-logger
  '@aws-sdk/middleware-recursion-detection@3.734.0':
    - node_modules\@aws-sdk\middleware-recursion-detection
  '@aws-sdk/middleware-user-agent@3.758.0':
    - node_modules\@aws-sdk\middleware-user-agent
  '@aws-sdk/nested-clients@3.758.0':
    - node_modules\@aws-sdk\nested-clients
  '@aws-sdk/region-config-resolver@3.734.0':
    - node_modules\@aws-sdk\region-config-resolver
  '@aws-sdk/token-providers@3.758.0':
    - node_modules\@aws-sdk\token-providers
  '@aws-sdk/types@3.734.0':
    - node_modules\@aws-sdk\types
  '@aws-sdk/types@3.804.0':
    - node_modules\@aws-crypto\sha256-js\node_modules\@aws-sdk\types
    - node_modules\@aws-crypto\sha256-browser\node_modules\@aws-sdk\types
    - node_modules\@aws-crypto\util\node_modules\@aws-sdk\types
    - node_modules\@aws-crypto\crc32\node_modules\@aws-sdk\types
  '@aws-sdk/util-endpoints@3.743.0':
    - node_modules\@aws-sdk\util-endpoints
  '@aws-sdk/util-locate-window@3.723.0':
    - node_modules\@aws-sdk\util-locate-window
  '@aws-sdk/util-user-agent-browser@3.734.0':
    - node_modules\@aws-sdk\util-user-agent-browser
  '@aws-sdk/util-user-agent-node@3.758.0':
    - node_modules\@aws-sdk\util-user-agent-node
  '@azu/format-text@1.0.2':
    - node_modules\@azu\format-text
  '@azu/style-format@1.0.1':
    - node_modules\@azu\style-format
  '@azure-rest/core-client@1.4.0':
    - node_modules\@azure-rest\core-client
  '@azure/abort-controller@2.1.2':
    - node_modules\@azure\abort-controller
  '@azure/core-auth@1.9.0':
    - node_modules\@azure\core-auth
  '@azure/core-rest-pipeline@1.19.0':
    - node_modules\@azure\core-rest-pipeline
  '@azure/core-sse@2.1.3':
    - node_modules\@azure\core-sse
  '@azure/core-tracing@1.2.0':
    - node_modules\@azure\core-tracing
  '@azure/core-util@1.11.0':
    - node_modules\@azure\core-util
  '@azure/logger@1.1.4':
    - node_modules\@azure\logger
  '@azure/openai@1.0.0-beta.13':
    - node_modules\@azure\openai
  '@babel/code-frame@7.26.2':
    - node_modules\@babel\code-frame
  '@babel/compat-data@7.26.8':
    - node_modules\@babel\compat-data
  '@babel/core@7.26.8':
    - node_modules\@babel\core
  '@babel/generator@7.26.8':
    - node_modules\@babel\generator
  '@babel/helper-compilation-targets@7.26.5':
    - node_modules\@babel\helper-compilation-targets
  '@babel/helper-module-imports@7.25.9':
    - node_modules\@babel\helper-module-imports
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.8)':
    - node_modules\@babel\helper-module-transforms
  '@babel/helper-plugin-utils@7.26.5':
    - node_modules\@babel\helper-plugin-utils
  '@babel/helper-string-parser@7.25.9':
    - node_modules\@babel\helper-string-parser
  '@babel/helper-validator-identifier@7.25.9':
    - node_modules\@babel\helper-validator-identifier
  '@babel/helper-validator-option@7.25.9':
    - node_modules\@babel\helper-validator-option
  '@babel/helpers@7.26.7':
    - node_modules\@babel\helpers
  '@babel/parser@7.26.8':
    - node_modules\@babel\parser
  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.8)':
    - node_modules\@babel\plugin-transform-arrow-functions
  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.8)':
    - node_modules\@babel\plugin-transform-react-jsx-self
  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.8)':
    - node_modules\@babel\plugin-transform-react-jsx-source
  '@babel/runtime@7.26.7':
    - node_modules\@babel\runtime
  '@babel/template@7.26.8':
    - node_modules\@babel\template
  '@babel/traverse@7.26.8':
    - node_modules\@babel\traverse
  '@babel/types@7.26.8':
    - node_modules\@babel\types
  '@bcoe/v8-coverage@1.0.2':
    - node_modules\@bcoe\v8-coverage
  '@browserbasehq/sdk@2.3.0(encoding@0.1.13)':
    - node_modules\@browserbasehq\sdk
  '@browserbasehq/stagehand@1.13.0(@playwright/test@1.50.1)(bufferutil@4.0.9)(deepmerge@4.3.1)(dotenv@16.4.7)(encoding@0.1.13)(openai@5.5.1(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.25.76))(utf-8-validate@6.0.5)(zod@3.25.76)':
    - node_modules\@browserbasehq\stagehand
  '@bufbuild/protobuf@2.2.3':
    - node_modules\@bufbuild\protobuf
  '@bundled-es-modules/cookie@2.0.1':
    - node_modules\@bundled-es-modules\cookie
  '@bundled-es-modules/statuses@1.0.1':
    - node_modules\@bundled-es-modules\statuses
  '@bundled-es-modules/tough-cookie@0.1.6':
    - node_modules\@bundled-es-modules\tough-cookie
  '@cfworker/json-schema@4.1.1':
    - node_modules\@cfworker\json-schema
  '@changesets/apply-release-plan@7.0.8':
    - node_modules\@changesets\apply-release-plan
  '@changesets/assemble-release-plan@6.0.5':
    - node_modules\@changesets\assemble-release-plan
  '@changesets/changelog-git@0.2.0':
    - node_modules\@changesets\changelog-git
  '@changesets/cli@2.27.12':
    - node_modules\@changesets\cli
  '@changesets/config@3.0.5':
    - node_modules\@changesets\config
  '@changesets/errors@0.2.0':
    - node_modules\@changesets\errors
  '@changesets/get-dependents-graph@2.1.2':
    - node_modules\@changesets\get-dependents-graph
  '@changesets/get-release-plan@4.0.6':
    - node_modules\@changesets\get-release-plan
  '@changesets/get-version-range-type@0.4.0':
    - node_modules\@changesets\get-version-range-type
  '@changesets/git@3.0.2':
    - node_modules\@changesets\git
  '@changesets/logger@0.1.1':
    - node_modules\@changesets\logger
  '@changesets/parse@0.4.0':
    - node_modules\@changesets\parse
  '@changesets/pre@2.0.1':
    - node_modules\@changesets\pre
  '@changesets/read@0.6.2':
    - node_modules\@changesets\read
  '@changesets/should-skip-package@0.1.1':
    - node_modules\@changesets\should-skip-package
  '@changesets/types@4.1.0':
    - node_modules\@manypkg\get-packages\node_modules\@changesets\types
  '@changesets/types@6.0.0':
    - node_modules\@changesets\types
  '@changesets/write@0.3.2':
    - node_modules\@changesets\write
  '@clack/core@0.3.5':
    - node_modules\@clack\core
  '@clack/core@0.4.1':
    - packages\ui-tars\cli\node_modules\@clack\core
  '@clack/core@0.5.0':
    - packages\agent-infra\create-new-mcp\node_modules\@clack\core
  '@clack/prompts@0.10.0':
    - packages\ui-tars\cli\node_modules\@clack\prompts
  '@clack/prompts@0.11.0':
    - packages\agent-infra\create-new-mcp\node_modules\@clack\prompts
  '@clack/prompts@0.6.3':
    - node_modules\@clack\prompts
  '@commitlint/cli@19.7.1(@types/node@20.17.17)(typescript@5.7.3)':
    - node_modules\@commitlint\cli
  '@commitlint/config-conventional@19.7.1':
    - node_modules\@commitlint\config-conventional
  '@commitlint/config-validator@19.5.0':
    - node_modules\@commitlint\config-validator
  '@commitlint/ensure@19.5.0':
    - node_modules\@commitlint\ensure
  '@commitlint/execute-rule@19.5.0':
    - node_modules\@commitlint\execute-rule
  '@commitlint/format@19.5.0':
    - node_modules\@commitlint\format
  '@commitlint/is-ignored@19.7.1':
    - node_modules\@commitlint\is-ignored
  '@commitlint/lint@19.7.1':
    - node_modules\@commitlint\lint
  '@commitlint/load@19.6.1(@types/node@20.17.17)(typescript@5.7.3)':
    - node_modules\@commitlint\load
  '@commitlint/message@19.5.0':
    - node_modules\@commitlint\message
  '@commitlint/parse@19.5.0':
    - node_modules\@commitlint\parse
  '@commitlint/read@19.5.0':
    - node_modules\@commitlint\read
  '@commitlint/resolve-extends@19.5.0':
    - node_modules\@commitlint\resolve-extends
  '@commitlint/rules@19.6.0':
    - node_modules\@commitlint\rules
  '@commitlint/to-lines@19.5.0':
    - node_modules\@commitlint\to-lines
  '@commitlint/top-level@19.5.0':
    - node_modules\@commitlint\top-level
  '@commitlint/types@19.5.0':
    - node_modules\@commitlint\types
  '@computer-use/default-clipboard-provider@4.2.0(@computer-use/nut-js@4.2.0(encoding@0.1.13))':
    - node_modules\@computer-use\default-clipboard-provider
  '@computer-use/libnut-darwin@2.7.1':
    - node_modules\@computer-use\libnut-darwin
  '@computer-use/libnut-linux@2.7.1':
    - node_modules\@computer-use\libnut-linux
  '@computer-use/libnut-win32@2.7.1':
    - node_modules\@computer-use\libnut-win32
  '@computer-use/libnut@4.2.0(@computer-use/nut-js@4.2.0(encoding@0.1.13))':
    - node_modules\@computer-use\libnut
  '@computer-use/mac-screen-capture-permissions@1.0.2':
    - node_modules\@computer-use\mac-screen-capture-permissions
  '@computer-use/node-mac-permissions@2.2.2':
    - node_modules\@computer-use\node-mac-permissions
  '@computer-use/nut-js@4.2.0(encoding@0.1.13)':
    - node_modules\@computer-use\nut-js
  '@computer-use/provider-interfaces@4.2.0(encoding@0.1.13)':
    - node_modules\@computer-use\provider-interfaces
  '@computer-use/shared@4.2.0(encoding@0.1.13)':
    - node_modules\@computer-use\shared
  '@cspotcode/source-map-support@0.8.1':
    - node_modules\@cspotcode\source-map-support
  '@csstools/color-helpers@5.0.2':
    - node_modules\@csstools\color-helpers
  '@csstools/css-calc@2.1.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    - node_modules\@csstools\css-calc
  '@csstools/css-color-parser@3.0.8(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    - node_modules\@csstools\css-color-parser
  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    - node_modules\@csstools\css-parser-algorithms
  '@csstools/css-tokenizer@3.0.3':
    - node_modules\@csstools\css-tokenizer
  '@dqbd/tiktoken@1.0.20':
    - node_modules\@dqbd\tiktoken
  '@electron-forge/cli@7.7.0(encoding@0.1.13)':
    - node_modules\@electron-forge\cli
  '@electron-forge/core-utils@7.7.0':
    - node_modules\@electron-forge\core-utils
  '@electron-forge/core@7.7.0(encoding@0.1.13)':
    - node_modules\@electron-forge\core
  '@electron-forge/maker-base@7.7.0':
    - node_modules\@electron-forge\maker-base
  '@electron-forge/maker-dmg@7.7.0':
    - node_modules\@electron-forge\maker-dmg
  '@electron-forge/maker-pkg@7.7.0':
    - node_modules\@electron-forge\maker-pkg
  '@electron-forge/maker-squirrel@7.7.0':
    - node_modules\@electron-forge\maker-squirrel
  '@electron-forge/maker-zip@7.7.0':
    - node_modules\@electron-forge\maker-zip
  '@electron-forge/plugin-auto-unpack-natives@7.7.0':
    - node_modules\@electron-forge\plugin-auto-unpack-natives
  '@electron-forge/plugin-base@7.7.0':
    - node_modules\@electron-forge\plugin-base
  '@electron-forge/plugin-fuses@7.7.0(@electron/fuses@1.8.0)':
    - node_modules\@electron-forge\plugin-fuses
  '@electron-forge/plugin-vite@7.7.0(bufferutil@4.0.9)(utf-8-validate@6.0.5)':
    - node_modules\@electron-forge\plugin-vite
  '@electron-forge/publisher-base@7.7.0':
    - node_modules\@electron-forge\publisher-base
  '@electron-forge/publisher-github@7.7.0(encoding@0.1.13)':
    - node_modules\@electron-forge\publisher-github
  '@electron-forge/shared-types@7.7.0':
    - node_modules\@electron-forge\shared-types
  '@electron-forge/shared-types@7.8.0':
    - packages\common\electron-build\node_modules\@electron-forge\shared-types
  '@electron-forge/template-base@7.7.0':
    - node_modules\@electron-forge\template-base
  '@electron-forge/template-vite-typescript@7.7.0':
    - node_modules\@electron-forge\template-vite-typescript
  '@electron-forge/template-vite@7.7.0':
    - node_modules\@electron-forge\template-vite
  '@electron-forge/template-webpack-typescript@7.7.0':
    - node_modules\@electron-forge\template-webpack-typescript
  '@electron-forge/template-webpack@7.7.0':
    - node_modules\@electron-forge\template-webpack
  '@electron-forge/tracer@7.7.0':
    - node_modules\@electron-forge\tracer
  '@electron-forge/tracer@7.8.0':
    - packages\common\electron-build\node_modules\@electron-forge\tracer
  '@electron-forge/web-multi-logger@7.7.0(bufferutil@4.0.9)(utf-8-validate@6.0.5)':
    - node_modules\@electron-forge\web-multi-logger
  '@electron-toolkit/eslint-config-prettier@2.0.0(eslint@8.57.1)(prettier@3.5.0)':
    - node_modules\@electron-toolkit\eslint-config-prettier
  '@electron-toolkit/eslint-config-ts@2.0.0(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@electron-toolkit\eslint-config-ts
  '@electron-toolkit/preload@3.0.1(electron@34.1.1)':
    - node_modules\@electron-toolkit\preload
  '@electron-toolkit/tsconfig@1.0.1(@types/node@20.17.17)':
    - node_modules\@electron-toolkit\tsconfig
  '@electron-toolkit/utils@3.0.0(electron@34.1.1)':
    - node_modules\@electron-toolkit\utils
  '@electron/asar@3.3.1':
    - node_modules\@electron\asar
  '@electron/fuses@1.8.0':
    - node_modules\@electron\fuses
  '@electron/get@2.0.3':
    - node_modules\electron\node_modules\@electron\get
    - packages\ui-tars\electron-ipc\node_modules\@electron\get
  '@electron/get@3.1.0':
    - node_modules\@electron\get
  '@electron/node-gyp@https://codeload.github.com/electron/node-gyp/tar.gz/06b29aafb7708acef8b3669835c8a7857ebc92d2':
    - node_modules\@electron\node-gyp
  '@electron/notarize@2.5.0':
    - node_modules\@electron\notarize
  '@electron/osx-sign@1.3.2':
    - node_modules\@electron\osx-sign
  '@electron/packager@18.3.6':
    - node_modules\@electron\packager
  '@electron/rebuild@3.7.1':
    - node_modules\@electron\rebuild
  '@electron/universal@2.0.1':
    - node_modules\@electron\universal
  '@electron/windows-sign@1.2.0':
    - node_modules\@electron\windows-sign
  '@emotion/babel-plugin@11.13.5':
    - node_modules\@emotion\babel-plugin
  '@emotion/cache@11.14.0':
    - node_modules\@emotion\cache
  '@emotion/hash@0.9.2':
    - node_modules\@emotion\hash
  '@emotion/is-prop-valid@1.3.1':
    - node_modules\@emotion\is-prop-valid
  '@emotion/memoize@0.9.0':
    - node_modules\@emotion\memoize
  '@emotion/react@11.14.0(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@emotion\react
  '@emotion/serialize@1.3.3':
    - node_modules\@emotion\serialize
  '@emotion/sheet@1.4.0':
    - node_modules\@emotion\sheet
  '@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.2)(react@19.1.0))(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@emotion\styled
  '@emotion/unitless@0.10.0':
    - node_modules\@emotion\unitless
  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@19.1.0)':
    - node_modules\@emotion\use-insertion-effect-with-fallbacks
  '@emotion/utils@1.4.2':
    - node_modules\@emotion\utils
  '@emotion/weak-memoize@0.4.0':
    - node_modules\@emotion\weak-memoize
  '@esbuild/win32-x64@0.23.1':
    - node_modules\tsx\node_modules\@esbuild\win32-x64
  '@esbuild/win32-x64@0.24.2':
    - node_modules\electron-vite\node_modules\@esbuild\win32-x64
  '@esbuild/win32-x64@0.25.0':
    - node_modules\@esbuild\win32-x64
  '@eslint-community/eslint-utils@4.4.1(eslint@8.57.1)':
    - node_modules\@eslint-community\eslint-utils
  '@eslint-community/regexpp@4.12.1':
    - node_modules\@eslint-community\regexpp
  '@eslint/eslintrc@2.1.4':
    - node_modules\@eslint\eslintrc
  '@eslint/js@8.57.1':
    - node_modules\@eslint\js
  '@fastify/busboy@2.1.1':
    - node_modules\@fastify\busboy
  '@floating-ui/core@1.6.9':
    - node_modules\@floating-ui\core
  '@floating-ui/dom@1.6.13':
    - node_modules\@floating-ui\dom
  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@floating-ui\react-dom
  '@floating-ui/utils@0.2.9':
    - node_modules\@floating-ui\utils
  '@gar/promisify@1.1.3':
    - node_modules\@gar\promisify
  '@ghostery/adblocker-content@2.5.2':
    - node_modules\@ghostery\adblocker-content
  '@ghostery/adblocker-extended-selectors@2.5.2':
    - node_modules\@ghostery\adblocker-extended-selectors
  '@ghostery/adblocker-puppeteer@2.5.2(puppeteer@24.4.0(bufferutil@4.0.9)(typescript@5.7.3)(utf-8-validate@6.0.5))':
    - node_modules\@ghostery\adblocker-puppeteer
  '@ghostery/adblocker@2.5.2':
    - node_modules\@ghostery\adblocker
  '@google/generative-ai@0.11.5':
    - node_modules\@google\generative-ai
  '@hookform/resolvers@5.0.1(react-hook-form@7.46.1(react@19.1.0))':
    - node_modules\@hookform\resolvers
  '@humanwhocodes/config-array@0.13.0':
    - node_modules\@humanwhocodes\config-array
  '@humanwhocodes/module-importer@1.0.1':
    - node_modules\@humanwhocodes\module-importer
  '@humanwhocodes/object-schema@2.0.3':
    - node_modules\@humanwhocodes\object-schema
  '@img/sharp-win32-x64@0.33.3':
    - node_modules\@img\sharp-win32-x64
  '@inquirer/confirm@5.1.6(@types/node@20.17.17)':
    - node_modules\@inquirer\confirm
  '@inquirer/core@10.1.7(@types/node@20.17.17)':
    - node_modules\@inquirer\core
  '@inquirer/figures@1.0.10':
    - node_modules\@inquirer\figures
  '@inquirer/type@3.0.4(@types/node@20.17.17)':
    - node_modules\@inquirer\type
  '@isaacs/cliui@8.0.2':
    - node_modules\@isaacs\cliui
  '@istanbuljs/schema@0.1.3':
    - node_modules\@istanbuljs\schema
  '@jimp/bmp@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\bmp
  '@jimp/core@0.22.12(encoding@0.1.13)':
    - node_modules\@jimp\custom\node_modules\@jimp\core
  '@jimp/core@1.6.0':
    - node_modules\@jimp\core
  '@jimp/custom@0.22.12(encoding@0.1.13)':
    - node_modules\@jimp\custom
  '@jimp/diff@1.6.0':
    - node_modules\@jimp\diff
  '@jimp/file-ops@1.6.0':
    - node_modules\@jimp\file-ops
  '@jimp/gif@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\gif
  '@jimp/jpeg@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\jpeg
  '@jimp/js-bmp@1.6.0':
    - node_modules\@jimp\js-bmp
  '@jimp/js-gif@1.6.0':
    - node_modules\@jimp\js-gif
  '@jimp/js-jpeg@1.6.0':
    - node_modules\@jimp\js-jpeg
  '@jimp/js-png@1.6.0':
    - node_modules\@jimp\js-png
  '@jimp/js-tiff@1.6.0':
    - node_modules\@jimp\js-tiff
  '@jimp/plugin-blit@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-blit
  '@jimp/plugin-blit@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-blit
  '@jimp/plugin-blur@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-blur
  '@jimp/plugin-blur@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-blur
  '@jimp/plugin-circle@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-circle
  '@jimp/plugin-circle@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-circle
  '@jimp/plugin-color@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugins\node_modules\@jimp\plugin-color
    - node_modules\@jimp\plugin-threshold\node_modules\@jimp\plugin-color
  '@jimp/plugin-color@1.6.0':
    - node_modules\@jimp\plugin-color
  '@jimp/plugin-contain@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-blit@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-scale@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))))':
    - node_modules\@jimp\plugin-contain
  '@jimp/plugin-contain@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-contain
  '@jimp/plugin-cover@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-crop@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-scale@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))))':
    - node_modules\@jimp\plugin-cover
  '@jimp/plugin-cover@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-cover
  '@jimp/plugin-crop@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-crop
  '@jimp/plugin-crop@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-crop
  '@jimp/plugin-displace@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-displace
  '@jimp/plugin-displace@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-displace
  '@jimp/plugin-dither@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-dither
  '@jimp/plugin-dither@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-dither
  '@jimp/plugin-fisheye@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-fisheye
  '@jimp/plugin-fisheye@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-fisheye
  '@jimp/plugin-flip@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-rotate@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-blit@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-crop@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))))':
    - node_modules\@jimp\plugin-flip
  '@jimp/plugin-flip@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-flip
  '@jimp/plugin-gaussian@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-gaussian
  '@jimp/plugin-hash@1.6.0':
    - node_modules\@jimp\plugin-hash
  '@jimp/plugin-invert@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-invert
  '@jimp/plugin-mask@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-mask
  '@jimp/plugin-mask@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-mask
  '@jimp/plugin-normalize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-normalize
  '@jimp/plugin-print@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-blit@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))':
    - node_modules\@jimp\plugin-print
  '@jimp/plugin-print@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-print
  '@jimp/plugin-quantize@1.6.0':
    - node_modules\@jimp\plugin-quantize
  '@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugin-resize
  '@jimp/plugin-resize@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-resize
    - node_modules\@jimp\diff\node_modules\@jimp\plugin-resize
    - node_modules\@jimp\plugin-hash\node_modules\@jimp\plugin-resize
  '@jimp/plugin-rotate@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-blit@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-crop@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))':
    - node_modules\@jimp\plugin-rotate
  '@jimp/plugin-rotate@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-rotate
  '@jimp/plugin-scale@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))':
    - node_modules\@jimp\plugin-scale
  '@jimp/plugin-shadow@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-blur@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))':
    - node_modules\@jimp\plugin-shadow
  '@jimp/plugin-threshold@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))(@jimp/plugin-color@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))(@jimp/plugin-resize@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13)))':
    - node_modules\@jimp\plugin-threshold
  '@jimp/plugin-threshold@1.6.0':
    - node_modules\jimp\node_modules\@jimp\plugin-threshold
  '@jimp/plugins@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\plugins
  '@jimp/png@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\png
  '@jimp/tiff@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@jimp\tiff
  '@jimp/types@0.22.12(@jimp/custom@0.22.12(encoding@0.1.13))':
    - node_modules\@computer-use\nut-js\node_modules\@jimp\types
    - node_modules\@computer-use\shared\node_modules\@jimp\types
  '@jimp/types@1.6.0':
    - node_modules\@jimp\types
  '@jimp/utils@0.22.12':
    - node_modules\@jimp\utils
  '@jimp/utils@1.6.0':
    - node_modules\jimp\node_modules\@jimp\utils
    - node_modules\@jimp\js-bmp\node_modules\@jimp\utils
    - node_modules\@jimp\diff\node_modules\@jimp\utils
    - node_modules\@jimp\core\node_modules\@jimp\utils
    - node_modules\@jimp\plugin-color\node_modules\@jimp\utils
    - node_modules\@jimp\plugin-hash\node_modules\@jimp\utils
  '@jridgewell/gen-mapping@0.3.8':
    - node_modules\@jridgewell\gen-mapping
  '@jridgewell/resolve-uri@3.1.2':
    - node_modules\@jridgewell\resolve-uri
  '@jridgewell/set-array@1.2.1':
    - node_modules\@jridgewell\set-array
  '@jridgewell/sourcemap-codec@1.5.0':
    - node_modules\@jridgewell\sourcemap-codec
  '@jridgewell/trace-mapping@0.3.25':
    - node_modules\@jridgewell\trace-mapping
  '@jridgewell/trace-mapping@0.3.9':
    - node_modules\@cspotcode\source-map-support\node_modules\@jridgewell\trace-mapping
  '@langchain/aws@0.1.6(@langchain/core@0.3.42(openai@4.87.3(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.24.2)))':
    - node_modules\@langchain\aws
  '@langchain/core@0.3.42(openai@4.87.3(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.24.2))':
    - node_modules\@langchain\core
  '@langchain/openai@0.4.4(@langchain/core@0.3.42(openai@4.87.3(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.24.2)))(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))':
    - node_modules\@langchain\openai
  '@malept/cross-spawn-promise@2.0.0':
    - node_modules\@malept\cross-spawn-promise
  '@manypkg/find-root@1.1.0':
    - node_modules\@manypkg\find-root
  '@manypkg/get-packages@1.1.3':
    - node_modules\@manypkg\get-packages
  '@mistralai/mistralai@1.5.1(zod@3.24.2)':
    - node_modules\@mistralai\mistralai
  '@mixmark-io/domino@2.2.0':
    - node_modules\@mixmark-io\domino
  '@modelcontextprotocol/sdk@1.15.1':
    - node_modules\@modelcontextprotocol\sdk
  '@module-federation/error-codes@0.15.0':
    - node_modules\@module-federation\error-codes
  '@module-federation/error-codes@0.8.4':
    - node_modules\@module-federation\runtime\node_modules\@module-federation\error-codes
  '@module-federation/runtime-core@0.15.0':
    - node_modules\@module-federation\runtime-core
  '@module-federation/runtime-tools@0.15.0':
    - packages\common\electron-build\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\action-parser\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\cli\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\electron-ipc\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\operators\adb\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\operators\browser-operator\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\operators\browserbase\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\operators\nut-js\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\sdk\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\shared\node_modules\@module-federation\runtime-tools
    - packages\ui-tars\utio\node_modules\@module-federation\runtime-tools
  '@module-federation/runtime-tools@0.8.4':
    - node_modules\@module-federation\runtime-tools
  '@module-federation/runtime@0.15.0':
    - packages\common\electron-build\node_modules\@module-federation\runtime
    - packages\ui-tars\action-parser\node_modules\@module-federation\runtime
    - packages\ui-tars\cli\node_modules\@module-federation\runtime
    - packages\ui-tars\electron-ipc\node_modules\@module-federation\runtime
    - packages\ui-tars\operators\adb\node_modules\@module-federation\runtime
    - packages\ui-tars\operators\browser-operator\node_modules\@module-federation\runtime
    - packages\ui-tars\operators\browserbase\node_modules\@module-federation\runtime
    - packages\ui-tars\operators\nut-js\node_modules\@module-federation\runtime
    - packages\ui-tars\sdk\node_modules\@module-federation\runtime
    - packages\ui-tars\shared\node_modules\@module-federation\runtime
    - packages\ui-tars\utio\node_modules\@module-federation\runtime
  '@module-federation/runtime@0.8.4':
    - node_modules\@module-federation\runtime
  '@module-federation/sdk@0.15.0':
    - node_modules\@module-federation\sdk
  '@module-federation/sdk@0.8.4':
    - node_modules\@module-federation\runtime\node_modules\@module-federation\sdk
    - node_modules\@module-federation\webpack-bundler-runtime\node_modules\@module-federation\sdk
  '@module-federation/webpack-bundler-runtime@0.15.0':
    - packages\common\electron-build\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\action-parser\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\cli\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\electron-ipc\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\operators\adb\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\operators\browser-operator\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\operators\browserbase\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\operators\nut-js\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\sdk\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\shared\node_modules\@module-federation\webpack-bundler-runtime
    - packages\ui-tars\utio\node_modules\@module-federation\webpack-bundler-runtime
  '@module-federation/webpack-bundler-runtime@0.8.4':
    - node_modules\@module-federation\webpack-bundler-runtime
  '@mozilla/readability@0.5.0':
    - node_modules\@mozilla\readability
  '@mswjs/interceptors@0.37.6':
    - node_modules\@mswjs\interceptors
  '@nodelib/fs.scandir@2.1.5':
    - node_modules\@nodelib\fs.scandir
  '@nodelib/fs.stat@2.0.5':
    - node_modules\@nodelib\fs.stat
  '@nodelib/fs.walk@1.2.8':
    - node_modules\@nodelib\fs.walk
  '@npmcli/fs@2.1.2':
    - node_modules\@npmcli\fs
  '@npmcli/move-file@2.0.1':
    - node_modules\@npmcli\move-file
  '@octokit/auth-token@2.5.0':
    - node_modules\@octokit\auth-token
  '@octokit/core@3.6.0(encoding@0.1.13)':
    - node_modules\@octokit\core
  '@octokit/endpoint@6.0.12':
    - node_modules\@octokit\endpoint
  '@octokit/graphql@4.8.0(encoding@0.1.13)':
    - node_modules\@octokit\graphql
  '@octokit/openapi-types@12.11.0':
    - node_modules\@octokit\openapi-types
  '@octokit/openapi-types@23.0.1':
    - node_modules\@electron-forge\publisher-github\node_modules\@octokit\openapi-types
  '@octokit/plugin-paginate-rest@2.21.3(@octokit/core@3.6.0(encoding@0.1.13))':
    - node_modules\@octokit\plugin-paginate-rest
  '@octokit/plugin-request-log@1.0.4(@octokit/core@3.6.0(encoding@0.1.13))':
    - node_modules\@octokit\plugin-request-log
  '@octokit/plugin-rest-endpoint-methods@5.16.2(@octokit/core@3.6.0(encoding@0.1.13))':
    - node_modules\@octokit\plugin-rest-endpoint-methods
  '@octokit/plugin-retry@3.0.9':
    - node_modules\@octokit\plugin-retry
  '@octokit/request-error@2.1.0':
    - node_modules\@octokit\request-error
  '@octokit/request-error@5.1.1':
    - node_modules\@electron-forge\publisher-github\node_modules\@octokit\request-error
  '@octokit/request@5.6.3(encoding@0.1.13)':
    - node_modules\@octokit\request
  '@octokit/rest@18.12.0(encoding@0.1.13)':
    - node_modules\@octokit\rest
  '@octokit/types@13.8.0':
    - node_modules\@electron-forge\publisher-github\node_modules\@octokit\request-error\node_modules\@octokit\types
  '@octokit/types@6.41.0':
    - node_modules\@octokit\types
  '@octokit/webhooks-schemas@6.11.0':
    - node_modules\@octokit\webhooks-schemas
  '@octokit/webhooks-types@6.11.0':
    - node_modules\@octokit\webhooks-types
  '@open-draft/deferred-promise@2.2.0':
    - node_modules\@open-draft\deferred-promise
  '@open-draft/logger@0.3.0':
    - node_modules\@open-draft\logger
  '@open-draft/until@2.1.0':
    - node_modules\@open-draft\until
  '@parcel/watcher-win32-x64@2.5.1':
    - node_modules\@parcel\watcher-win32-x64
  '@parcel/watcher@2.5.1':
    - node_modules\@parcel\watcher
  '@pkgjs/parseargs@0.11.0':
    - node_modules\@pkgjs\parseargs
  '@pkgr/core@0.1.1':
    - node_modules\@pkgr\core
  '@playwright/test@1.50.1':
    - node_modules\@playwright\test
  '@polka/url@1.0.0-next.28':
    - node_modules\@polka\url
  '@puppeteer/browsers@2.10.2':
    - node_modules\puppeteer-core\node_modules\@puppeteer\browsers
  '@puppeteer/browsers@2.10.6':
    - node_modules\@agent-infra\browser-context\node_modules\@puppeteer\browsers
  '@puppeteer/browsers@2.8.0':
    - node_modules\@puppeteer\browsers
  '@radix-ui/number@1.1.1':
    - node_modules\@radix-ui\number
  '@radix-ui/primitive@1.1.2':
    - node_modules\@radix-ui\primitive
  '@radix-ui/react-alert-dialog@1.1.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-alert-dialog
  '@radix-ui/react-arrow@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-arrow
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-arrow
  '@radix-ui/react-avatar@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-avatar
  '@radix-ui/react-checkbox@1.3.2(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-checkbox
  '@radix-ui/react-collapsible@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-collapsible
  '@radix-ui/react-collection@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-collection
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-compose-refs
  '@radix-ui/react-context@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-context
  '@radix-ui/react-dialog@1.1.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-dialog
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-direction
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-dismissable-layer
  '@radix-ui/react-dismissable-layer@1.1.6(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-dismissable-layer
  '@radix-ui/react-dropdown-menu@2.1.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-dropdown-menu
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-focus-guards
  '@radix-ui/react-focus-scope@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-focus-scope
  '@radix-ui/react-hover-card@1.1.14(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-hover-card
  '@radix-ui/react-id@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-id
  '@radix-ui/react-label@2.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-label
  '@radix-ui/react-menu@2.1.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-menu
  '@radix-ui/react-popper@1.2.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-popper
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-popper
  '@radix-ui/react-portal@1.1.5(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-portal
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-portal
  '@radix-ui/react-presence@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-presence
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-checkbox\node_modules\@radix-ui\react-presence
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-presence
  '@radix-ui/react-primitive@2.0.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-primitive
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-checkbox\node_modules\@radix-ui\react-primitive
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-primitive
  '@radix-ui/react-roving-focus@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-roving-focus
  '@radix-ui/react-scroll-area@1.2.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-scroll-area
  '@radix-ui/react-select@2.1.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-select
  '@radix-ui/react-separator@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-separator
  '@radix-ui/react-slider@1.2.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-slider
  '@radix-ui/react-slot@1.2.0(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-slot
  '@radix-ui/react-slot@1.2.3(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-checkbox\node_modules\@radix-ui\react-slot
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-slot
  '@radix-ui/react-switch@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-switch
  '@radix-ui/react-tabs@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-tabs
  '@radix-ui/react-tooltip@1.2.0(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-tooltip
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-callback-ref
  '@radix-ui/react-use-controllable-state@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-controllable-state
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-checkbox\node_modules\@radix-ui\react-use-controllable-state
    - node_modules\@radix-ui\react-hover-card\node_modules\@radix-ui\react-use-controllable-state
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-effect-event
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-escape-keydown
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-layout-effect
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-previous
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-rect
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    - node_modules\@radix-ui\react-use-size
  '@radix-ui/react-visually-hidden@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    - node_modules\@radix-ui\react-visually-hidden
  '@radix-ui/rect@1.1.1':
    - node_modules\@radix-ui\rect
  '@remusao/guess-url-type@2.0.0':
    - node_modules\@remusao\guess-url-type
  '@remusao/small@2.0.0':
    - node_modules\@remusao\small
  '@remusao/smaz-compress@2.1.0':
    - node_modules\@remusao\smaz-compress
  '@remusao/smaz-decompress@2.1.0':
    - node_modules\@remusao\smaz-decompress
  '@remusao/smaz@2.1.0':
    - node_modules\@remusao\smaz
  '@remusao/trie@2.0.0':
    - node_modules\@remusao\trie
  '@rollup/rollup-win32-x64-msvc@4.34.6':
    - node_modules\@rollup\rollup-win32-x64-msvc
  '@rsbuild/core@1.2.19':
    - node_modules\@rsbuild\core
  '@rsbuild/core@1.4.0-beta.3':
    - packages\common\electron-build\node_modules\@rsbuild\core
    - packages\ui-tars\action-parser\node_modules\@rsbuild\core
    - packages\ui-tars\cli\node_modules\@rsbuild\core
    - packages\ui-tars\electron-ipc\node_modules\@rsbuild\core
    - packages\ui-tars\operators\adb\node_modules\@rsbuild\core
    - packages\ui-tars\operators\browser-operator\node_modules\@rsbuild\core
    - packages\ui-tars\operators\browserbase\node_modules\@rsbuild\core
    - packages\ui-tars\operators\nut-js\node_modules\@rsbuild\core
    - packages\ui-tars\sdk\node_modules\@rsbuild\core
    - packages\ui-tars\shared\node_modules\@rsbuild\core
    - packages\ui-tars\utio\node_modules\@rsbuild\core
  '@rslib/core@0.10.0(typescript@5.7.3)':
    - packages\common\electron-build\node_modules\@rslib\core
    - packages\ui-tars\action-parser\node_modules\@rslib\core
    - packages\ui-tars\cli\node_modules\@rslib\core
    - packages\ui-tars\electron-ipc\node_modules\@rslib\core
    - packages\ui-tars\operators\adb\node_modules\@rslib\core
    - packages\ui-tars\operators\browser-operator\node_modules\@rslib\core
    - packages\ui-tars\operators\browserbase\node_modules\@rslib\core
    - packages\ui-tars\operators\nut-js\node_modules\@rslib\core
    - packages\ui-tars\sdk\node_modules\@rslib\core
    - packages\ui-tars\shared\node_modules\@rslib\core
    - packages\ui-tars\utio\node_modules\@rslib\core
  '@rslib/core@0.5.3(typescript@5.7.3)':
    - node_modules\@rslib\core
  '@rslib/core@0.5.4(typescript@5.7.3)':
    - packages\common\configs\node_modules\@rslib\core
  '@rspack/binding-win32-x64-msvc@1.2.8':
    - node_modules\@rspack\binding-win32-x64-msvc
  '@rspack/binding-win32-x64-msvc@1.4.0-beta.0':
    - packages\common\electron-build\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\action-parser\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\cli\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\electron-ipc\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\operators\adb\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\operators\browser-operator\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\operators\browserbase\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\operators\nut-js\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\sdk\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\shared\node_modules\@rspack\binding-win32-x64-msvc
    - packages\ui-tars\utio\node_modules\@rspack\binding-win32-x64-msvc
  '@rspack/binding@1.2.8':
    - node_modules\@rspack\binding
  '@rspack/binding@1.4.0-beta.0':
    - packages\common\electron-build\node_modules\@rspack\binding
    - packages\ui-tars\action-parser\node_modules\@rspack\binding
    - packages\ui-tars\cli\node_modules\@rspack\binding
    - packages\ui-tars\electron-ipc\node_modules\@rspack\binding
    - packages\ui-tars\operators\adb\node_modules\@rspack\binding
    - packages\ui-tars\operators\browser-operator\node_modules\@rspack\binding
    - packages\ui-tars\operators\browserbase\node_modules\@rspack\binding
    - packages\ui-tars\operators\nut-js\node_modules\@rspack\binding
    - packages\ui-tars\sdk\node_modules\@rspack\binding
    - packages\ui-tars\shared\node_modules\@rspack\binding
    - packages\ui-tars\utio\node_modules\@rspack\binding
  '@rspack/core@1.2.8(@swc/helpers@0.5.17)':
    - node_modules\@rspack\core
  '@rspack/core@1.4.0-beta.0(@swc/helpers@0.5.17)':
    - packages\common\electron-build\node_modules\@rspack\core
    - packages\ui-tars\action-parser\node_modules\@rspack\core
    - packages\ui-tars\cli\node_modules\@rspack\core
    - packages\ui-tars\electron-ipc\node_modules\@rspack\core
    - packages\ui-tars\operators\adb\node_modules\@rspack\core
    - packages\ui-tars\operators\browser-operator\node_modules\@rspack\core
    - packages\ui-tars\operators\browserbase\node_modules\@rspack\core
    - packages\ui-tars\operators\nut-js\node_modules\@rspack\core
    - packages\ui-tars\sdk\node_modules\@rspack\core
    - packages\ui-tars\shared\node_modules\@rspack\core
    - packages\ui-tars\utio\node_modules\@rspack\core
  '@rspack/lite-tapable@1.0.1':
    - node_modules\@rspack\lite-tapable
  '@rtsao/scc@1.1.0':
    - node_modules\@rtsao\scc
  '@secretlint/config-creator@10.2.1':
    - node_modules\@secretlint\config-creator
  '@secretlint/config-loader@10.2.1':
    - node_modules\@secretlint\config-loader
  '@secretlint/core@10.2.1':
    - node_modules\@secretlint\core
  '@secretlint/formatter@10.2.1':
    - node_modules\@secretlint\formatter
  '@secretlint/node@10.2.1':
    - node_modules\@secretlint\node
  '@secretlint/profiler@10.2.1':
    - node_modules\@secretlint\profiler
  '@secretlint/resolver@10.2.1':
    - node_modules\@secretlint\resolver
  '@secretlint/secretlint-rule-1password@10.2.1':
    - node_modules\@secretlint\secretlint-rule-1password
  '@secretlint/secretlint-rule-anthropic@10.2.1':
    - node_modules\@secretlint\secretlint-rule-anthropic
  '@secretlint/secretlint-rule-aws@10.2.1':
    - node_modules\@secretlint\secretlint-rule-aws
  '@secretlint/secretlint-rule-azure@10.2.1':
    - node_modules\@secretlint\secretlint-rule-azure
  '@secretlint/secretlint-rule-basicauth@10.2.1':
    - node_modules\@secretlint\secretlint-rule-basicauth
  '@secretlint/secretlint-rule-database-connection-string@10.2.1':
    - node_modules\@secretlint\secretlint-rule-database-connection-string
  '@secretlint/secretlint-rule-filter-comments@10.2.1':
    - node_modules\@secretlint\secretlint-rule-filter-comments
  '@secretlint/secretlint-rule-gcp@10.2.1':
    - node_modules\@secretlint\secretlint-rule-gcp
  '@secretlint/secretlint-rule-github@10.2.1':
    - node_modules\@secretlint\secretlint-rule-github
  '@secretlint/secretlint-rule-no-dotenv@10.2.1':
    - node_modules\@secretlint\secretlint-rule-no-dotenv
  '@secretlint/secretlint-rule-no-homedir@10.2.1':
    - node_modules\@secretlint\secretlint-rule-no-homedir
  '@secretlint/secretlint-rule-no-k8s-kind-secret@10.2.1':
    - node_modules\@secretlint\secretlint-rule-no-k8s-kind-secret
  '@secretlint/secretlint-rule-npm@10.2.1':
    - node_modules\@secretlint\secretlint-rule-npm
  '@secretlint/secretlint-rule-openai@10.2.1':
    - node_modules\@secretlint\secretlint-rule-openai
  '@secretlint/secretlint-rule-pattern@10.2.1':
    - node_modules\@secretlint\secretlint-rule-pattern
  '@secretlint/secretlint-rule-privatekey@10.2.1':
    - node_modules\@secretlint\secretlint-rule-privatekey
  '@secretlint/secretlint-rule-secp256k1-privatekey@10.2.1':
    - node_modules\@secretlint\secretlint-rule-secp256k1-privatekey
  '@secretlint/source-creator@10.2.1':
    - node_modules\@secretlint\source-creator
  '@secretlint/tester@10.2.1':
    - node_modules\@secretlint\tester
  '@secretlint/types@10.2.1':
    - node_modules\@secretlint\types
  '@sindresorhus/is@4.6.0':
    - node_modules\@sindresorhus\is
  '@sindresorhus/merge-streams@2.3.0':
    - node_modules\@sindresorhus\merge-streams
  '@smithy/abort-controller@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\node-http-handler\node_modules\@smithy\abort-controller
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\abort-controller
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\abort-controller
  '@smithy/abort-controller@4.0.2':
    - node_modules\@smithy\abort-controller
  '@smithy/config-resolver@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\config-resolver
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\config-resolver
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\config-resolver
  '@smithy/config-resolver@4.1.2':
    - node_modules\@smithy\config-resolver
  '@smithy/core@3.1.5':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\core
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\core
  '@smithy/core@3.3.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\core
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\core
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\core
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\core
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\core
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\core
    - node_modules\@smithy\core
  '@smithy/credential-provider-imds@4.0.1':
    - node_modules\@smithy\credential-provider-imds
  '@smithy/credential-provider-imds@4.0.4':
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\credential-provider-imds
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\credential-provider-imds
  '@smithy/eventstream-codec@4.0.1':
    - node_modules\@smithy\eventstream-codec
  '@smithy/eventstream-serde-browser@4.0.1':
    - node_modules\@smithy\eventstream-serde-browser
  '@smithy/eventstream-serde-config-resolver@4.0.1':
    - node_modules\@smithy\eventstream-serde-config-resolver
  '@smithy/eventstream-serde-node@4.0.1':
    - node_modules\@smithy\eventstream-serde-node
  '@smithy/eventstream-serde-universal@4.0.1':
    - node_modules\@smithy\eventstream-serde-universal
  '@smithy/fetch-http-handler@5.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\fetch-http-handler
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\fetch-http-handler
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\fetch-http-handler
  '@smithy/fetch-http-handler@5.0.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core\node_modules\@smithy\fetch-http-handler
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-stream\node_modules\@smithy\fetch-http-handler
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\fetch-http-handler
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\fetch-http-handler
    - node_modules\@smithy\fetch-http-handler
  '@smithy/hash-node@4.0.1':
    - node_modules\@smithy\hash-node
  '@smithy/hash-node@4.0.2':
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\hash-node
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\hash-node
  '@smithy/invalid-dependency@4.0.1':
    - node_modules\@smithy\invalid-dependency
  '@smithy/invalid-dependency@4.0.2':
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\invalid-dependency
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\invalid-dependency
  '@smithy/is-array-buffer@2.2.0':
    - node_modules\@aws-crypto\sha256-browser\node_modules\@smithy\is-array-buffer
    - node_modules\@aws-crypto\util\node_modules\@smithy\is-array-buffer
  '@smithy/is-array-buffer@4.0.0':
    - node_modules\@smithy\is-array-buffer
  '@smithy/middleware-content-length@4.0.1':
    - node_modules\@smithy\middleware-content-length
  '@smithy/middleware-content-length@4.0.2':
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\middleware-content-length
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\middleware-content-length
  '@smithy/middleware-endpoint@4.0.6':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint
  '@smithy/middleware-endpoint@4.1.4':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-endpoint
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-endpoint
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-endpoint
    - node_modules\@smithy\middleware-endpoint
  '@smithy/middleware-retry@4.0.7':
    - node_modules\@smithy\middleware-retry
  '@smithy/middleware-retry@4.1.5':
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\middleware-retry
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\middleware-retry
  '@smithy/middleware-serde@4.0.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-serde
  '@smithy/middleware-serde@4.0.3':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\core\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\core\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-serde
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\middleware-serde
    - node_modules\@smithy\middleware-serde
  '@smithy/middleware-stack@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-stack
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-stack
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-stack
  '@smithy/middleware-stack@4.0.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-stack
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-stack
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\middleware-stack
    - node_modules\@smithy\middleware-stack
  '@smithy/node-config-provider@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\node-config-provider
  '@smithy/node-config-provider@4.1.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\config-resolver\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\node-config-provider
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\node-config-provider
    - node_modules\@smithy\node-config-provider
  '@smithy/node-http-handler@4.0.3':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\node-http-handler
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\node-http-handler
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\node-http-handler
  '@smithy/node-http-handler@4.0.4':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core\node_modules\@smithy\node-http-handler
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-stream\node_modules\@smithy\node-http-handler
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\node-http-handler
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\node-http-handler
    - node_modules\@smithy\node-http-handler
  '@smithy/property-provider@4.0.1':
    - node_modules\@smithy\property-provider
  '@smithy/property-provider@4.0.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\config-resolver\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\property-provider
    - node_modules\@smithy\node-config-provider\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\token-providers\node_modules\@smithy\property-provider
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\property-provider
  '@smithy/protocol-http@5.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\protocol-http
  '@smithy/protocol-http@5.1.0':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-stream\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\node-http-handler\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\fetch-http-handler\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\core\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\node-http-handler\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\fetch-http-handler\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\core\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\node-http-handler\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\fetch-http-handler\node_modules\@smithy\protocol-http
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\protocol-http
    - node_modules\@smithy\protocol-http
  '@smithy/querystring-builder@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\node-http-handler\node_modules\@smithy\querystring-builder
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\fetch-http-handler\node_modules\@smithy\querystring-builder
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\querystring-builder
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\querystring-builder
  '@smithy/querystring-builder@4.0.2':
    - node_modules\@smithy\querystring-builder
  '@smithy/querystring-parser@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\url-parser\node_modules\@smithy\querystring-parser
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\url-parser\node_modules\@smithy\querystring-parser
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\url-parser\node_modules\@smithy\querystring-parser
  '@smithy/querystring-parser@4.0.2':
    - node_modules\@smithy\querystring-parser
  '@smithy/service-error-classification@4.0.1':
    - node_modules\@smithy\service-error-classification
  '@smithy/service-error-classification@4.0.3':
    - node_modules\@smithy\util-retry\node_modules\@smithy\service-error-classification
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\service-error-classification
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\service-error-classification
  '@smithy/shared-ini-file-loader@4.0.1':
    - node_modules\@smithy\shared-ini-file-loader
  '@smithy/shared-ini-file-loader@4.0.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\node-config-provider\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\node-config-provider\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\config-resolver\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\node-config-provider\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@smithy\node-config-provider\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@aws-sdk\token-providers\node_modules\@smithy\shared-ini-file-loader
    - node_modules\@smithy\middleware-endpoint\node_modules\@smithy\shared-ini-file-loader
  '@smithy/signature-v4@5.0.1':
    - node_modules\@smithy\signature-v4
  '@smithy/smithy-client@4.1.6':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client
  '@smithy/smithy-client@4.2.4':
    - node_modules\@smithy\smithy-client
  '@smithy/types@4.1.0':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\types
    - node_modules\@aws-sdk\credential-provider-node\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\types
  '@smithy/types@4.2.0':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-retry\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\url-parser\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-stream\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\protocol-http\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-middleware\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-serde\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-stack\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\node-http-handler\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\fetch-http-handler\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\node-config-provider\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\core\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\url-parser\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\util-retry\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\protocol-http\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\util-middleware\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-serde\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-stack\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\node-http-handler\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\fetch-http-handler\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\node-config-provider\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\abort-controller\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\querystring-builder\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\core\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\util-retry\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\url-parser\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\protocol-http\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\config-resolver\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\util-middleware\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-serde\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-stack\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\node-http-handler\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\fetch-http-handler\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\node-config-provider\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\abort-controller\node_modules\@smithy\types
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\querystring-builder\node_modules\@smithy\types
    - node_modules\@smithy\types
  '@smithy/url-parser@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\url-parser
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\url-parser
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\url-parser
  '@smithy/url-parser@4.0.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\url-parser
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\url-parser
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\url-parser
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\url-parser
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\url-parser
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\url-parser
    - node_modules\@smithy\url-parser
  '@smithy/util-base64@4.0.0':
    - node_modules\@smithy\util-base64
  '@smithy/util-body-length-browser@4.0.0':
    - node_modules\@smithy\util-body-length-browser
  '@smithy/util-body-length-node@4.0.0':
    - node_modules\@smithy\util-body-length-node
  '@smithy/util-buffer-from@2.2.0':
    - node_modules\@aws-crypto\sha256-browser\node_modules\@smithy\util-buffer-from
    - node_modules\@aws-crypto\util\node_modules\@smithy\util-buffer-from
  '@smithy/util-buffer-from@4.0.0':
    - node_modules\@smithy\util-buffer-from
  '@smithy/util-config-provider@4.0.0':
    - node_modules\@smithy\util-config-provider
  '@smithy/util-defaults-mode-browser@4.0.12':
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\util-defaults-mode-browser
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\util-defaults-mode-browser
  '@smithy/util-defaults-mode-browser@4.0.7':
    - node_modules\@smithy\util-defaults-mode-browser
  '@smithy/util-defaults-mode-node@4.0.12':
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\util-defaults-mode-node
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\util-defaults-mode-node
  '@smithy/util-defaults-mode-node@4.0.7':
    - node_modules\@smithy\util-defaults-mode-node
  '@smithy/util-endpoints@3.0.1':
    - node_modules\@smithy\util-endpoints
  '@smithy/util-endpoints@3.0.4':
    - node_modules\@aws-sdk\util-endpoints\node_modules\@smithy\util-endpoints
    - node_modules\@aws-sdk\nested-clients\node_modules\@smithy\util-endpoints
    - node_modules\@aws-sdk\client-sso\node_modules\@smithy\util-endpoints
  '@smithy/util-hex-encoding@4.0.0':
    - node_modules\@smithy\util-hex-encoding
  '@smithy/util-middleware@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\util-middleware
  '@smithy/util-middleware@4.0.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\core\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\config-resolver\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\core\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\smithy-client\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\config-resolver\node_modules\@smithy\util-middleware
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\util-middleware
    - node_modules\@smithy\util-middleware
  '@smithy/util-retry@4.0.1':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-retry
    - node_modules\@aws-sdk\client-bedrock-agent-runtime\node_modules\@smithy\util-retry
    - node_modules\@aws-sdk\client-kendra\node_modules\@smithy\util-retry
  '@smithy/util-retry@4.0.3':
    - node_modules\@smithy\util-retry
  '@smithy/util-stream@4.1.2':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\util-stream
  '@smithy/util-stream@4.2.0':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\core\node_modules\@smithy\util-stream
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\smithy-client\node_modules\@smithy\util-stream
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@smithy\middleware-endpoint\node_modules\@smithy\util-stream
    - node_modules\@smithy\util-stream
  '@smithy/util-uri-escape@4.0.0':
    - node_modules\@smithy\util-uri-escape
  '@smithy/util-utf8@2.3.0':
    - node_modules\@aws-crypto\sha256-browser\node_modules\@smithy\util-utf8
    - node_modules\@aws-crypto\util\node_modules\@smithy\util-utf8
  '@smithy/util-utf8@4.0.0':
    - node_modules\@smithy\util-utf8
  '@standard-schema/utils@0.3.0':
    - node_modules\@standard-schema\utils
  '@swc/helpers@0.5.17':
    - node_modules\@swc\helpers
  '@szmarczak/http-timer@4.0.6':
    - node_modules\@szmarczak\http-timer
  '@tailwindcss/node@4.1.3':
    - node_modules\@tailwindcss\node
  '@tailwindcss/oxide-win32-x64-msvc@4.1.3':
    - node_modules\@tailwindcss\oxide-win32-x64-msvc
  '@tailwindcss/oxide@4.1.3':
    - node_modules\@tailwindcss\oxide
  '@tailwindcss/vite@4.1.3(vite@6.2.2(@types/node@20.17.17)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0))':
    - node_modules\@tailwindcss\vite
  '@tavily/core@0.3.1':
    - node_modules\@tavily\core
  '@testing-library/dom@10.4.0':
    - node_modules\@testing-library\dom
  '@testing-library/user-event@14.6.1(@testing-library/dom@10.4.0)':
    - node_modules\@testing-library\user-event
  '@textlint/ast-node-types@15.2.1':
    - node_modules\@textlint\ast-node-types
  '@textlint/linter-formatter@15.2.1':
    - node_modules\@textlint\linter-formatter
  '@textlint/module-interop@15.2.1':
    - node_modules\@textlint\module-interop
  '@textlint/regexp-string-matcher@2.0.2':
    - node_modules\@textlint\regexp-string-matcher
  '@textlint/resolver@15.2.1':
    - node_modules\@textlint\resolver
  '@textlint/types@15.2.1':
    - node_modules\@textlint\types
  '@tokenizer/token@0.3.0':
    - node_modules\@tokenizer\token
  '@tootallnate/once@2.0.0':
    - node_modules\@tootallnate\once
  '@tootallnate/quickjs-emscripten@0.23.0':
    - node_modules\@tootallnate\quickjs-emscripten
  '@trivago/prettier-plugin-sort-imports@5.2.2(@vue/compiler-sfc@3.5.13)(prettier@3.5.0)':
    - node_modules\@trivago\prettier-plugin-sort-imports
  '@tsconfig/node10@1.0.11':
    - node_modules\@tsconfig\node10
  '@tsconfig/node12@1.0.11':
    - node_modules\@tsconfig\node12
  '@tsconfig/node14@1.0.3':
    - node_modules\@tsconfig\node14
  '@tsconfig/node16@1.0.4':
    - node_modules\@tsconfig\node16
  '@types/appdmg@0.5.5':
    - node_modules\@types\appdmg
  '@types/aria-query@5.0.4':
    - node_modules\@types\aria-query
  '@types/async-retry@1.4.9':
    - node_modules\@types\async-retry
  '@types/babel__core@7.20.5':
    - node_modules\@types\babel__core
  '@types/babel__generator@7.6.8':
    - node_modules\@types\babel__generator
  '@types/babel__template@7.4.4':
    - node_modules\@types\babel__template
  '@types/babel__traverse@7.20.6':
    - node_modules\@types\babel__traverse
  '@types/big.js@6.2.2':
    - node_modules\@types\big.js
  '@types/bn.js@5.2.0':
    - node_modules\@types\bn.js
  '@types/body-parser@1.19.5':
    - node_modules\@types\body-parser
  '@types/cacheable-request@6.0.3':
    - node_modules\@types\cacheable-request
  '@types/connect@3.4.38':
    - node_modules\@types\connect
  '@types/conventional-commits-parser@5.0.1':
    - node_modules\@types\conventional-commits-parser
  '@types/cookie@0.6.0':
    - node_modules\@types\cookie
  '@types/debug@4.1.12':
    - node_modules\@types\debug
  '@types/diff@5.2.3':
    - node_modules\@types\diff
  '@types/estree-jsx@1.0.5':
    - node_modules\@types\estree-jsx
  '@types/estree@1.0.6':
    - node_modules\@types\estree
  '@types/express-serve-static-core@5.0.6':
    - node_modules\@types\express-serve-static-core
  '@types/express@5.0.2':
    - node_modules\@types\express
  '@types/gensync@1.0.4':
    - node_modules\@types\gensync
  '@types/hast@3.0.4':
    - node_modules\@types\hast
  '@types/hoist-non-react-statics@3.3.6':
    - node_modules\@types\hoist-non-react-statics
  '@types/http-cache-semantics@4.0.4':
    - node_modules\@types\http-cache-semantics
  '@types/http-errors@2.0.4':
    - node_modules\@types\http-errors
  '@types/inquirer@9.0.7':
    - node_modules\@types\inquirer
  '@types/js-cookie@2.2.7':
    - node_modules\@types\js-cookie
  '@types/js-yaml@4.0.9':
    - node_modules\@types\js-yaml
  '@types/jsdom@21.1.7':
    - node_modules\@types\jsdom
  '@types/json-schema@7.0.15':
    - node_modules\@types\json-schema
  '@types/json5@0.0.29':
    - node_modules\@types\json5
  '@types/keyv@3.1.4':
    - node_modules\@types\keyv
  '@types/lodash-es@4.17.12':
    - node_modules\@types\lodash-es
  '@types/lodash.isnumber@3.0.3':
    - node_modules\@types\lodash.isnumber
  '@types/lodash.merge@4.6.9':
    - node_modules\@types\lodash.merge
  '@types/lodash@4.17.15':
    - node_modules\@types\lodash
  '@types/mdast@4.0.4':
    - node_modules\@types\mdast
  '@types/mime-types@3.0.1':
    - node_modules\@types\mime-types
  '@types/mime@1.3.5':
    - node_modules\@types\mime
  '@types/minimatch@5.1.2':
    - node_modules\@types\minimatch
  '@types/ms@2.1.0':
    - node_modules\@types\ms
  '@types/node-fetch@2.6.12':
    - node_modules\@types\node-fetch
  '@types/node@12.20.55':
    - node_modules\@manypkg\find-root\node_modules\@types\node
  '@types/node@16.9.1':
    - node_modules\image-q\node_modules\@types\node
  '@types/node@18.19.75':
    - node_modules\opencommit\node_modules\@types\node
    - node_modules\@anthropic-ai\sdk\node_modules\@types\node
    - node_modules\openai\node_modules\@types\node
    - node_modules\@langchain\openai\node_modules\@types\node
    - node_modules\@browserbasehq\stagehand\node_modules\@types\node
    - node_modules\@browserbasehq\sdk\node_modules\@types\node
  '@types/node@20.14.8':
    - node_modules\@types\node-fetch\node_modules\@types\node
    - node_modules\electron\node_modules\@types\node
    - node_modules\@types\jsdom\node_modules\@types\node
    - node_modules\@types\conventional-commits-parser\node_modules\@types\node
    - node_modules\@inquirer\confirm\node_modules\@types\node
    - node_modules\@types\serve-static\node_modules\@types\node
    - node_modules\@types\body-parser\node_modules\@types\node
    - node_modules\@types\express-serve-static-core\node_modules\@types\node
    - node_modules\@types\bn.js\node_modules\@types\node
    - node_modules\@types\secp256k1\node_modules\@types\node
    - node_modules\@types\yauzl\node_modules\@types\node
    - node_modules\@types\responselike\node_modules\@types\node
    - node_modules\@types\cacheable-request\node_modules\@types\node
    - node_modules\@types\through\node_modules\@types\node
    - node_modules\@types\appdmg\node_modules\@types\node
    - node_modules\@inquirer\type\node_modules\@types\node
    - node_modules\@inquirer\core\node_modules\@types\node
    - node_modules\@types\send\node_modules\@types\node
    - node_modules\@types\connect\node_modules\@types\node
    - node_modules\@types\keyv\node_modules\@types\node
    - packages\agent-infra\browser\node_modules\@types\node
    - packages\agent-infra\browser-use\node_modules\@types\node
    - packages\agent-infra\logger\node_modules\@types\node
    - packages\agent-infra\mcp-client\node_modules\@types\node
    - packages\agent-infra\mcp-servers\commands\node_modules\@types\node
    - packages\agent-infra\mcp-servers\search\node_modules\@types\node
    - packages\agent-infra\search\bing-search\node_modules\@types\node
    - packages\agent-infra\search\browser-search\node_modules\@types\node
    - packages\agent-infra\search\duckduckgo-search\node_modules\@types\node
    - packages\agent-infra\search\search\node_modules\@types\node
    - packages\agent-infra\shared\node_modules\@types\node
    - packages\ui-tars\electron-ipc\node_modules\@types\node
  '@types/node@20.17.17':
    - node_modules\@types\node
  '@types/node@22.13.10':
    - node_modules\@vitest\browser\node_modules\@types\node
    - node_modules\vitest-browser-react\node_modules\@types\node
    - packages\agent-infra\create-new-mcp\node_modules\@types\node
    - packages\agent-infra\mcp-benchmark\node_modules\@types\node
    - packages\agent-infra\mcp-http-server\node_modules\@types\node
    - packages\agent-infra\mcp-servers\browser\node_modules\@types\node
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@types\node
    - packages\agent-infra\mcp-shared\node_modules\@types\node
    - packages\ui-tars\operators\browserbase\node_modules\@types\node
    - packages\ui-tars\sdk\node_modules\@types\node
  '@types/normalize-package-data@2.4.4':
    - node_modules\@types\normalize-package-data
  '@types/parse-json@4.0.2':
    - node_modules\@types\parse-json
  '@types/prop-types@15.7.14':
    - node_modules\@types\prop-types
  '@types/qs@6.14.0':
    - node_modules\@types\qs
  '@types/range-parser@1.2.7':
    - node_modules\@types\range-parser
  '@types/react-dom@19.1.1(@types/react@19.1.2)':
    - node_modules\@types\react-dom
  '@types/react@18.3.18':
    - node_modules\@types\hoist-non-react-statics\node_modules\@types\react
  '@types/react@19.1.2':
    - node_modules\@types\react
  '@types/responselike@1.0.3':
    - node_modules\@types\responselike
  '@types/retry@0.12.0':
    - node_modules\@types\retry
  '@types/retry@0.12.5':
    - node_modules\@types\async-retry\node_modules\@types\retry
  '@types/secp256k1@4.0.6':
    - node_modules\@types\secp256k1
  '@types/semver@7.5.8':
    - node_modules\@types\semver
  '@types/send@0.17.4':
    - node_modules\@types\send
  '@types/serve-static@1.15.7':
    - node_modules\@types\serve-static
  '@types/statuses@2.0.5':
    - node_modules\@types\statuses
  '@types/through@0.0.33':
    - node_modules\@types\through
  '@types/tough-cookie@4.0.5':
    - node_modules\@types\tough-cookie
  '@types/turndown@5.0.5':
    - node_modules\@types\turndown
  '@types/unist@2.0.11':
    - node_modules\parse-entities\node_modules\@types\unist
  '@types/unist@3.0.3':
    - node_modules\@types\unist
  '@types/uuid@10.0.0':
    - node_modules\@types\uuid
  '@types/uuid@9.0.8':
    - node_modules\@aws-sdk\client-bedrock-runtime\node_modules\@types\uuid
    - node_modules\@aws-sdk\client-kendra\node_modules\@types\uuid
  '@types/which@2.0.2':
    - node_modules\@types\which
  '@types/which@3.0.4':
    - packages\agent-infra\browser\node_modules\@types\which
  '@types/yauzl@2.10.3':
    - node_modules\@types\yauzl
  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.7.3))(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@typescript-eslint\eslint-plugin
  '@typescript-eslint/eslint-plugin@7.18.0(@typescript-eslint/parser@7.18.0(eslint@8.57.1)(typescript@5.7.3))(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\eslint-plugin
  '@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@typescript-eslint\parser
  '@typescript-eslint/parser@7.18.0(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\parser
  '@typescript-eslint/scope-manager@5.62.0':
    - node_modules\@typescript-eslint\scope-manager
  '@typescript-eslint/scope-manager@7.18.0':
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\scope-manager
  '@typescript-eslint/type-utils@5.62.0(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@typescript-eslint\type-utils
  '@typescript-eslint/type-utils@7.18.0(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\type-utils
  '@typescript-eslint/types@5.62.0':
    - node_modules\@typescript-eslint\types
  '@typescript-eslint/types@7.18.0':
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\types
    - node_modules\@typescript-eslint\visitor-keys\node_modules\@typescript-eslint\types
  '@typescript-eslint/typescript-estree@5.62.0(typescript@5.7.3)':
    - node_modules\@typescript-eslint\typescript-estree
  '@typescript-eslint/typescript-estree@7.18.0(typescript@5.7.3)':
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\typescript-estree
  '@typescript-eslint/utils@5.62.0(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@typescript-eslint\utils
  '@typescript-eslint/utils@7.18.0(eslint@8.57.1)(typescript@5.7.3)':
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\utils
  '@typescript-eslint/visitor-keys@5.62.0':
    - node_modules\@typescript-eslint\scope-manager\node_modules\@typescript-eslint\visitor-keys
    - node_modules\@typescript-eslint\typescript-estree\node_modules\@typescript-eslint\visitor-keys
  '@typescript-eslint/visitor-keys@7.18.0':
    - node_modules\@typescript-eslint\visitor-keys
  '@ungap/structured-clone@1.3.0':
    - node_modules\@ungap\structured-clone
  '@vitejs/plugin-react@4.3.4(vite@6.2.2(@types/node@20.17.17)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0))':
    - node_modules\@vitejs\plugin-react
  '@vitest/browser@3.0.5(@types/node@22.13.10)(bufferutil@4.0.9)(playwright@1.50.1)(typescript@5.7.3)(utf-8-validate@6.0.5)(vite@6.2.2(@types/node@22.13.10)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0))(vitest@3.0.5)':
    - node_modules\@vitest\browser
  '@vitest/coverage-istanbul@3.0.5(vitest@3.0.8(@types/debug@4.1.12)(@types/node@20.17.17)(happy-dom@17.1.1)(jiti@2.4.2)(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5))(lightningcss@1.29.2)(msw@2.7.0(@types/node@20.17.17)(typescript@5.7.3))(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0))':
    - node_modules\@vitest\coverage-istanbul
  '@vitest/coverage-v8@3.0.7(vitest@3.0.7(@types/debug@4.1.12)(@types/node@20.14.8)(happy-dom@17.1.1)(jiti@2.4.2)(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5))(lightningcss@1.29.2)(msw@2.7.0(@types/node@20.14.8)(typescript@5.7.3))(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0))':
    - packages\agent-infra\browser\node_modules\@vitest\coverage-v8
    - packages\agent-infra\browser-use\node_modules\@vitest\coverage-v8
    - packages\agent-infra\search\bing-search\node_modules\@vitest\coverage-v8
    - packages\agent-infra\search\browser-search\node_modules\@vitest\coverage-v8
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\coverage-v8
    - packages\agent-infra\search\search\node_modules\@vitest\coverage-v8
  '@vitest/coverage-v8@3.0.8':
    - node_modules\@vitest\coverage-v8
  '@vitest/expect@3.0.5':
    - node_modules\@vitest\browser\node_modules\@vitest\expect
    - node_modules\vitest-browser-react\node_modules\@vitest\expect
    - packages\ui-tars\operators\browserbase\node_modules\@vitest\expect
    - packages\ui-tars\sdk\node_modules\@vitest\expect
  '@vitest/expect@3.0.7':
    - packages\agent-infra\browser\node_modules\@vitest\expect
    - packages\agent-infra\browser-use\node_modules\@vitest\expect
    - packages\agent-infra\mcp-client\node_modules\@vitest\expect
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\expect
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\expect
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\expect
    - packages\agent-infra\search\bing-search\node_modules\@vitest\expect
    - packages\agent-infra\search\browser-search\node_modules\@vitest\expect
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\expect
    - packages\agent-infra\search\search\node_modules\@vitest\expect
    - packages\agent-infra\shared\node_modules\@vitest\expect
  '@vitest/expect@3.0.8':
    - node_modules\@vitest\expect
  '@vitest/mocker@3.0.5(msw@2.7.0(@types/node@22.13.10)(typescript@5.7.3))(vite@6.2.2(@types/node@22.13.10)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0))':
    - node_modules\@vitest\mocker
  '@vitest/mocker@3.0.7(msw@2.7.0(@types/node@20.14.8)(typescript@5.7.3))(vite@6.2.2(@types/node@20.14.8)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0))':
    - packages\agent-infra\browser\node_modules\@vitest\mocker
    - packages\agent-infra\browser-use\node_modules\@vitest\mocker
    - packages\agent-infra\mcp-client\node_modules\@vitest\mocker
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\mocker
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\mocker
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\mocker
    - packages\agent-infra\search\bing-search\node_modules\@vitest\mocker
    - packages\agent-infra\search\browser-search\node_modules\@vitest\mocker
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\mocker
    - packages\agent-infra\search\search\node_modules\@vitest\mocker
    - packages\agent-infra\shared\node_modules\@vitest\mocker
  '@vitest/mocker@3.0.8(msw@2.7.0(@types/node@20.17.17)(typescript@5.7.3))(vite@6.2.2(@types/node@20.17.17)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0))':
    - node_modules\vitest\node_modules\@vitest\mocker
  '@vitest/pretty-format@3.0.5':
    - node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - node_modules\@vitest\browser\node_modules\@vitest\pretty-format
    - node_modules\vitest-browser-react\node_modules\@vitest\pretty-format
    - packages\ui-tars\operators\browserbase\node_modules\@vitest\pretty-format
    - packages\ui-tars\sdk\node_modules\@vitest\pretty-format
  '@vitest/pretty-format@3.0.7':
    - packages\agent-infra\browser\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\browser\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\browser-use\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\browser-use\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-client\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-client\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\bing-search\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\bing-search\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\browser-search\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\browser-search\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\search\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\search\search\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
    - packages\agent-infra\shared\node_modules\@vitest\utils\node_modules\@vitest\pretty-format
    - packages\agent-infra\shared\node_modules\@vitest\snapshot\node_modules\@vitest\pretty-format
  '@vitest/pretty-format@3.0.8':
    - node_modules\@vitest\pretty-format
  '@vitest/runner@3.0.5':
    - node_modules\@vitest\browser\node_modules\@vitest\runner
    - node_modules\vitest-browser-react\node_modules\@vitest\runner
    - packages\ui-tars\operators\browserbase\node_modules\@vitest\runner
    - packages\ui-tars\sdk\node_modules\@vitest\runner
  '@vitest/runner@3.0.7':
    - packages\agent-infra\browser\node_modules\@vitest\runner
    - packages\agent-infra\browser-use\node_modules\@vitest\runner
    - packages\agent-infra\mcp-client\node_modules\@vitest\runner
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\runner
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\runner
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\runner
    - packages\agent-infra\search\bing-search\node_modules\@vitest\runner
    - packages\agent-infra\search\browser-search\node_modules\@vitest\runner
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\runner
    - packages\agent-infra\search\search\node_modules\@vitest\runner
    - packages\agent-infra\shared\node_modules\@vitest\runner
  '@vitest/runner@3.0.8':
    - node_modules\@vitest\runner
  '@vitest/snapshot@3.0.5':
    - node_modules\@vitest\browser\node_modules\@vitest\snapshot
    - node_modules\vitest-browser-react\node_modules\@vitest\snapshot
    - packages\ui-tars\operators\browserbase\node_modules\@vitest\snapshot
    - packages\ui-tars\sdk\node_modules\@vitest\snapshot
  '@vitest/snapshot@3.0.7':
    - packages\agent-infra\browser\node_modules\@vitest\snapshot
    - packages\agent-infra\browser-use\node_modules\@vitest\snapshot
    - packages\agent-infra\mcp-client\node_modules\@vitest\snapshot
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\snapshot
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\snapshot
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\snapshot
    - packages\agent-infra\search\bing-search\node_modules\@vitest\snapshot
    - packages\agent-infra\search\browser-search\node_modules\@vitest\snapshot
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\snapshot
    - packages\agent-infra\search\search\node_modules\@vitest\snapshot
    - packages\agent-infra\shared\node_modules\@vitest\snapshot
  '@vitest/snapshot@3.0.8':
    - node_modules\@vitest\snapshot
  '@vitest/spy@3.0.5':
    - node_modules\@vitest\mocker\node_modules\@vitest\spy
    - node_modules\@vitest\browser\node_modules\@vitest\spy
    - node_modules\vitest-browser-react\node_modules\@vitest\spy
    - packages\ui-tars\operators\browserbase\node_modules\@vitest\spy
    - packages\ui-tars\sdk\node_modules\@vitest\spy
  '@vitest/spy@3.0.7':
    - packages\agent-infra\browser\node_modules\@vitest\spy
    - packages\agent-infra\browser-use\node_modules\@vitest\spy
    - packages\agent-infra\mcp-client\node_modules\@vitest\spy
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\spy
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\spy
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\spy
    - packages\agent-infra\search\bing-search\node_modules\@vitest\spy
    - packages\agent-infra\search\browser-search\node_modules\@vitest\spy
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\spy
    - packages\agent-infra\search\search\node_modules\@vitest\spy
    - packages\agent-infra\shared\node_modules\@vitest\spy
  '@vitest/spy@3.0.8':
    - node_modules\@vitest\spy
  '@vitest/utils@3.0.5':
    - node_modules\@vitest\utils
  '@vitest/utils@3.0.7':
    - packages\agent-infra\browser\node_modules\@vitest\utils
    - packages\agent-infra\browser-use\node_modules\@vitest\utils
    - packages\agent-infra\mcp-client\node_modules\@vitest\utils
    - packages\agent-infra\mcp-servers\browser\node_modules\@vitest\utils
    - packages\agent-infra\mcp-servers\commands\node_modules\@vitest\utils
    - packages\agent-infra\mcp-servers\filesystem\node_modules\@vitest\utils
    - packages\agent-infra\search\bing-search\node_modules\@vitest\utils
    - packages\agent-infra\search\browser-search\node_modules\@vitest\utils
    - packages\agent-infra\search\duckduckgo-search\node_modules\@vitest\utils
    - packages\agent-infra\search\search\node_modules\@vitest\utils
    - packages\agent-infra\shared\node_modules\@vitest\utils
  '@vitest/utils@3.0.8':
    - node_modules\vitest\node_modules\@vitest\utils
    - node_modules\@vitest\expect\node_modules\@vitest\utils
    - node_modules\@vitest\runner\node_modules\@vitest\utils
  '@vue/compiler-core@3.5.13':
    - node_modules\@vue\compiler-core
  '@vue/compiler-dom@3.5.13':
    - node_modules\@vue\compiler-dom
  '@vue/compiler-sfc@3.5.13':
    - node_modules\@vue\compiler-sfc
  '@vue/compiler-ssr@3.5.13':
    - node_modules\@vue\compiler-ssr
  '@vue/shared@3.5.13':
    - node_modules\@vue\shared
  '@xmldom/xmldom@0.8.10':
    - node_modules\@xmldom\xmldom
  '@xobotyi/scrollbar-width@1.9.5':
    - node_modules\@xobotyi\scrollbar-width
  JSONStream@1.3.5:
    - node_modules\JSONStream
  abbrev@1.1.1:
    - node_modules\abbrev
  abort-controller@3.0.0:
    - node_modules\abort-controller
  accepts@1.3.8:
    - node_modules\accepts
  accepts@2.0.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\accepts
    - node_modules\express-rate-limit\node_modules\accepts
    - packages\agent-infra\mcp-http-server\node_modules\accepts
  acorn-jsx@5.3.2(acorn@8.14.0):
    - node_modules\acorn-jsx
  acorn-walk@8.3.4:
    - node_modules\acorn-walk
  acorn@8.14.0:
    - node_modules\acorn
  agent-base@6.0.2:
    - node_modules\make-fetch-happen\node_modules\agent-base
  agent-base@7.1.3:
    - node_modules\agent-base
  agentkeepalive@4.6.0:
    - node_modules\agentkeepalive
  aggregate-error@3.1.0:
    - node_modules\aggregate-error
  ajv-formats@3.0.1(ajv@8.17.1):
    - node_modules\ajv-formats
  ajv@6.12.6:
    - node_modules\eslint\node_modules\ajv
    - node_modules\@eslint\eslintrc\node_modules\ajv
    - node_modules\@modelcontextprotocol\sdk\node_modules\ajv
  ajv@8.17.1:
    - node_modules\ajv
  ansi-colors@4.1.3:
    - node_modules\ansi-colors
  ansi-escapes@4.3.2:
    - node_modules\ansi-escapes
  ansi-escapes@5.0.0:
    - node_modules\log-update\node_modules\ansi-escapes
  ansi-escapes@7.0.0:
    - node_modules\terminal-link\node_modules\ansi-escapes
  ansi-regex@5.0.1:
    - node_modules\ansi-regex
  ansi-regex@6.1.0:
    - node_modules\@secretlint\formatter\node_modules\ansi-regex
    - node_modules\wrap-ansi\node_modules\ansi-regex
    - node_modules\log-update\node_modules\ansi-regex
    - node_modules\cli-truncate\node_modules\ansi-regex
    - node_modules\@isaacs\cliui\node_modules\ansi-regex
    - node_modules\mcp-proxy\node_modules\ansi-regex
  ansi-styles@4.3.0:
    - node_modules\ansi-styles
  ansi-styles@5.2.0:
    - node_modules\@langchain\core\node_modules\ansi-styles
    - node_modules\pretty-format\node_modules\ansi-styles
  ansi-styles@6.2.1:
    - node_modules\wrap-ansi\node_modules\ansi-styles
    - node_modules\mcp-proxy\node_modules\ansi-styles
    - node_modules\slice-ansi\node_modules\ansi-styles
  any-base@1.1.0:
    - node_modules\any-base
  arch@2.2.0:
    - node_modules\arch
  arg@4.1.3:
    - node_modules\arg
  argparse@1.0.10:
    - node_modules\@changesets\parse\node_modules\argparse
    - node_modules\read-yaml-file\node_modules\argparse
    - node_modules\@textlint\linter-formatter\node_modules\argparse
  argparse@2.0.1:
    - node_modules\argparse
  aria-hidden@1.2.4:
    - node_modules\aria-hidden
  aria-query@5.3.0:
    - node_modules\aria-query
  array-buffer-byte-length@1.0.2:
    - node_modules\array-buffer-byte-length
  array-flatten@1.1.1:
    - node_modules\array-flatten
  array-ify@1.0.0:
    - node_modules\array-ify
  array-includes@3.1.8:
    - node_modules\array-includes
  array-union@2.1.0:
    - node_modules\array-union
  array.prototype.findlast@1.2.5:
    - node_modules\array.prototype.findlast
  array.prototype.findlastindex@1.2.5:
    - node_modules\array.prototype.findlastindex
  array.prototype.flat@1.3.3:
    - node_modules\array.prototype.flat
  array.prototype.flatmap@1.3.3:
    - node_modules\array.prototype.flatmap
  array.prototype.tosorted@1.1.4:
    - node_modules\array.prototype.tosorted
  arraybuffer.prototype.slice@1.0.4:
    - node_modules\arraybuffer.prototype.slice
  assertion-error@2.0.1:
    - node_modules\assertion-error
  ast-types@0.13.4:
    - node_modules\ast-types
  astral-regex@2.0.0:
    - node_modules\astral-regex
  async-function@1.0.0:
    - node_modules\async-function
  async-retry@1.3.3:
    - node_modules\async-retry
  asynckit@0.4.0:
    - node_modules\asynckit
  at-least-node@1.0.0:
    - node_modules\at-least-node
  atomically@2.0.3:
    - node_modules\atomically
  author-regex@1.0.0:
    - node_modules\author-regex
  available-typed-arrays@1.0.7:
    - node_modules\available-typed-arrays
  await-to-js@3.0.0:
    - node_modules\await-to-js
  axios@1.7.9:
    - node_modules\axios
  b4a@1.6.7:
    - node_modules\b4a
  babel-plugin-macros@3.1.0:
    - node_modules\babel-plugin-macros
  bail@2.0.2:
    - node_modules\bail
  balanced-match@1.0.2:
    - node_modules\balanced-match
  bare-events@2.5.4:
    - node_modules\bare-events
  bare-fs@4.0.1:
    - node_modules\bare-fs
  bare-os@3.6.0:
    - node_modules\bare-os
  bare-path@3.0.0:
    - node_modules\bare-path
  bare-stream@2.6.5(bare-events@2.5.4):
    - node_modules\bare-stream
  base64-js@1.5.1:
    - node_modules\base64-js
  basic-ftp@5.0.5:
    - node_modules\basic-ftp
  before-after-hook@2.2.3:
    - node_modules\before-after-hook
  better-path-resolve@1.0.0:
    - node_modules\better-path-resolve
  big.js@6.2.2:
    - node_modules\big.js
  binaryextensions@6.11.0:
    - node_modules\binaryextensions
  bindings@1.5.0:
    - node_modules\bindings
  bl@4.1.0:
    - node_modules\bl
  bluebird@3.7.2:
    - node_modules\bluebird
  bmp-js@0.1.0:
    - node_modules\bmp-js
  bmp-ts@1.0.9:
    - node_modules\bmp-ts
  bn.js@4.12.2:
    - node_modules\elliptic\node_modules\bn.js
  bn.js@5.2.2:
    - node_modules\bn.js
  body-parser@1.20.3:
    - node_modules\body-parser
  body-parser@2.2.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\body-parser
    - node_modules\express-rate-limit\node_modules\body-parser
    - packages\agent-infra\mcp-http-server\node_modules\body-parser
  boolean@3.2.0:
    - node_modules\boolean
  bottleneck@2.19.5:
    - node_modules\bottleneck
  boundary@2.0.0:
    - node_modules\boundary
  bowser@2.11.0:
    - node_modules\bowser
  brace-expansion@1.1.11:
    - node_modules\minimatch\node_modules\brace-expansion
  brace-expansion@2.0.1:
    - node_modules\brace-expansion
  braces@3.0.3:
    - node_modules\braces
  brorand@1.1.0:
    - node_modules\brorand
  browserslist@4.24.4:
    - node_modules\browserslist
  buffer-builder@0.2.0:
    - node_modules\buffer-builder
  buffer-crc32@0.2.13:
    - node_modules\buffer-crc32
  buffer-equal@0.0.1:
    - node_modules\buffer-equal
  buffer-from@1.1.2:
    - node_modules\buffer-from
  buffer@5.7.1:
    - node_modules\buffer
  buffer@6.0.3:
    - node_modules\readable-web-to-node-stream\node_modules\buffer
  bufferutil@4.0.9:
    - node_modules\bufferutil
  builder-util-runtime@9.2.10:
    - packages\common\electron-build\node_modules\builder-util-runtime
  builder-util-runtime@9.3.1:
    - node_modules\builder-util-runtime
  bytes@3.1.2:
    - node_modules\bytes
  cac@6.7.14:
    - node_modules\cac
  cacache@16.1.3:
    - node_modules\cacache
  cacheable-lookup@5.0.4:
    - node_modules\cacheable-lookup
  cacheable-request@7.0.4:
    - node_modules\cacheable-request
  call-bind-apply-helpers@1.0.1:
    - node_modules\call-bind-apply-helpers
  call-bind@1.0.8:
    - node_modules\call-bind
  call-bound@1.0.3:
    - node_modules\call-bound
  callsites@3.1.0:
    - node_modules\callsites
  camelcase@6.3.0:
    - node_modules\camelcase
  caniuse-lite@1.0.30001703:
    - node_modules\caniuse-lite
  caniuse-lite@1.0.30001717:
    - node_modules\@rspack\core\node_modules\caniuse-lite
  ccount@2.0.1:
    - node_modules\ccount
  centra@2.7.0:
    - node_modules\centra
  chai@5.1.2:
    - node_modules\@vitest\browser\node_modules\chai
    - node_modules\vitest-browser-react\node_modules\chai
    - packages\ui-tars\operators\browserbase\node_modules\chai
    - packages\ui-tars\sdk\node_modules\chai
  chai@5.2.0:
    - node_modules\chai
    - node_modules\@vitest\browser\node_modules\@vitest\expect\node_modules\chai
    - node_modules\vitest-browser-react\node_modules\@vitest\expect\node_modules\chai
    - packages\ui-tars\operators\browserbase\node_modules\@vitest\expect\node_modules\chai
    - packages\ui-tars\sdk\node_modules\@vitest\expect\node_modules\chai
  chalk@4.1.2:
    - node_modules\chalk
  chalk@5.3.0:
    - node_modules\lint-staged\node_modules\chalk
  chalk@5.4.1:
    - node_modules\opencommit\node_modules\chalk
    - node_modules\@secretlint\formatter\node_modules\chalk
    - node_modules\@commitlint\load\node_modules\chalk
    - node_modules\@commitlint\types\node_modules\chalk
    - node_modules\@commitlint\format\node_modules\chalk
  character-entities-html4@2.1.0:
    - node_modules\character-entities-html4
  character-entities-legacy@3.0.0:
    - node_modules\character-entities-legacy
  character-entities@2.0.2:
    - node_modules\character-entities
  character-reference-invalid@2.0.1:
    - node_modules\character-reference-invalid
  chardet@0.7.0:
    - node_modules\chardet
  check-error@2.1.1:
    - node_modules\check-error
  chokidar@4.0.3:
    - node_modules\chokidar
  chownr@2.0.0:
    - node_modules\chownr
  chrome-trace-event@1.0.4:
    - node_modules\chrome-trace-event
  chromium-bidi@2.1.2(devtools-protocol@0.0.1413902):
    - node_modules\chromium-bidi
  chromium-bidi@4.1.1(devtools-protocol@0.0.1425554):
    - node_modules\puppeteer-core\node_modules\chromium-bidi
  chromium-bidi@7.1.1(devtools-protocol@0.0.1464554):
    - node_modules\@agent-infra\browser-context\node_modules\chromium-bidi
  ci-info@3.9.0:
    - node_modules\ci-info
  class-variance-authority@0.7.1:
    - node_modules\class-variance-authority
  clean-stack@2.2.0:
    - node_modules\clean-stack
  cleye@1.3.4:
    - node_modules\cleye
  cli-cursor@3.1.0:
    - node_modules\cli-cursor
  cli-cursor@4.0.0:
    - node_modules\log-update\node_modules\cli-cursor
  cli-spinners@2.9.2:
    - node_modules\cli-spinners
  cli-truncate@3.1.0:
    - node_modules\cli-truncate
  cli-width@3.0.0:
    - packages\ui-tars\operators\adb\node_modules\cli-width
  cli-width@4.1.0:
    - node_modules\cli-width
  clipboardy@2.3.0:
    - node_modules\clipboardy
  cliui@8.0.1:
    - node_modules\cliui
  cliui@9.0.1:
    - node_modules\mcp-proxy\node_modules\cliui
  clone-response@1.0.3:
    - node_modules\clone-response
  clone@1.0.4:
    - node_modules\clone
  clsx@2.1.1:
    - node_modules\clsx
  color-convert@2.0.1:
    - node_modules\color-convert
  color-name@1.1.4:
    - node_modules\color-name
  color-string@1.9.1:
    - node_modules\color-string
  color@4.2.3:
    - node_modules\color
  colorette@2.0.20:
    - node_modules\colorette
  colorjs.io@0.5.2:
    - node_modules\colorjs.io
  combined-stream@1.0.8:
    - node_modules\combined-stream
  comma-separated-tokens@2.0.3:
    - node_modules\comma-separated-tokens
  commander@11.0.0:
    - node_modules\lint-staged\node_modules\commander
  commander@11.1.0:
    - node_modules\@electron-forge\cli\node_modules\commander
  commander@13.1.0:
    - node_modules\commander
  commander@5.1.0:
    - node_modules\@electron\asar\node_modules\commander
  commander@9.5.0:
    - node_modules\postject\node_modules\commander
  compare-func@2.0.0:
    - node_modules\compare-func
  compare-version@0.1.2:
    - node_modules\compare-version
  concat-map@0.0.1:
    - node_modules\concat-map
  conf@13.1.0:
    - node_modules\conf
  console-table-printer@2.12.1:
    - node_modules\console-table-printer
  content-disposition@0.5.4:
    - node_modules\content-disposition
  content-disposition@1.0.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\content-disposition
    - node_modules\express-rate-limit\node_modules\content-disposition
    - packages\agent-infra\mcp-http-server\node_modules\content-disposition
  content-type@1.0.5:
    - node_modules\content-type
  conventional-changelog-angular@7.0.0:
    - node_modules\conventional-changelog-angular
  conventional-changelog-conventionalcommits@7.0.2:
    - node_modules\conventional-changelog-conventionalcommits
  conventional-commits-parser@5.0.0:
    - node_modules\conventional-commits-parser
  convert-source-map@1.9.0:
    - node_modules\@emotion\babel-plugin\node_modules\convert-source-map
  convert-source-map@2.0.0:
    - node_modules\convert-source-map
  cookie-signature@1.0.6:
    - node_modules\cookie-signature
  cookie-signature@1.2.2:
    - node_modules\@modelcontextprotocol\sdk\node_modules\cookie-signature
    - node_modules\express-rate-limit\node_modules\cookie-signature
    - packages\agent-infra\mcp-http-server\node_modules\cookie-signature
  cookie@0.7.1:
    - node_modules\express\node_modules\cookie
  cookie@0.7.2:
    - node_modules\cookie
  cookie@1.0.2:
    - node_modules\react-router\node_modules\cookie
  copy-to-clipboard@3.3.3:
    - node_modules\copy-to-clipboard
  core-js@3.41.0:
    - node_modules\core-js
  core-js@3.43.0:
    - packages\common\electron-build\node_modules\core-js
    - packages\ui-tars\action-parser\node_modules\core-js
    - packages\ui-tars\cli\node_modules\core-js
    - packages\ui-tars\electron-ipc\node_modules\core-js
    - packages\ui-tars\operators\adb\node_modules\core-js
    - packages\ui-tars\operators\browser-operator\node_modules\core-js
    - packages\ui-tars\operators\browserbase\node_modules\core-js
    - packages\ui-tars\operators\nut-js\node_modules\core-js
    - packages\ui-tars\sdk\node_modules\core-js
    - packages\ui-tars\shared\node_modules\core-js
    - packages\ui-tars\utio\node_modules\core-js
  core-util-is@1.0.3:
    - node_modules\core-util-is
  cors@2.8.5:
    - node_modules\cors
  cosmiconfig-typescript-loader@6.1.0(@types/node@20.17.17)(cosmiconfig@9.0.0(typescript@5.7.3))(typescript@5.7.3):
    - node_modules\cosmiconfig-typescript-loader
  cosmiconfig@7.1.0:
    - node_modules\babel-plugin-macros\node_modules\cosmiconfig
  cosmiconfig@9.0.0(typescript@5.7.3):
    - node_modules\cosmiconfig
  countup.js@2.8.2:
    - node_modules\countup.js
  create-require@1.1.1:
    - node_modules\create-require
  cross-dirname@0.1.0:
    - node_modules\cross-dirname
  cross-env@7.0.3:
    - node_modules\cross-env
  cross-fetch@4.1.0(encoding@0.1.13):
    - node_modules\cross-fetch
  cross-spawn@6.0.6:
    - node_modules\execa\node_modules\cross-spawn
  cross-spawn@7.0.6:
    - node_modules\cross-spawn
  cross-zip@4.0.1:
    - node_modules\cross-zip
  crypto@1.0.1:
    - node_modules\crypto
  css-in-js-utils@3.1.0:
    - node_modules\css-in-js-utils
  css-tree@1.1.3:
    - node_modules\css-tree
  cssstyle@4.3.0:
    - node_modules\cssstyle
  csstype@3.1.3:
    - node_modules\csstype
  dargs@8.1.0:
    - node_modules\dargs
  data-uri-to-buffer@6.0.2:
    - node_modules\data-uri-to-buffer
  data-urls@5.0.0:
    - node_modules\data-urls
  data-view-buffer@1.0.2:
    - node_modules\data-view-buffer
  data-view-byte-length@1.0.2:
    - node_modules\data-view-byte-length
  data-view-byte-offset@1.0.1:
    - node_modules\data-view-byte-offset
  dayjs@1.11.13:
    - node_modules\dayjs
  debounce-fn@6.0.0:
    - node_modules\debounce-fn
  debug@2.6.9:
    - node_modules\electron-squirrel-startup\node_modules\debug
    - node_modules\body-parser\node_modules\debug
    - node_modules\express\node_modules\debug
    - node_modules\get-package-info\node_modules\debug
    - node_modules\finalhandler\node_modules\debug
    - node_modules\send\node_modules\debug
  debug@3.2.7:
    - node_modules\eslint-plugin-import\node_modules\debug
    - node_modules\eslint-module-utils\node_modules\debug
    - node_modules\eslint-import-resolver-node\node_modules\debug
  debug@4.3.4:
    - node_modules\lint-staged\node_modules\debug
  debug@4.4.0:
    - node_modules\debug
  debug@4.4.1:
    - node_modules\secretlint\node_modules\debug
    - node_modules\@secretlint\node\node_modules\debug
    - node_modules\@secretlint\formatter\node_modules\debug
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\typescript-estree\node_modules\debug
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\@typescript-eslint\type-utils\node_modules\debug
    - node_modules\electron\node_modules\debug
    - node_modules\@agent-infra\browser-context\node_modules\debug
    - node_modules\extract-zip\node_modules\debug
    - node_modules\sumchecker\node_modules\debug
    - node_modules\@secretlint\core\node_modules\debug
    - node_modules\@secretlint\config-loader\node_modules\debug
    - node_modules\@textlint\linter-formatter\node_modules\debug
    - node_modules\proxy-agent\node_modules\debug
    - node_modules\http-proxy-agent\node_modules\debug
    - node_modules\https-proxy-agent\node_modules\debug
    - node_modules\puppeteer\node_modules\debug
    - node_modules\read-binary-file-arch\node_modules\debug
    - node_modules\galactus\node_modules\debug
    - node_modules\@electron\notarize\node_modules\debug
    - node_modules\@electron\universal\node_modules\debug
    - node_modules\@electron\windows-sign\node_modules\debug
    - node_modules\rc-config-loader\node_modules\debug
    - node_modules\@electron-forge\template-base\node_modules\debug
    - node_modules\pac-proxy-agent\node_modules\debug
    - node_modules\socks-proxy-agent\node_modules\debug
    - node_modules\@puppeteer\browsers\node_modules\debug
    - node_modules\make-fetch-happen\node_modules\debug
    - node_modules\get-uri\node_modules\debug
    - node_modules\micromark\node_modules\debug
    - packages\ui-tars\electron-ipc\node_modules\debug
  decamelize@1.2.0:
    - node_modules\decamelize
  decimal.js@10.5.0:
    - node_modules\decimal.js
  decode-named-character-reference@1.1.0:
    - node_modules\decode-named-character-reference
  decompress-response@6.0.0:
    - node_modules\decompress-response
  deep-eql@5.0.2:
    - node_modules\deep-eql
  deep-is@0.1.4:
    - node_modules\deep-is
  deepmerge@2.2.1:
    - node_modules\deepmerge
  deepmerge@4.3.1:
    - node_modules\@browserbasehq\stagehand\node_modules\deepmerge
  defaults@1.0.4:
    - node_modules\defaults
  defer-to-connect@2.0.1:
    - node_modules\defer-to-connect
  define-data-property@1.1.4:
    - node_modules\define-data-property
  define-properties@1.2.1:
    - node_modules\define-properties
  defuddle@0.6.4(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5)):
    - node_modules\defuddle
  degenerator@5.0.1:
    - node_modules\degenerator
  delayed-stream@1.0.0:
    - node_modules\delayed-stream
  depd@2.0.0:
    - node_modules\depd
  deprecation@2.3.1:
    - node_modules\deprecation
  dequal@2.0.3:
    - node_modules\dequal
  destroy@1.2.0:
    - node_modules\destroy
  detect-indent@6.1.0:
    - node_modules\detect-indent
  detect-libc@1.0.3:
    - node_modules\@parcel\watcher\node_modules\detect-libc
  detect-libc@2.0.3:
    - node_modules\detect-libc
  detect-node-es@1.1.0:
    - node_modules\detect-node-es
  detect-node@2.1.0:
    - node_modules\detect-node
  devlop@1.1.0:
    - node_modules\devlop
  devtools-protocol@0.0.1413902:
    - node_modules\devtools-protocol
  devtools-protocol@0.0.1425554:
    - node_modules\puppeteer-core\node_modules\devtools-protocol
  devtools-protocol@0.0.1464554:
    - node_modules\@agent-infra\browser-context\node_modules\devtools-protocol
  diff@4.0.2:
    - node_modules\diff
  diff@5.2.0:
    - packages\agent-infra\mcp-servers\filesystem\node_modules\diff
  dir-compare@4.2.0:
    - node_modules\dir-compare
  dir-glob@3.0.1:
    - node_modules\dir-glob
  doctrine@2.1.0:
    - node_modules\doctrine
  doctrine@3.0.0:
    - node_modules\eslint\node_modules\doctrine
  dom-accessibility-api@0.5.16:
    - node_modules\dom-accessibility-api
  dom-walk@0.1.2:
    - node_modules\dom-walk
  dot-prop@5.3.0:
    - node_modules\dot-prop
  dot-prop@9.0.0:
    - node_modules\conf\node_modules\dot-prop
  dotenv@16.4.7:
    - node_modules\dotenv
  duck-duck-scrape@2.2.7:
    - node_modules\duck-duck-scrape
  dunder-proto@1.0.1:
    - node_modules\dunder-proto
  eastasianwidth@0.2.0:
    - node_modules\eastasianwidth
  edge-paths@3.0.5:
    - node_modules\edge-paths
  editions@6.21.0:
    - node_modules\editions
  ee-first@1.1.1:
    - node_modules\ee-first
  electron-debug@3.2.0:
    - node_modules\electron-debug
  electron-devtools-installer@3.2.1:
    - node_modules\electron-devtools-installer
  electron-installer-dmg@5.0.1:
    - node_modules\electron-installer-dmg
  electron-is-accelerator@0.1.2:
    - node_modules\electron-is-accelerator
  electron-is-dev@1.2.0:
    - node_modules\electron-is-dev
  electron-localshortcut@3.2.1:
    - node_modules\electron-localshortcut
  electron-log@5.2.4:
    - node_modules\electron-log
  electron-packager-languages@0.5.0:
    - node_modules\electron-packager-languages
  electron-playwright-helpers@1.7.1:
    - node_modules\electron-playwright-helpers
  electron-squirrel-startup@1.0.1:
    - node_modules\electron-squirrel-startup
  electron-store@10.0.1:
    - node_modules\electron-store
  electron-to-chromium@1.5.97:
    - node_modules\electron-to-chromium
  electron-updater@6.3.9:
    - packages\common\electron-build\node_modules\electron-updater
  electron-updater@6.6.2:
    - node_modules\electron-updater
  electron-util@0.17.2:
    - node_modules\electron-util
  electron-vite@3.0.0(vite@6.2.2(@types/node@20.17.17)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0)):
    - node_modules\electron-vite
  electron-winstaller@5.4.0:
    - node_modules\electron-winstaller
  electron@30.5.1:
    - packages\ui-tars\electron-ipc\node_modules\electron
  electron@34.1.1:
    - node_modules\electron
  elliptic@6.6.1:
    - node_modules\elliptic
  emoji-regex@10.4.0:
    - node_modules\mcp-proxy\node_modules\emoji-regex
  emoji-regex@8.0.0:
    - node_modules\emoji-regex
  emoji-regex@9.2.2:
    - node_modules\wrap-ansi\node_modules\emoji-regex
    - node_modules\cli-truncate\node_modules\emoji-regex
    - node_modules\@isaacs\cliui\node_modules\emoji-regex
  encodeurl@1.0.2:
    - node_modules\send\node_modules\encodeurl
  encodeurl@2.0.0:
    - node_modules\encodeurl
  encoding@0.1.13:
    - node_modules\encoding
  end-of-stream@1.4.4:
    - node_modules\end-of-stream
  enhanced-resolve@5.18.1:
    - node_modules\enhanced-resolve
  enquirer@2.4.1:
    - node_modules\enquirer
  entities@4.5.0:
    - node_modules\entities
  env-paths@2.2.1:
    - node_modules\env-paths
  env-paths@3.0.0:
    - node_modules\conf\node_modules\env-paths
  environment@1.1.0:
    - node_modules\environment
  err-code@2.0.3:
    - node_modules\err-code
  error-ex@1.3.2:
    - node_modules\error-ex
  error-stack-parser@2.1.4:
    - node_modules\error-stack-parser
  es-abstract@1.23.9:
    - node_modules\es-abstract
  es-define-property@1.0.1:
    - node_modules\es-define-property
  es-errors@1.3.0:
    - node_modules\es-errors
  es-iterator-helpers@1.2.1:
    - node_modules\es-iterator-helpers
  es-module-lexer@1.6.0:
    - node_modules\es-module-lexer
  es-object-atoms@1.1.1:
    - node_modules\es-object-atoms
  es-set-tostringtag@2.1.0:
    - node_modules\es-set-tostringtag
  es-shim-unscopables@1.0.2:
    - node_modules\es-shim-unscopables
  es-to-primitive@1.3.0:
    - node_modules\es-to-primitive
  es6-error@4.1.1:
    - node_modules\es6-error
  esbuild-register@3.6.0(esbuild@0.25.0):
    - node_modules\esbuild-register
  esbuild@0.23.1:
    - node_modules\tsx\node_modules\esbuild
  esbuild@0.24.2:
    - node_modules\electron-vite\node_modules\esbuild
  esbuild@0.25.0:
    - node_modules\esbuild
  escalade@3.2.0:
    - node_modules\escalade
  escape-html@1.0.3:
    - node_modules\escape-html
  escape-string-regexp@1.0.5:
    - node_modules\figures\node_modules\escape-string-regexp
    - node_modules\strip-outer\node_modules\escape-string-regexp
    - node_modules\trim-repeated\node_modules\escape-string-regexp
  escape-string-regexp@4.0.0:
    - node_modules\escape-string-regexp
  escape-string-regexp@5.0.0:
    - node_modules\@secretlint\secretlint-rule-no-homedir\node_modules\escape-string-regexp
  escodegen@2.1.0:
    - node_modules\escodegen
  eslint-config-prettier@9.1.0(eslint@8.57.1):
    - node_modules\eslint-config-prettier
  eslint-import-resolver-node@0.3.9:
    - node_modules\eslint-import-resolver-node
  eslint-module-utils@2.12.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.7.3))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    - node_modules\eslint-module-utils
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@5.7.3))(eslint@8.57.1):
    - node_modules\eslint-plugin-import
  eslint-plugin-prettier@5.2.3(eslint-config-prettier@9.1.0(eslint@8.57.1))(eslint@8.57.1)(prettier@3.5.0):
    - node_modules\eslint-plugin-prettier
  eslint-plugin-react@7.37.4(eslint@8.57.1):
    - node_modules\eslint-plugin-react
  eslint-scope@5.1.1:
    - node_modules\eslint-scope
  eslint-scope@7.2.2:
    - node_modules\eslint\node_modules\eslint-scope
  eslint-visitor-keys@3.4.3:
    - node_modules\eslint-visitor-keys
  eslint@8.57.1:
    - node_modules\eslint
  espree@9.6.1:
    - node_modules\espree
  esprima@4.0.1:
    - node_modules\esprima
  esquery@1.6.0:
    - node_modules\esquery
  esrecurse@4.3.0:
    - node_modules\esrecurse
  estraverse@4.3.0:
    - node_modules\eslint-scope\node_modules\estraverse
  estraverse@5.3.0:
    - node_modules\estraverse
  estree-util-is-identifier-name@3.0.0:
    - node_modules\estree-util-is-identifier-name
  estree-walker@2.0.2:
    - node_modules\@vue\compiler-sfc\node_modules\estree-walker
    - node_modules\@vue\compiler-core\node_modules\estree-walker
  estree-walker@3.0.3:
    - node_modules\estree-walker
  esutils@2.0.3:
    - node_modules\esutils
  etag@1.8.1:
    - node_modules\etag
  event-target-shim@5.0.1:
    - node_modules\event-target-shim
  eventemitter3@4.0.7:
    - node_modules\p-queue\node_modules\eventemitter3
  eventemitter3@5.0.1:
    - node_modules\eventemitter3
  events@3.3.0:
    - node_modules\events
  eventsource-parser@3.0.2:
    - node_modules\eventsource-parser
  eventsource@3.0.5:
    - node_modules\@modelcontextprotocol\sdk\node_modules\eventsource
  eventsource@4.0.0:
    - node_modules\eventsource
  execa@1.0.0:
    - node_modules\execa
  execa@5.0.1:
    - packages\ui-tars\operators\adb\node_modules\execa
  execa@5.1.1:
    - node_modules\@computer-use\mac-screen-capture-permissions\node_modules\execa
  execa@7.2.0:
    - node_modules\lint-staged\node_modules\execa
    - node_modules\opencommit\node_modules\execa
  exif-parser@0.1.12:
    - node_modules\exif-parser
  expect-type@1.1.0:
    - node_modules\expect-type
  exponential-backoff@3.1.2:
    - node_modules\exponential-backoff
  express-rate-limit@7.5.0(express@5.1.0):
    - node_modules\express-rate-limit
  express-ws@5.0.2(bufferutil@4.0.9)(express@4.21.2)(utf-8-validate@6.0.5):
    - node_modules\express-ws
  express@4.21.2:
    - node_modules\express
  express@5.1.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\express
    - node_modules\express-rate-limit\node_modules\express
    - packages\agent-infra\mcp-http-server\node_modules\express
  extend@3.0.2:
    - node_modules\extend
  extendable-error@0.1.7:
    - node_modules\extendable-error
  external-editor@3.1.0:
    - node_modules\external-editor
  extract-zip@2.0.1:
    - node_modules\extract-zip
  fast-deep-equal@3.1.3:
    - node_modules\fast-deep-equal
  fast-diff@1.3.0:
    - node_modules\fast-diff
  fast-fifo@1.3.2:
    - node_modules\fast-fifo
  fast-glob@3.3.3:
    - node_modules\fast-glob
  fast-json-stable-stringify@2.1.0:
    - node_modules\fast-json-stable-stringify
  fast-levenshtein@2.0.6:
    - node_modules\fast-levenshtein
  fast-shallow-equal@1.0.0:
    - node_modules\fast-shallow-equal
  fast-uri@3.0.6:
    - node_modules\fast-uri
  fast-xml-parser@4.4.1:
    - node_modules\fast-xml-parser
  fastest-stable-stringify@2.0.2:
    - node_modules\fastest-stable-stringify
  fastq@1.19.0:
    - node_modules\fastq
  fd-slicer@1.1.0:
    - node_modules\fd-slicer
  fdir@6.4.3(picomatch@4.0.2):
    - node_modules\fdir
  fdir@6.4.5(picomatch@4.0.2):
    - packages\common\configs\node_modules\fdir
    - packages\common\electron-build\node_modules\fdir
    - packages\ui-tars\action-parser\node_modules\fdir
    - packages\ui-tars\cli\node_modules\fdir
    - packages\ui-tars\electron-ipc\node_modules\fdir
    - packages\ui-tars\operators\adb\node_modules\fdir
    - packages\ui-tars\operators\browser-operator\node_modules\fdir
    - packages\ui-tars\operators\browserbase\node_modules\fdir
    - packages\ui-tars\operators\nut-js\node_modules\fdir
    - packages\ui-tars\sdk\node_modules\fdir
    - packages\ui-tars\shared\node_modules\fdir
    - packages\ui-tars\utio\node_modules\fdir
  figures@3.2.0:
    - node_modules\figures
  file-entry-cache@6.0.1:
    - node_modules\file-entry-cache
  file-type@16.5.4:
    - node_modules\file-type
  file-uri-to-path@1.0.0:
    - node_modules\file-uri-to-path
  filename-reserved-regex@2.0.0:
    - node_modules\filename-reserved-regex
  filenamify@4.3.0:
    - node_modules\filenamify
  fill-range@7.1.1:
    - node_modules\fill-range
  finalhandler@1.3.1:
    - node_modules\finalhandler
  finalhandler@2.1.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\finalhandler
    - node_modules\express-rate-limit\node_modules\finalhandler
    - packages\agent-infra\mcp-http-server\node_modules\finalhandler
  find-root@1.1.0:
    - node_modules\find-root
  find-up@2.1.0:
    - node_modules\read-pkg-up\node_modules\find-up
  find-up@4.1.0:
    - node_modules\@manypkg\find-root\node_modules\find-up
  find-up@5.0.0:
    - node_modules\find-up
  find-up@7.0.0:
    - node_modules\@commitlint\top-level\node_modules\find-up
  flat-cache@3.2.0:
    - node_modules\flat-cache
  flatted@3.3.2:
    - node_modules\flatted
  flora-colossus@2.0.0:
    - node_modules\flora-colossus
  follow-redirects@1.15.9:
    - node_modules\follow-redirects
  for-each@0.3.5:
    - node_modules\for-each
  foreground-child@3.3.0:
    - node_modules\foreground-child
  form-data-encoder@1.7.2:
    - node_modules\form-data-encoder
  form-data@4.0.1:
    - node_modules\form-data
  formdata-node@4.4.1:
    - node_modules\formdata-node
  formik@2.4.6(react@19.1.0):
    - node_modules\formik
  forwarded@0.2.0:
    - node_modules\forwarded
  framer-motion@11.18.2(@emotion/is-prop-valid@1.3.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\framer-motion
  fresh@0.5.2:
    - node_modules\fresh
  fresh@2.0.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\fresh
    - node_modules\express-rate-limit\node_modules\fresh
    - packages\agent-infra\mcp-http-server\node_modules\fresh
  fs-extra@10.1.0:
    - node_modules\fs-extra
  fs-extra@11.3.0:
    - node_modules\@electron\packager\node_modules\fs-extra
    - node_modules\@electron\universal\node_modules\fs-extra
    - node_modules\@electron\windows-sign\node_modules\fs-extra
  fs-extra@7.0.1:
    - node_modules\@changesets\cli\node_modules\fs-extra
    - node_modules\@changesets\pre\node_modules\fs-extra
    - node_modules\@changesets\read\node_modules\fs-extra
    - node_modules\@changesets\config\node_modules\fs-extra
    - node_modules\@changesets\write\node_modules\fs-extra
    - node_modules\@changesets\apply-release-plan\node_modules\fs-extra
    - node_modules\electron-winstaller\node_modules\fs-extra
  fs-extra@8.1.0:
    - node_modules\@manypkg\get-packages\node_modules\fs-extra
    - node_modules\electron\node_modules\fs-extra
    - node_modules\@manypkg\find-root\node_modules\fs-extra
    - node_modules\@electron\get\node_modules\fs-extra
    - packages\ui-tars\electron-ipc\node_modules\fs-extra
  fs-extra@9.1.0:
    - node_modules\@electron\fuses\node_modules\fs-extra
    - node_modules\@electron\notarize\node_modules\fs-extra
  fs-minipass@2.1.0:
    - node_modules\fs-minipass
  fs.realpath@1.0.0:
    - node_modules\fs.realpath
  function-bind@1.1.2:
    - node_modules\function-bind
  function.prototype.name@1.1.8:
    - node_modules\function.prototype.name
  functions-have-names@1.2.3:
    - node_modules\functions-have-names
  galactus@1.0.0:
    - node_modules\galactus
  gensync@1.0.0-beta.2:
    - node_modules\gensync
  get-caller-file@2.0.5:
    - node_modules\get-caller-file
  get-east-asian-width@1.3.0:
    - node_modules\get-east-asian-width
  get-intrinsic@1.2.7:
    - node_modules\get-intrinsic
  get-nonce@1.0.1:
    - node_modules\get-nonce
  get-package-info@1.0.0:
    - node_modules\get-package-info
  get-port@7.1.0:
    - node_modules\get-port
  get-proto@1.0.1:
    - node_modules\get-proto
  get-stream@4.1.0:
    - node_modules\execa\node_modules\get-stream
  get-stream@5.2.0:
    - node_modules\extract-zip\node_modules\get-stream
    - node_modules\cacheable-request\node_modules\get-stream
  get-stream@6.0.1:
    - node_modules\get-stream
  get-symbol-description@1.1.0:
    - node_modules\get-symbol-description
  get-tsconfig@4.10.0:
    - node_modules\get-tsconfig
  get-uri@6.0.4:
    - node_modules\get-uri
  gifwrap@0.10.1:
    - node_modules\gifwrap
  git-raw-commits@4.0.0:
    - node_modules\git-raw-commits
  glob-parent@5.1.2:
    - node_modules\glob-parent
  glob-parent@6.0.2:
    - node_modules\eslint\node_modules\glob-parent
  glob@10.4.5:
    - node_modules\test-exclude\node_modules\glob
    - packages\agent-infra\mcp-client\node_modules\glob
    - packages\agent-infra\mcp-servers\filesystem\node_modules\glob
  glob@11.0.1:
    - node_modules\rimraf\node_modules\glob
  glob@7.2.3:
    - node_modules\glob
    - node_modules\cacache\node_modules\rimraf\node_modules\glob
  glob@8.1.0:
    - node_modules\@electron\node-gyp\node_modules\glob
    - node_modules\cacache\node_modules\glob
  global-agent@3.0.0:
    - node_modules\global-agent
  global-directory@4.0.1:
    - node_modules\global-directory
  global-dirs@3.0.1:
    - node_modules\global-dirs
  global@4.4.0:
    - node_modules\global
  globals@11.12.0:
    - node_modules\@babel\traverse\node_modules\globals
  globals@13.24.0:
    - node_modules\globals
  globalthis@1.0.4:
    - node_modules\globalthis
  globby@11.1.0:
    - node_modules\globby
  globby@14.1.0:
    - node_modules\secretlint\node_modules\globby
  globrex@0.1.2:
    - node_modules\globrex
  gopd@1.2.0:
    - node_modules\gopd
  got@11.8.6:
    - node_modules\got
  graceful-fs@4.2.11:
    - node_modules\graceful-fs
  graphemer@1.4.0:
    - node_modules\graphemer
  graphql@16.10.0:
    - node_modules\graphql
  growly@1.3.0:
    - node_modules\growly
  happy-dom@17.1.1:
    - node_modules\happy-dom
  has-bigints@1.1.0:
    - node_modules\has-bigints
  has-flag@4.0.0:
    - node_modules\has-flag
  has-property-descriptors@1.0.2:
    - node_modules\has-property-descriptors
  has-proto@1.2.0:
    - node_modules\has-proto
  has-symbols@1.1.0:
    - node_modules\has-symbols
  has-tostringtag@1.0.2:
    - node_modules\has-tostringtag
  hash.js@1.1.7:
    - node_modules\hash.js
  hasown@2.0.2:
    - node_modules\hasown
  hast-util-to-jsx-runtime@2.3.6:
    - node_modules\hast-util-to-jsx-runtime
  hast-util-whitespace@3.0.0:
    - node_modules\hast-util-whitespace
  headers-polyfill@4.0.3:
    - node_modules\headers-polyfill
  hmac-drbg@1.0.1:
    - node_modules\hmac-drbg
  hoist-non-react-statics@3.3.2:
    - node_modules\hoist-non-react-statics
  hosted-git-info@2.8.9:
    - node_modules\read-pkg-up\node_modules\hosted-git-info
  hosted-git-info@7.0.2:
    - node_modules\hosted-git-info
  html-encoding-sniffer@4.0.0:
    - node_modules\html-encoding-sniffer
  html-entities@2.5.2:
    - node_modules\html-entities
  html-escaper@2.0.2:
    - node_modules\html-escaper
  html-url-attributes@3.0.1:
    - node_modules\html-url-attributes
  http-cache-semantics@4.1.1:
    - node_modules\http-cache-semantics
  http-errors@2.0.0:
    - node_modules\http-errors
  http-proxy-agent@5.0.0:
    - node_modules\make-fetch-happen\node_modules\http-proxy-agent
  http-proxy-agent@7.0.2:
    - node_modules\http-proxy-agent
  http2-wrapper@1.0.3:
    - node_modules\http2-wrapper
  https-proxy-agent@5.0.1:
    - node_modules\make-fetch-happen\node_modules\https-proxy-agent
  https-proxy-agent@7.0.6:
    - node_modules\https-proxy-agent
  human-id@1.0.2:
    - node_modules\human-id
  human-signals@2.1.0:
    - node_modules\human-signals
  human-signals@4.3.1:
    - node_modules\lint-staged\node_modules\human-signals
    - node_modules\opencommit\node_modules\human-signals
  humanize-ms@1.2.1:
    - node_modules\humanize-ms
  husky@9.1.7:
    - node_modules\husky
  hyphenate-style-name@1.1.0:
    - node_modules\hyphenate-style-name
  iconv-lite@0.4.24:
    - node_modules\external-editor\node_modules\iconv-lite
    - node_modules\body-parser\node_modules\iconv-lite
  iconv-lite@0.6.3:
    - node_modules\iconv-lite
  idb-keyval@6.2.1:
    - node_modules\idb-keyval
  ieee754@1.2.1:
    - node_modules\ieee754
  ignore@5.3.2:
    - node_modules\ignore
  ignore@7.0.5:
    - node_modules\secretlint\node_modules\ignore
  image-q@4.0.0:
    - node_modules\image-q
  immediate@3.0.6:
    - node_modules\immediate
  immutable@5.0.3:
    - node_modules\immutable
  import-fresh@3.3.1:
    - node_modules\import-fresh
  import-meta-resolve@4.1.0:
    - node_modules\import-meta-resolve
  imurmurhash@0.1.4:
    - node_modules\imurmurhash
  indent-string@4.0.0:
    - node_modules\indent-string
  index-to-position@1.1.0:
    - node_modules\index-to-position
  infer-owner@1.0.4:
    - node_modules\infer-owner
  inflight@1.0.6:
    - node_modules\inflight
  inherits@2.0.4:
    - node_modules\inherits
  ini@2.0.0:
    - node_modules\global-dirs\node_modules\ini
  ini@3.0.1:
    - node_modules\opencommit\node_modules\ini
  ini@4.1.1:
    - node_modules\ini
  inline-style-parser@0.2.4:
    - node_modules\inline-style-parser
  inline-style-prefixer@7.0.1:
    - node_modules\inline-style-prefixer
  inquirer@8.2.4:
    - packages\ui-tars\operators\adb\node_modules\inquirer
  inquirer@9.3.7:
    - node_modules\inquirer
  internal-slot@1.1.0:
    - node_modules\internal-slot
  interpret@1.4.0:
    - node_modules\shelljs\node_modules\interpret
  interpret@3.1.1:
    - node_modules\interpret
  ip-address@9.0.5:
    - node_modules\ip-address
  ipaddr.js@1.9.1:
    - node_modules\ipaddr.js
  is-alphabetical@2.0.1:
    - node_modules\is-alphabetical
  is-alphanumerical@2.0.1:
    - node_modules\is-alphanumerical
  is-array-buffer@3.0.5:
    - node_modules\is-array-buffer
  is-arrayish@0.2.1:
    - node_modules\is-arrayish
  is-arrayish@0.3.2:
    - node_modules\simple-swizzle\node_modules\is-arrayish
  is-async-function@2.1.1:
    - node_modules\is-async-function
  is-bigint@1.1.0:
    - node_modules\is-bigint
  is-boolean-object@1.2.2:
    - node_modules\is-boolean-object
  is-callable@1.2.7:
    - node_modules\is-callable
  is-core-module@2.16.1:
    - node_modules\is-core-module
  is-data-view@1.0.2:
    - node_modules\is-data-view
  is-date-object@1.1.0:
    - node_modules\is-date-object
  is-decimal@2.0.1:
    - node_modules\is-decimal
  is-docker@2.2.1:
    - node_modules\is-docker
  is-extglob@2.1.1:
    - node_modules\is-extglob
  is-finalizationregistry@1.1.1:
    - node_modules\is-finalizationregistry
  is-fullwidth-code-point@3.0.0:
    - node_modules\is-fullwidth-code-point
  is-fullwidth-code-point@4.0.0:
    - node_modules\slice-ansi\node_modules\is-fullwidth-code-point
  is-function@1.0.2:
    - node_modules\is-function
  is-generator-function@1.1.0:
    - node_modules\is-generator-function
  is-glob@4.0.3:
    - node_modules\is-glob
  is-hexadecimal@2.0.1:
    - node_modules\is-hexadecimal
  is-interactive@1.0.0:
    - node_modules\is-interactive
  is-lambda@1.0.1:
    - node_modules\is-lambda
  is-map@2.0.3:
    - node_modules\is-map
  is-node-process@1.2.0:
    - node_modules\is-node-process
  is-number-object@1.1.1:
    - node_modules\is-number-object
  is-number@7.0.0:
    - node_modules\is-number
  is-obj@2.0.0:
    - node_modules\is-obj
  is-path-inside@3.0.3:
    - node_modules\is-path-inside
  is-plain-obj@4.1.0:
    - node_modules\is-plain-obj
  is-plain-object@5.0.0:
    - node_modules\is-plain-object
  is-potential-custom-element-name@1.0.1:
    - node_modules\is-potential-custom-element-name
  is-promise@4.0.0:
    - node_modules\is-promise
  is-regex@1.2.1:
    - node_modules\is-regex
  is-set@2.0.3:
    - node_modules\is-set
  is-shared-array-buffer@1.0.4:
    - node_modules\is-shared-array-buffer
  is-stream@1.1.0:
    - node_modules\execa\node_modules\is-stream
  is-stream@2.0.1:
    - node_modules\is-stream
  is-stream@3.0.0:
    - node_modules\lint-staged\node_modules\is-stream
    - node_modules\opencommit\node_modules\is-stream
  is-string@1.1.1:
    - node_modules\is-string
  is-subdir@1.2.0:
    - node_modules\is-subdir
  is-symbol@1.1.1:
    - node_modules\is-symbol
  is-text-path@2.0.0:
    - node_modules\is-text-path
  is-typed-array@1.1.15:
    - node_modules\is-typed-array
  is-unicode-supported@0.1.0:
    - node_modules\is-unicode-supported
  is-weakmap@2.0.2:
    - node_modules\is-weakmap
  is-weakref@1.1.1:
    - node_modules\is-weakref
  is-weakset@2.0.4:
    - node_modules\is-weakset
  is-windows@1.0.2:
    - node_modules\is-windows
  is-wsl@2.2.0:
    - node_modules\is-wsl
  isarray@1.0.0:
    - node_modules\jszip\node_modules\isarray
  isarray@2.0.5:
    - node_modules\isarray
  isbinaryfile@4.0.10:
    - node_modules\isbinaryfile
  isbinaryfile@5.0.4:
    - packages\agent-infra\mcp-servers\browser\node_modules\isbinaryfile
  isexe@2.0.0:
    - node_modules\isexe
  isexe@3.1.1:
    - packages\agent-infra\browser\node_modules\isexe
  isomorphic-fetch@3.0.0(encoding@0.1.13):
    - node_modules\isomorphic-fetch
  isomorphic-rslog@0.0.6:
    - node_modules\isomorphic-rslog
  istanbul-lib-coverage@3.2.2:
    - node_modules\istanbul-lib-coverage
  istanbul-lib-instrument@6.0.3:
    - node_modules\istanbul-lib-instrument
  istanbul-lib-report@3.0.1:
    - node_modules\istanbul-lib-report
  istanbul-lib-source-maps@5.0.6:
    - node_modules\istanbul-lib-source-maps
  istanbul-reports@3.1.7:
    - node_modules\istanbul-reports
  istextorbinary@9.5.0:
    - node_modules\istextorbinary
  iterator.prototype@1.1.5:
    - node_modules\iterator.prototype
  jackspeak@3.4.3:
    - node_modules\jackspeak
  jackspeak@4.0.3:
    - node_modules\rimraf\node_modules\jackspeak
  javascript-natural-sort@0.7.1:
    - node_modules\javascript-natural-sort
  jimp@0.22.10(encoding@0.1.13):
    - node_modules\@computer-use\nut-js\node_modules\jimp
    - node_modules\@computer-use\shared\node_modules\jimp
  jimp@1.6.0:
    - node_modules\jimp
  jiti@2.4.2:
    - node_modules\jiti
  jose@6.0.11:
    - node_modules\jose
  jpeg-js@0.4.4:
    - node_modules\jpeg-js
  js-cookie@2.2.1:
    - node_modules\js-cookie
  js-tiktoken@1.0.19:
    - node_modules\js-tiktoken
  js-tokens@4.0.0:
    - node_modules\js-tokens
  js-yaml@3.14.1:
    - node_modules\@changesets\parse\node_modules\js-yaml
    - node_modules\read-yaml-file\node_modules\js-yaml
    - node_modules\@textlint\linter-formatter\node_modules\js-yaml
  js-yaml@4.1.0:
    - node_modules\js-yaml
  jsbn@1.1.0:
    - node_modules\jsbn
  jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\jsdom
  jsesc@3.1.0:
    - node_modules\jsesc
  json-buffer@3.0.1:
    - node_modules\json-buffer
  json-parse-even-better-errors@2.3.1:
    - node_modules\json-parse-even-better-errors
  json-schema-traverse@0.4.1:
    - node_modules\eslint\node_modules\json-schema-traverse
    - node_modules\@eslint\eslintrc\node_modules\json-schema-traverse
    - node_modules\@modelcontextprotocol\sdk\node_modules\json-schema-traverse
  json-schema-traverse@1.0.0:
    - node_modules\json-schema-traverse
  json-schema-typed@8.0.1:
    - node_modules\json-schema-typed
  json-stable-stringify-without-jsonify@1.0.1:
    - node_modules\json-stable-stringify-without-jsonify
  json-stringify-safe@5.0.1:
    - node_modules\json-stringify-safe
  json5@1.0.2:
    - node_modules\eslint-plugin-import\node_modules\json5
  json5@2.2.3:
    - node_modules\json5
  jsonfile@4.0.0:
    - node_modules\@changesets\cli\node_modules\jsonfile
    - node_modules\@changesets\pre\node_modules\jsonfile
    - node_modules\@changesets\read\node_modules\jsonfile
    - node_modules\@changesets\config\node_modules\jsonfile
    - node_modules\@changesets\write\node_modules\jsonfile
    - node_modules\@manypkg\get-packages\node_modules\jsonfile
    - node_modules\@changesets\apply-release-plan\node_modules\jsonfile
    - node_modules\electron\node_modules\jsonfile
    - node_modules\@manypkg\find-root\node_modules\jsonfile
    - node_modules\@electron\get\node_modules\jsonfile
    - node_modules\electron-winstaller\node_modules\jsonfile
    - packages\ui-tars\electron-ipc\node_modules\jsonfile
  jsonfile@6.1.0:
    - node_modules\jsonfile
  jsonparse@1.3.1:
    - node_modules\jsonparse
  jsonrepair@3.12.0:
    - node_modules\jsonrepair
  jsx-ast-utils@3.3.5:
    - node_modules\jsx-ast-utils
  jszip@3.10.1:
    - node_modules\jszip
  junk@3.1.0:
    - node_modules\junk
  keyboardevent-from-electron-accelerator@2.0.0:
    - node_modules\keyboardevent-from-electron-accelerator
  keyboardevents-areequal@0.2.2:
    - node_modules\keyboardevents-areequal
  keyv@4.5.4:
    - node_modules\keyv
  langsmith@0.3.14(openai@4.87.3(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.24.2)):
    - node_modules\langsmith
  lazy-val@1.0.5:
    - node_modules\lazy-val
  levn@0.4.1:
    - node_modules\levn
  lie@3.3.0:
    - node_modules\lie
  lightningcss-win32-x64-msvc@1.29.2:
    - node_modules\lightningcss-win32-x64-msvc
  lightningcss@1.29.2:
    - node_modules\lightningcss
  lilconfig@2.1.0:
    - node_modules\lilconfig
  lines-and-columns@1.2.4:
    - node_modules\lines-and-columns
  linkify-it@5.0.0:
    - node_modules\linkify-it
  lint-staged@14.0.1(enquirer@2.4.1):
    - node_modules\lint-staged
  listr2@6.6.1(enquirer@2.4.1):
    - node_modules\lint-staged\node_modules\listr2
  listr2@7.0.2:
    - node_modules\listr2
  load-bmfont@1.4.2:
    - node_modules\load-bmfont
  load-json-file@2.0.0:
    - node_modules\load-json-file
  locate-path@2.0.0:
    - node_modules\read-pkg-up\node_modules\locate-path
  locate-path@5.0.0:
    - node_modules\locate-path
  locate-path@6.0.0:
    - node_modules\find-up\node_modules\locate-path
  locate-path@7.2.0:
    - node_modules\@commitlint\top-level\node_modules\locate-path
  lodash-es@4.17.21:
    - node_modules\lodash-es
  lodash.camelcase@4.3.0:
    - node_modules\lodash.camelcase
  lodash.escaperegexp@4.1.2:
    - node_modules\lodash.escaperegexp
  lodash.get@4.4.2:
    - node_modules\lodash.get
  lodash.isequal@4.5.0:
    - node_modules\lodash.isequal
  lodash.isnumber@3.0.3:
    - node_modules\lodash.isnumber
  lodash.isplainobject@4.0.6:
    - node_modules\lodash.isplainobject
  lodash.kebabcase@4.1.1:
    - node_modules\lodash.kebabcase
  lodash.merge@4.6.2:
    - node_modules\lodash.merge
  lodash.mergewith@4.6.2:
    - node_modules\lodash.mergewith
  lodash.snakecase@4.1.1:
    - node_modules\lodash.snakecase
  lodash.sortby@4.7.0:
    - node_modules\lodash.sortby
  lodash.startcase@4.4.0:
    - node_modules\lodash.startcase
  lodash.truncate@4.4.2:
    - node_modules\lodash.truncate
  lodash.uniq@4.5.0:
    - node_modules\lodash.uniq
  lodash.uniqwith@4.5.0:
    - node_modules\lodash.uniqwith
  lodash.upperfirst@4.3.1:
    - node_modules\lodash.upperfirst
  lodash@4.17.21:
    - node_modules\lodash
  log-symbols@4.1.0:
    - node_modules\log-symbols
  log-update@5.0.1:
    - node_modules\log-update
  longest-streak@3.1.0:
    - node_modules\longest-streak
  loose-envify@1.4.0:
    - node_modules\loose-envify
  loupe@3.1.3:
    - node_modules\loupe
  lowercase-keys@2.0.0:
    - node_modules\lowercase-keys
  lru-cache@10.4.3:
    - node_modules\lru-cache
  lru-cache@11.0.2:
    - node_modules\rimraf\node_modules\lru-cache
  lru-cache@5.1.1:
    - node_modules\@babel\helper-compilation-targets\node_modules\lru-cache
  lru-cache@7.18.3:
    - node_modules\proxy-agent\node_modules\lru-cache
    - node_modules\make-fetch-happen\node_modules\lru-cache
    - node_modules\cacache\node_modules\lru-cache
  lucide-react@0.487.0(react@19.1.0):
    - node_modules\lucide-react
  lz-string@1.5.0:
    - node_modules\lz-string
  macos-version@5.2.1:
    - node_modules\macos-version
  magic-string@0.30.17:
    - node_modules\magic-string
  magicast@0.3.5:
    - node_modules\magicast
  make-dir@4.0.0:
    - node_modules\make-dir
  make-error@1.3.6:
    - node_modules\make-error
  make-fetch-happen@10.2.1:
    - node_modules\make-fetch-happen
  map-age-cleaner@0.1.3:
    - node_modules\map-age-cleaner
  markdown-it@14.1.0:
    - node_modules\markdown-it
  matcher@3.0.0:
    - node_modules\matcher
  math-intrinsics@1.1.0:
    - node_modules\math-intrinsics
  mathml-to-latex@1.5.0:
    - node_modules\mathml-to-latex
  mcp-proxy@3.0.3:
    - packages\agent-infra\mcp-servers\browser\node_modules\mcp-proxy
  mcp-proxy@5.4.0:
    - node_modules\mcp-proxy
  mdast-util-from-markdown@2.0.2:
    - node_modules\mdast-util-from-markdown
  mdast-util-mdx-expression@2.0.1:
    - node_modules\mdast-util-mdx-expression
  mdast-util-mdx-jsx@3.2.0:
    - node_modules\mdast-util-mdx-jsx
  mdast-util-mdxjs-esm@2.0.1:
    - node_modules\mdast-util-mdxjs-esm
  mdast-util-phrasing@4.1.0:
    - node_modules\mdast-util-phrasing
  mdast-util-to-hast@13.2.0:
    - node_modules\mdast-util-to-hast
  mdast-util-to-markdown@2.1.2:
    - node_modules\mdast-util-to-markdown
  mdast-util-to-string@4.0.0:
    - node_modules\mdast-util-to-string
  mdn-data@2.0.14:
    - node_modules\mdn-data
  mdurl@2.0.0:
    - node_modules\mdurl
  media-typer@0.3.0:
    - node_modules\media-typer
  media-typer@1.1.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\media-typer
    - node_modules\express-rate-limit\node_modules\media-typer
    - packages\agent-infra\mcp-http-server\node_modules\media-typer
  medium-zoom@1.1.0:
    - node_modules\medium-zoom
  mem@4.3.0:
    - node_modules\mem
  meow@12.1.1:
    - node_modules\meow
  merge-descriptors@1.0.3:
    - node_modules\merge-descriptors
  merge-descriptors@2.0.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\merge-descriptors
    - node_modules\express-rate-limit\node_modules\merge-descriptors
    - packages\agent-infra\mcp-http-server\node_modules\merge-descriptors
  merge-stream@2.0.0:
    - node_modules\merge-stream
  merge2@1.4.1:
    - node_modules\merge2
  methods@1.1.2:
    - node_modules\methods
  micromark-core-commonmark@2.0.3:
    - node_modules\micromark-core-commonmark
  micromark-factory-destination@2.0.1:
    - node_modules\micromark-factory-destination
  micromark-factory-label@2.0.1:
    - node_modules\micromark-factory-label
  micromark-factory-space@2.0.1:
    - node_modules\micromark-factory-space
  micromark-factory-title@2.0.1:
    - node_modules\micromark-factory-title
  micromark-factory-whitespace@2.0.1:
    - node_modules\micromark-factory-whitespace
  micromark-util-character@2.1.1:
    - node_modules\micromark-util-character
  micromark-util-chunked@2.0.1:
    - node_modules\micromark-util-chunked
  micromark-util-classify-character@2.0.1:
    - node_modules\micromark-util-classify-character
  micromark-util-combine-extensions@2.0.1:
    - node_modules\micromark-util-combine-extensions
  micromark-util-decode-numeric-character-reference@2.0.2:
    - node_modules\micromark-util-decode-numeric-character-reference
  micromark-util-decode-string@2.0.1:
    - node_modules\micromark-util-decode-string
  micromark-util-encode@2.0.1:
    - node_modules\micromark-util-encode
  micromark-util-html-tag-name@2.0.1:
    - node_modules\micromark-util-html-tag-name
  micromark-util-normalize-identifier@2.0.1:
    - node_modules\micromark-util-normalize-identifier
  micromark-util-resolve-all@2.0.1:
    - node_modules\micromark-util-resolve-all
  micromark-util-sanitize-uri@2.0.1:
    - node_modules\micromark-util-sanitize-uri
  micromark-util-subtokenize@2.1.0:
    - node_modules\micromark-util-subtokenize
  micromark-util-symbol@2.0.1:
    - node_modules\micromark-util-symbol
  micromark-util-types@2.0.2:
    - node_modules\micromark-util-types
  micromark@4.0.2:
    - node_modules\micromark
  micromatch@4.0.5:
    - node_modules\lint-staged\node_modules\micromatch
  micromatch@4.0.8:
    - node_modules\micromatch
  mime-db@1.52.0:
    - node_modules\mime-db
  mime-db@1.54.0:
    - node_modules\mime-types\node_modules\mime-db
  mime-types@2.1.35:
    - node_modules\@electron-forge\publisher-github\node_modules\mime-types
    - node_modules\form-data\node_modules\mime-types
    - node_modules\type-is\node_modules\mime-types
    - node_modules\accepts\node_modules\mime-types
  mime-types@3.0.1:
    - node_modules\mime-types
  mime@1.6.0:
    - node_modules\mime
  mime@3.0.0:
    - node_modules\@jimp\core\node_modules\mime
  mimic-fn@2.1.0:
    - node_modules\mimic-fn
  mimic-fn@4.0.0:
    - node_modules\lint-staged\node_modules\mimic-fn
    - node_modules\opencommit\node_modules\mimic-fn
  mimic-function@5.0.1:
    - node_modules\mimic-function
  mimic-response@1.0.1:
    - node_modules\mimic-response
  mimic-response@3.1.0:
    - node_modules\decompress-response\node_modules\mimic-response
  min-document@2.19.0:
    - node_modules\min-document
  minimalistic-assert@1.0.1:
    - node_modules\minimalistic-assert
  minimalistic-crypto-utils@1.0.1:
    - node_modules\minimalistic-crypto-utils
  minimatch@10.0.1:
    - node_modules\rimraf\node_modules\minimatch
    - packages\agent-infra\mcp-servers\filesystem\node_modules\minimatch
  minimatch@3.1.2:
    - node_modules\minimatch
  minimatch@5.1.6:
    - node_modules\@electron\node-gyp\node_modules\minimatch
    - node_modules\cacache\node_modules\glob\node_modules\minimatch
  minimatch@9.0.5:
    - node_modules\test-exclude\node_modules\minimatch
    - node_modules\@electron-toolkit\eslint-config-ts\node_modules\minimatch
    - node_modules\@electron\universal\node_modules\minimatch
    - packages\agent-infra\mcp-client\node_modules\minimatch
    - packages\agent-infra\mcp-servers\filesystem\node_modules\glob\node_modules\minimatch
  minimist@1.2.8:
    - node_modules\minimist
  minipass-collect@1.0.2:
    - node_modules\minipass-collect
  minipass-fetch@2.1.2:
    - node_modules\minipass-fetch
  minipass-flush@1.0.5:
    - node_modules\minipass-flush
  minipass-pipeline@1.2.4:
    - node_modules\minipass-pipeline
  minipass-sized@1.0.3:
    - node_modules\minipass-sized
  minipass@3.3.6:
    - node_modules\minipass
  minipass@5.0.0:
    - node_modules\tar\node_modules\minipass
  minipass@7.1.2:
    - node_modules\rimraf\node_modules\minipass
    - node_modules\test-exclude\node_modules\minipass
    - node_modules\path-scurry\node_modules\minipass
    - packages\agent-infra\mcp-client\node_modules\minipass
    - packages\agent-infra\mcp-servers\filesystem\node_modules\minipass
  minizlib@2.1.2:
    - node_modules\minizlib
  mitt@3.0.1:
    - node_modules\mitt
  mkdirp@0.5.6:
    - node_modules\unzip-crx-3\node_modules\mkdirp
    - node_modules\temp\node_modules\mkdirp
  mkdirp@1.0.4:
    - node_modules\mkdirp
  motion-dom@11.18.1:
    - node_modules\motion-dom
  motion-utils@11.18.1:
    - node_modules\motion-utils
  mri@1.2.0:
    - node_modules\mri
  mrmime@2.0.1:
    - node_modules\mrmime
  ms@2.0.0:
    - node_modules\electron-squirrel-startup\node_modules\ms
    - node_modules\body-parser\node_modules\ms
    - node_modules\express\node_modules\ms
    - node_modules\get-package-info\node_modules\ms
    - node_modules\send\node_modules\debug\node_modules\ms
    - node_modules\finalhandler\node_modules\ms
  ms@2.1.2:
    - node_modules\lint-staged\node_modules\ms
  ms@2.1.3:
    - node_modules\ms
  msw@2.7.0(@types/node@20.17.17)(typescript@5.7.3):
    - node_modules\msw
  mustache@4.2.0:
    - node_modules\mustache
  mute-stream@0.0.8:
    - packages\ui-tars\operators\adb\node_modules\mute-stream
  mute-stream@1.0.0:
    - node_modules\inquirer\node_modules\mute-stream
  mute-stream@2.0.0:
    - node_modules\mute-stream
  nano-css@5.6.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\nano-css
  nanoid@3.3.8:
    - node_modules\nanoid
  natural-compare-lite@1.4.0:
    - node_modules\natural-compare-lite
  natural-compare@1.4.0:
    - node_modules\natural-compare
  needle@3.3.1:
    - node_modules\needle
  negotiator@0.6.3:
    - node_modules\accepts\node_modules\negotiator
  negotiator@0.6.4:
    - node_modules\negotiator
  negotiator@1.0.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\negotiator
    - node_modules\express-rate-limit\node_modules\negotiator
    - packages\agent-infra\mcp-http-server\node_modules\negotiator
  netmask@2.0.2:
    - node_modules\netmask
  new-github-issue-url@0.2.1:
    - node_modules\new-github-issue-url
  next-themes@0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\next-themes
  nice-try@1.0.5:
    - node_modules\nice-try
  node-abi@3.74.0:
    - node_modules\node-abi
  node-abort-controller@3.1.1:
    - node_modules\node-abort-controller
  node-addon-api@5.0.0:
    - node_modules\node-addon-api
  node-addon-api@7.1.1:
    - node_modules\@parcel\watcher\node_modules\node-addon-api
  node-api-version@0.2.0:
    - node_modules\node-api-version
  node-domexception@1.0.0:
    - node_modules\node-domexception
  node-fetch@2.7.0(encoding@0.1.13):
    - node_modules\node-fetch
  node-forge@1.3.1:
    - node_modules\node-forge
  node-gyp-build@4.8.4:
    - node_modules\node-gyp-build
  node-machine-id@1.1.12:
    - node_modules\node-machine-id
  node-notifier@10.0.1:
    - node_modules\node-notifier
  node-releases@2.0.19:
    - node_modules\node-releases
  nopt@6.0.0:
    - node_modules\nopt
  normalize-package-data@2.5.0:
    - node_modules\read-pkg-up\node_modules\normalize-package-data
  normalize-package-data@6.0.2:
    - node_modules\normalize-package-data
  normalize-url@6.1.0:
    - node_modules\normalize-url
  npm-run-path@2.0.2:
    - node_modules\execa\node_modules\npm-run-path
  npm-run-path@4.0.1:
    - node_modules\npm-run-path
  npm-run-path@5.3.0:
    - node_modules\lint-staged\node_modules\npm-run-path
    - node_modules\opencommit\node_modules\npm-run-path
  nwsapi@2.2.18:
    - node_modules\nwsapi
  object-assign@4.1.1:
    - node_modules\object-assign
  object-inspect@1.13.4:
    - node_modules\object-inspect
  object-keys@1.1.1:
    - node_modules\object-keys
  object.assign@4.1.7:
    - node_modules\object.assign
  object.entries@1.1.8:
    - node_modules\object.entries
  object.fromentries@2.0.8:
    - node_modules\object.fromentries
  object.groupby@1.0.3:
    - node_modules\object.groupby
  object.values@1.2.1:
    - node_modules\object.values
  omggif@1.0.10:
    - node_modules\omggif
  on-finished@2.4.1:
    - node_modules\on-finished
  once@1.4.0:
    - node_modules\once
  onetime@5.1.2:
    - node_modules\onetime
  onetime@6.0.0:
    - node_modules\lint-staged\node_modules\onetime
    - node_modules\opencommit\node_modules\onetime
  openai@4.87.3(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.24.2):
    - node_modules\openai
  openai@4.93.0(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.24.2):
    - node_modules\opencommit\node_modules\openai
    - node_modules\@langchain\openai\node_modules\openai
  openai@5.5.1(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.25.76):
    - node_modules\@browserbasehq\stagehand\node_modules\openai
    - packages\ui-tars\sdk\node_modules\openai
  opencommit@3.2.5(encoding@0.1.13)(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5)):
    - node_modules\opencommit
  optionator@0.9.4:
    - node_modules\optionator
  ora@5.4.1:
    - node_modules\ora
  os-tmpdir@1.0.2:
    - node_modules\os-tmpdir
  outdent@0.5.0:
    - node_modules\outdent
  outvariant@1.4.3:
    - node_modules\outvariant
  own-keys@1.0.1:
    - node_modules\own-keys
  p-cancelable@2.1.1:
    - node_modules\p-cancelable
  p-defer@1.0.0:
    - node_modules\p-defer
  p-filter@2.1.0:
    - node_modules\p-filter
  p-finally@1.0.0:
    - node_modules\p-finally
  p-is-promise@2.1.0:
    - node_modules\p-is-promise
  p-limit@1.3.0:
    - node_modules\read-pkg-up\node_modules\p-limit
  p-limit@2.3.0:
    - node_modules\p-limit
  p-limit@3.1.0:
    - node_modules\find-up\node_modules\p-limit
    - node_modules\dir-compare\node_modules\p-limit
  p-limit@4.0.0:
    - node_modules\@commitlint\top-level\node_modules\p-limit
  p-locate@2.0.0:
    - node_modules\read-pkg-up\node_modules\p-locate
  p-locate@4.1.0:
    - node_modules\p-locate
  p-locate@5.0.0:
    - node_modules\find-up\node_modules\p-locate
  p-locate@6.0.0:
    - node_modules\@commitlint\top-level\node_modules\p-locate
  p-map@2.1.0:
    - node_modules\p-map
  p-map@4.0.0:
    - node_modules\cacache\node_modules\p-map
  p-map@7.0.3:
    - node_modules\@secretlint\node\node_modules\p-map
  p-queue@6.6.2:
    - node_modules\p-queue
  p-retry@4.6.2:
    - node_modules\p-retry
  p-timeout@3.2.0:
    - node_modules\p-timeout
  p-try@1.0.0:
    - node_modules\read-pkg-up\node_modules\p-try
  p-try@2.2.0:
    - node_modules\p-try
  pac-proxy-agent@7.2.0:
    - node_modules\pac-proxy-agent
  pac-resolver@7.0.1:
    - node_modules\pac-resolver
  package-json-from-dist@1.0.1:
    - node_modules\package-json-from-dist
  package-manager-detector@0.2.9:
    - node_modules\package-manager-detector
  pako@1.0.11:
    - node_modules\pako
  parent-module@1.0.1:
    - node_modules\parent-module
  parse-author@2.0.0:
    - node_modules\parse-author
  parse-bmfont-ascii@1.0.6:
    - node_modules\parse-bmfont-ascii
  parse-bmfont-binary@1.0.6:
    - node_modules\parse-bmfont-binary
  parse-bmfont-xml@1.1.6:
    - node_modules\parse-bmfont-xml
  parse-entities@4.0.2:
    - node_modules\parse-entities
  parse-headers@2.0.5:
    - node_modules\parse-headers
  parse-json@2.2.0:
    - node_modules\load-json-file\node_modules\parse-json
  parse-json@5.2.0:
    - node_modules\parse-json
  parse-json@8.3.0:
    - node_modules\read-pkg\node_modules\parse-json
  parse5@7.2.1:
    - node_modules\parse5
  parseurl@1.3.3:
    - node_modules\parseurl
  path-exists@3.0.0:
    - node_modules\read-pkg-up\node_modules\path-exists
  path-exists@4.0.0:
    - node_modules\path-exists
  path-exists@5.0.0:
    - node_modules\@commitlint\top-level\node_modules\path-exists
  path-is-absolute@1.0.1:
    - node_modules\path-is-absolute
  path-key@2.0.1:
    - node_modules\execa\node_modules\path-key
  path-key@3.1.1:
    - node_modules\path-key
  path-key@4.0.0:
    - node_modules\lint-staged\node_modules\path-key
    - node_modules\opencommit\node_modules\path-key
  path-parse@1.0.7:
    - node_modules\path-parse
  path-scurry@1.11.1:
    - node_modules\path-scurry
  path-scurry@2.0.0:
    - node_modules\rimraf\node_modules\path-scurry
  path-to-regexp@0.1.12:
    - node_modules\express\node_modules\path-to-regexp
  path-to-regexp@6.3.0:
    - node_modules\path-to-regexp
  path-to-regexp@8.2.0:
    - node_modules\router\node_modules\path-to-regexp
  path-type@2.0.0:
    - node_modules\read-pkg-up\node_modules\path-type
  path-type@4.0.0:
    - node_modules\path-type
  path-type@6.0.0:
    - node_modules\secretlint\node_modules\path-type
  pathe@2.0.3:
    - node_modules\pathe
  pathval@2.0.0:
    - node_modules\pathval
  pe-library@1.0.1:
    - node_modules\pe-library
  peek-readable@4.1.0:
    - node_modules\peek-readable
  pend@1.2.0:
    - node_modules\pend
  phin@3.7.1:
    - node_modules\phin
  picocolors@1.1.1:
    - node_modules\picocolors
  picomatch@2.3.1:
    - node_modules\lint-staged\node_modules\picomatch
    - node_modules\micromatch\node_modules\picomatch
  picomatch@4.0.2:
    - node_modules\picomatch
  pidtree@0.6.0:
    - node_modules\pidtree
  pify@2.3.0:
    - node_modules\pify
  pify@4.0.1:
    - node_modules\read-yaml-file\node_modules\pify
  pixelmatch@4.0.2:
    - node_modules\pixelmatch
  pixelmatch@5.3.0:
    - node_modules\@jimp\diff\node_modules\pixelmatch
  pkce-challenge@5.0.0:
    - node_modules\pkce-challenge
  playwright-core@1.50.1:
    - node_modules\playwright-core
  playwright@1.50.1:
    - node_modules\playwright
  plist@3.1.0:
    - node_modules\plist
  pluralize@2.0.0:
    - node_modules\@textlint\linter-formatter\node_modules\pluralize
  pluralize@8.0.0:
    - node_modules\pluralize
  pngjs@3.4.0:
    - node_modules\pixelmatch\node_modules\pngjs
  pngjs@6.0.0:
    - node_modules\pngjs
  pngjs@7.0.0:
    - node_modules\@jimp\js-png\node_modules\pngjs
  possible-typed-array-names@1.1.0:
    - node_modules\possible-typed-array-names
  postcss@8.5.3:
    - node_modules\postcss
  postject@1.0.0-alpha.6:
    - node_modules\postject
  prelude-ls@1.2.1:
    - node_modules\prelude-ls
  prettier-linter-helpers@1.0.0:
    - node_modules\prettier-linter-helpers
  prettier@2.8.8:
    - node_modules\@changesets\write\node_modules\prettier
    - node_modules\@changesets\apply-release-plan\node_modules\prettier
  prettier@3.5.0:
    - node_modules\prettier
  pretty-format@27.5.1:
    - node_modules\pretty-format
  proc-log@2.0.1:
    - node_modules\proc-log
  process-nextick-args@2.0.1:
    - node_modules\process-nextick-args
  process@0.11.10:
    - node_modules\process
  progress@2.0.3:
    - node_modules\progress
  promise-inflight@1.0.1:
    - node_modules\promise-inflight
  promise-retry@2.0.1:
    - node_modules\promise-retry
  prop-types@15.8.1:
    - node_modules\prop-types
  property-information@7.0.0:
    - node_modules\property-information
  proxy-addr@2.0.7:
    - node_modules\proxy-addr
  proxy-agent@6.5.0:
    - node_modules\proxy-agent
  proxy-from-env@1.1.0:
    - node_modules\proxy-from-env
  psl@1.15.0:
    - node_modules\psl
  pump@3.0.2:
    - node_modules\pump
  punycode.js@2.3.1:
    - node_modules\punycode.js
  punycode@2.3.1:
    - node_modules\punycode
  puppeteer-core@24.14.0(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\@agent-infra\browser-context\node_modules\puppeteer-core
  puppeteer-core@24.4.0(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\puppeteer\node_modules\puppeteer-core
  puppeteer-core@24.7.2(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\puppeteer-core
  puppeteer@24.4.0(bufferutil@4.0.9)(typescript@5.7.3)(utf-8-validate@6.0.5):
    - node_modules\puppeteer
  qs@6.13.0:
    - node_modules\qs
  qs@6.13.1:
    - apps\ui-tars\src\renderer\node_modules\qs
  qs@6.14.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\qs
    - node_modules\express-rate-limit\node_modules\qs
    - packages\agent-infra\mcp-http-server\node_modules\qs
  querystringify@2.2.0:
    - node_modules\querystringify
  queue-microtask@1.2.3:
    - node_modules\queue-microtask
  quick-lru@5.1.1:
    - node_modules\quick-lru
  range-parser@1.2.1:
    - node_modules\range-parser
  raw-body@2.5.2:
    - node_modules\body-parser\node_modules\raw-body
  raw-body@3.0.0:
    - node_modules\raw-body
  rc-config-loader@4.1.3:
    - node_modules\rc-config-loader
  react-countdown@2.3.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\react-countdown
  react-countup@6.5.3(react@19.1.0):
    - node_modules\react-countup
  react-dom@18.3.1(react@18.3.1):
    - node_modules\vitest-browser-react\node_modules\react-dom
    - packages\ui-tars\sdk\node_modules\react-dom
  react-dom@19.1.0(react@19.1.0):
    - node_modules\react-dom
  react-fast-compare@2.0.4:
    - node_modules\react-fast-compare
  react-hook-form@7.46.1(react@19.1.0):
    - node_modules\react-hook-form
  react-icons@5.4.0(react@19.1.0):
    - node_modules\react-icons
  react-is@16.13.1:
    - node_modules\react-is
  react-is@17.0.2:
    - node_modules\pretty-format\node_modules\react-is
  react-markdown@10.1.0(@types/react@19.1.2)(react@19.1.0):
    - node_modules\react-markdown
  react-refresh@0.14.2:
    - node_modules\react-refresh
  react-remove-scroll-bar@2.3.8(@types/react@19.1.2)(react@19.1.0):
    - node_modules\react-remove-scroll-bar
  react-remove-scroll@2.6.3(@types/react@19.1.2)(react@19.1.0):
    - node_modules\react-remove-scroll
  react-router@7.1.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\react-router
  react-style-singleton@2.2.3(@types/react@19.1.2)(react@19.1.0):
    - node_modules\react-style-singleton
  react-universal-interface@0.6.2(react@19.1.0)(tslib@2.8.1):
    - node_modules\react-universal-interface
  react-use@17.6.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\react-use
  react@18.3.1:
    - node_modules\vitest-browser-react\node_modules\react
    - packages\ui-tars\sdk\node_modules\react
  react@19.1.0:
    - node_modules\react
  read-binary-file-arch@1.0.6:
    - node_modules\read-binary-file-arch
  read-pkg-up@2.0.0:
    - node_modules\read-pkg-up
  read-pkg@2.0.0:
    - node_modules\read-pkg-up\node_modules\read-pkg
  read-pkg@9.0.1:
    - node_modules\read-pkg
  read-yaml-file@1.1.0:
    - node_modules\read-yaml-file
  readable-stream@2.3.8:
    - node_modules\jszip\node_modules\readable-stream
  readable-stream@3.6.2:
    - node_modules\readable-stream
  readable-stream@4.7.0:
    - node_modules\readable-web-to-node-stream\node_modules\readable-stream
  readable-web-to-node-stream@3.0.3:
    - node_modules\readable-web-to-node-stream
  readdirp@4.1.2:
    - node_modules\readdirp
  rechoir@0.6.2:
    - node_modules\shelljs\node_modules\rechoir
  rechoir@0.8.0:
    - node_modules\rechoir
  reflect.getprototypeof@1.0.10:
    - node_modules\reflect.getprototypeof
  regenerator-runtime@0.13.11:
    - node_modules\regenerator-runtime
  regenerator-runtime@0.14.1:
    - node_modules\@babel\runtime\node_modules\regenerator-runtime
  regexp.prototype.flags@1.5.4:
    - node_modules\regexp.prototype.flags
  remark-parse@11.0.0:
    - node_modules\remark-parse
  remark-rehype@11.1.1:
    - node_modules\remark-rehype
  require-directory@2.1.1:
    - node_modules\require-directory
  require-from-string@2.0.2:
    - node_modules\require-from-string
  requires-port@1.0.0:
    - node_modules\requires-port
  resedit@2.0.3:
    - node_modules\resedit
  resize-observer-polyfill@1.5.1:
    - node_modules\resize-observer-polyfill
  resolve-alpn@1.2.1:
    - node_modules\resolve-alpn
  resolve-from@4.0.0:
    - node_modules\import-fresh\node_modules\resolve-from
  resolve-from@5.0.0:
    - node_modules\resolve-from
  resolve-pkg-maps@1.0.0:
    - node_modules\resolve-pkg-maps
  resolve@1.22.10:
    - node_modules\resolve
  resolve@2.0.0-next.5:
    - node_modules\eslint-plugin-react\node_modules\resolve
  responselike@2.0.1:
    - node_modules\responselike
  restore-cursor@3.1.0:
    - node_modules\cli-cursor\node_modules\restore-cursor
  restore-cursor@4.0.0:
    - node_modules\restore-cursor
  retry@0.12.0:
    - node_modules\promise-retry\node_modules\retry
  retry@0.13.1:
    - node_modules\retry
  reusify@1.0.4:
    - node_modules\reusify
  rfdc@1.4.1:
    - node_modules\rfdc
  rimraf@2.6.3:
    - node_modules\temp\node_modules\rimraf
  rimraf@2.7.1:
    - node_modules\electron-packager-languages\node_modules\rimraf
  rimraf@3.0.2:
    - node_modules\electron-devtools-installer\node_modules\rimraf
    - node_modules\flat-cache\node_modules\rimraf
    - node_modules\@npmcli\move-file\node_modules\rimraf
    - node_modules\cacache\node_modules\rimraf
  rimraf@4.1.0:
    - packages\agent-infra\search\bing-search\node_modules\rimraf
    - packages\agent-infra\search\browser-search\node_modules\rimraf
    - packages\agent-infra\search\duckduckgo-search\node_modules\rimraf
    - packages\agent-infra\search\search\node_modules\rimraf
  rimraf@6.0.1:
    - node_modules\rimraf
  roarr@2.15.4:
    - node_modules\roarr
  rollup@4.34.6:
    - node_modules\rollup
  router@2.2.0:
    - node_modules\router
  rrweb-cssom@0.8.0:
    - node_modules\rrweb-cssom
  rsbuild-plugin-dts@0.10.0(@rsbuild/core@1.4.0-beta.3)(typescript@5.7.3):
    - packages\common\electron-build\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\action-parser\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\cli\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\electron-ipc\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\operators\adb\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\operators\browser-operator\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\operators\browserbase\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\operators\nut-js\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\sdk\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\shared\node_modules\rsbuild-plugin-dts
    - packages\ui-tars\utio\node_modules\rsbuild-plugin-dts
  rsbuild-plugin-dts@0.5.3(@rsbuild/core@1.2.19)(typescript@5.7.3):
    - node_modules\rsbuild-plugin-dts
  rsbuild-plugin-dts@0.5.4(@rsbuild/core@1.2.19)(typescript@5.7.3):
    - packages\common\configs\node_modules\rsbuild-plugin-dts
  rtl-css-js@1.16.1:
    - node_modules\rtl-css-js
  run-async@2.4.1:
    - packages\ui-tars\operators\adb\node_modules\run-async
  run-async@3.0.0:
    - node_modules\run-async
  run-parallel@1.2.0:
    - node_modules\run-parallel
  rxjs@7.8.1:
    - node_modules\rxjs
  safe-array-concat@1.1.3:
    - node_modules\safe-array-concat
  safe-buffer@5.1.2:
    - node_modules\jszip\node_modules\safe-buffer
  safe-buffer@5.2.1:
    - node_modules\safe-buffer
  safe-push-apply@1.0.0:
    - node_modules\safe-push-apply
  safe-regex-test@1.1.0:
    - node_modules\safe-regex-test
  safer-buffer@2.1.2:
    - node_modules\safer-buffer
  sass-embedded-win32-x64@1.83.4:
    - node_modules\sass-embedded-win32-x64
  sass-embedded@1.83.4:
    - node_modules\sass-embedded
  sass@1.85.1:
    - node_modules\sass
  sax@1.4.1:
    - node_modules\sax
  saxes@6.0.0:
    - node_modules\saxes
  scheduler@0.23.2:
    - node_modules\vitest-browser-react\node_modules\scheduler
    - packages\ui-tars\sdk\node_modules\scheduler
  scheduler@0.26.0:
    - node_modules\scheduler
  screenfull@5.2.0:
    - node_modules\screenfull
  secp256k1@5.0.1:
    - node_modules\secp256k1
  secretlint@10.2.1:
    - node_modules\secretlint
  semver-compare@1.0.0:
    - node_modules\semver-compare
  semver@5.7.2:
    - node_modules\macos-version\node_modules\semver
    - node_modules\read-pkg-up\node_modules\semver
    - node_modules\execa\node_modules\semver
  semver@6.3.1:
    - node_modules\eslint-plugin-import\node_modules\semver
    - node_modules\eslint-plugin-react\node_modules\semver
    - node_modules\electron\node_modules\semver
    - node_modules\@babel\core\node_modules\semver
    - node_modules\@electron\get\node_modules\semver
    - node_modules\@babel\helper-compilation-targets\node_modules\semver
    - packages\ui-tars\electron-ipc\node_modules\semver
  semver@7.7.2:
    - node_modules\semver
  send@0.19.0:
    - node_modules\send
  send@1.2.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\send
    - node_modules\express-rate-limit\node_modules\send
    - packages\agent-infra\mcp-http-server\node_modules\send
  serialize-error@7.0.1:
    - node_modules\serialize-error
  serve-static@1.16.2:
    - node_modules\serve-static
  serve-static@2.2.0:
    - node_modules\@modelcontextprotocol\sdk\node_modules\serve-static
    - node_modules\express-rate-limit\node_modules\serve-static
    - packages\agent-infra\mcp-http-server\node_modules\serve-static
  set-cookie-parser@2.7.1:
    - node_modules\set-cookie-parser
  set-function-length@1.2.2:
    - node_modules\set-function-length
  set-function-name@2.0.2:
    - node_modules\set-function-name
  set-harmonic-interval@1.0.1:
    - node_modules\set-harmonic-interval
  set-proto@1.0.0:
    - node_modules\set-proto
  setimmediate@1.0.5:
    - node_modules\setimmediate
  setprototypeof@1.2.0:
    - node_modules\setprototypeof
  sharp@0.33.3:
    - node_modules\sharp
  shebang-command@1.2.0:
    - node_modules\execa\node_modules\shebang-command
  shebang-command@2.0.0:
    - node_modules\shebang-command
  shebang-regex@1.0.0:
    - node_modules\execa\node_modules\shebang-regex
  shebang-regex@3.0.0:
    - node_modules\shebang-regex
  shelljs@0.8.5:
    - node_modules\shelljs
  shellwords@0.1.1:
    - node_modules\shellwords
  shx@0.3.4:
    - node_modules\shx
  side-channel-list@1.0.0:
    - node_modules\side-channel-list
  side-channel-map@1.0.1:
    - node_modules\side-channel-map
  side-channel-weakmap@1.0.2:
    - node_modules\side-channel-weakmap
  side-channel@1.1.0:
    - node_modules\side-channel
  siginfo@2.0.0:
    - node_modules\siginfo
  signal-exit@3.0.7:
    - node_modules\signal-exit
  signal-exit@4.1.0:
    - node_modules\spawndamnit\node_modules\signal-exit
    - node_modules\foreground-child\node_modules\signal-exit
    - node_modules\@inquirer\core\node_modules\signal-exit
  simple-swizzle@0.2.2:
    - node_modules\simple-swizzle
  simple-wcswidth@1.0.1:
    - node_modules\simple-wcswidth
  simple-xml-to-json@1.2.3:
    - node_modules\simple-xml-to-json
  sirv@3.0.1:
    - node_modules\sirv
  sisteransi@1.0.5:
    - node_modules\sisteransi
  slash@3.0.0:
    - node_modules\slash
  slash@5.1.0:
    - node_modules\secretlint\node_modules\slash
  slice-ansi@4.0.0:
    - node_modules\table\node_modules\slice-ansi
  slice-ansi@5.0.0:
    - node_modules\slice-ansi
  smart-buffer@4.2.0:
    - node_modules\smart-buffer
  socks-proxy-agent@7.0.0:
    - node_modules\make-fetch-happen\node_modules\socks-proxy-agent
  socks-proxy-agent@8.0.5:
    - node_modules\socks-proxy-agent
  socks@2.8.4:
    - node_modules\socks
  sonner@2.0.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\sonner
  source-map-js@1.2.1:
    - node_modules\source-map-js
  source-map-support@0.5.21:
    - node_modules\source-map-support
  source-map@0.5.6:
    - node_modules\stacktrace-gps\node_modules\source-map
  source-map@0.5.7:
    - node_modules\@emotion\babel-plugin\node_modules\source-map
  source-map@0.6.1:
    - node_modules\source-map
  space-separated-tokens@2.0.2:
    - node_modules\space-separated-tokens
  spawndamnit@3.0.1:
    - node_modules\spawndamnit
  spdx-correct@3.2.0:
    - node_modules\spdx-correct
  spdx-exceptions@2.5.0:
    - node_modules\spdx-exceptions
  spdx-expression-parse@3.0.1:
    - node_modules\spdx-expression-parse
  spdx-license-ids@3.0.21:
    - node_modules\spdx-license-ids
  split2@4.2.0:
    - node_modules\split2
  sprintf-js@1.0.3:
    - node_modules\@changesets\parse\node_modules\sprintf-js
    - node_modules\read-yaml-file\node_modules\sprintf-js
    - node_modules\@textlint\linter-formatter\node_modules\sprintf-js
  sprintf-js@1.1.3:
    - node_modules\sprintf-js
  ssri@9.0.1:
    - node_modules\ssri
  stack-generator@2.0.10:
    - node_modules\stack-generator
  stackback@0.0.2:
    - node_modules\stackback
  stackframe@1.3.4:
    - node_modules\stackframe
  stacktrace-gps@3.1.2:
    - node_modules\stacktrace-gps
  stacktrace-js@2.0.2:
    - node_modules\stacktrace-js
  statuses@2.0.1:
    - node_modules\statuses
  std-env@3.8.0:
    - node_modules\std-env
  streamx@2.22.0:
    - node_modules\streamx
  strict-event-emitter@0.5.1:
    - node_modules\strict-event-emitter
  string-argv@0.3.2:
    - node_modules\string-argv
  string-width@4.2.3:
    - node_modules\string-width
    - node_modules\string-width-cjs
  string-width@5.1.2:
    - node_modules\wrap-ansi\node_modules\string-width
    - node_modules\cli-truncate\node_modules\string-width
    - node_modules\@isaacs\cliui\node_modules\string-width
  string-width@7.2.0:
    - node_modules\mcp-proxy\node_modules\string-width
  string.prototype.matchall@4.0.12:
    - node_modules\string.prototype.matchall
  string.prototype.repeat@1.0.0:
    - node_modules\string.prototype.repeat
  string.prototype.trim@1.2.10:
    - node_modules\string.prototype.trim
  string.prototype.trimend@1.0.9:
    - node_modules\string.prototype.trimend
  string.prototype.trimstart@1.0.8:
    - node_modules\string.prototype.trimstart
  string_decoder@1.1.1:
    - node_modules\jszip\node_modules\string_decoder
  string_decoder@1.3.0:
    - node_modules\string_decoder
  stringify-entities@4.0.4:
    - node_modules\stringify-entities
  strip-ansi@6.0.1:
    - node_modules\strip-ansi
    - node_modules\strip-ansi-cjs
  strip-ansi@7.1.0:
    - node_modules\@secretlint\formatter\node_modules\strip-ansi
    - node_modules\wrap-ansi\node_modules\strip-ansi
    - node_modules\log-update\node_modules\strip-ansi
    - node_modules\cli-truncate\node_modules\strip-ansi
    - node_modules\@isaacs\cliui\node_modules\strip-ansi
    - node_modules\mcp-proxy\node_modules\strip-ansi
  strip-bom@3.0.0:
    - node_modules\strip-bom
  strip-eof@1.0.0:
    - node_modules\strip-eof
  strip-final-newline@2.0.0:
    - node_modules\strip-final-newline
  strip-final-newline@3.0.0:
    - node_modules\lint-staged\node_modules\strip-final-newline
    - node_modules\opencommit\node_modules\strip-final-newline
  strip-json-comments@3.1.1:
    - node_modules\strip-json-comments
  strip-outer@1.0.1:
    - node_modules\strip-outer
  strnum@1.1.2:
    - node_modules\strnum
  strtok3@6.3.0:
    - node_modules\strtok3
  structured-source@4.0.0:
    - node_modules\structured-source
  stubborn-fs@1.2.5:
    - node_modules\stubborn-fs
  style-to-js@1.1.16:
    - node_modules\style-to-js
  style-to-object@1.0.8:
    - node_modules\style-to-object
  stylis@4.2.0:
    - node_modules\stylis
  stylis@4.3.6:
    - node_modules\nano-css\node_modules\stylis
  sudo-prompt@9.2.1:
    - node_modules\sudo-prompt
  sumchecker@3.0.1:
    - node_modules\sumchecker
  supergateway@3.4.0(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\supergateway
  supports-color@7.2.0:
    - node_modules\supports-color
  supports-color@8.1.1:
    - node_modules\sass-embedded\node_modules\supports-color
  supports-hyperlinks@3.2.0:
    - node_modules\supports-hyperlinks
  supports-preserve-symlinks-flag@1.0.0:
    - node_modules\supports-preserve-symlinks-flag
  swr@2.3.2(react@19.1.0):
    - node_modules\swr
  symbol-tree@3.2.4:
    - node_modules\symbol-tree
  sync-child-process@1.0.2:
    - node_modules\sync-child-process
  sync-message-port@1.1.3:
    - node_modules\sync-message-port
  synckit@0.9.2:
    - node_modules\synckit
  table@6.9.0:
    - node_modules\table
  tailwind-merge@3.1.0:
    - node_modules\tailwind-merge
  tailwindcss@4.1.3:
    - node_modules\tailwindcss
  tapable@2.2.1:
    - node_modules\tapable
  tar-fs@3.0.8:
    - node_modules\puppeteer-core\node_modules\tar-fs
  tar-fs@3.1.0:
    - node_modules\tar-fs
  tar-stream@3.1.7:
    - node_modules\tar-stream
  tar@6.2.1:
    - node_modules\tar
  temml@0.11.9:
    - node_modules\temml
  temp@0.9.4:
    - node_modules\temp
  term-size@2.2.1:
    - node_modules\term-size
  terminal-columns@1.4.1:
    - node_modules\terminal-columns
  terminal-link@4.0.0:
    - node_modules\terminal-link
  test-exclude@7.0.1:
    - node_modules\test-exclude
  text-decoder@1.2.3:
    - node_modules\text-decoder
  text-extensions@2.4.0:
    - node_modules\text-extensions
  text-table@0.2.0:
    - node_modules\text-table
  textextensions@6.11.0:
    - node_modules\textextensions
  throttle-debounce@3.0.1:
    - node_modules\throttle-debounce
  through@2.3.8:
    - node_modules\through
  timm@1.7.1:
    - node_modules\timm
  tiny-typed-emitter@2.1.0:
    - node_modules\tiny-typed-emitter
  tiny-warning@1.0.3:
    - node_modules\tiny-warning
  tinybench@2.9.0:
    - node_modules\tinybench
  tinycolor2@1.6.0:
    - node_modules\tinycolor2
  tinyexec@0.3.2:
    - node_modules\tinyexec
  tinyglobby@0.2.12:
    - node_modules\tinyglobby
  tinyglobby@0.2.14:
    - packages\common\configs\node_modules\rsbuild-plugin-dts\node_modules\tinyglobby
    - packages\common\electron-build\node_modules\tinyglobby
    - packages\ui-tars\action-parser\node_modules\tinyglobby
    - packages\ui-tars\cli\node_modules\tinyglobby
    - packages\ui-tars\electron-ipc\node_modules\tinyglobby
    - packages\ui-tars\operators\adb\node_modules\tinyglobby
    - packages\ui-tars\operators\browser-operator\node_modules\tinyglobby
    - packages\ui-tars\operators\browserbase\node_modules\tinyglobby
    - packages\ui-tars\operators\nut-js\node_modules\tinyglobby
    - packages\ui-tars\sdk\node_modules\tinyglobby
    - packages\ui-tars\shared\node_modules\tinyglobby
    - packages\ui-tars\utio\node_modules\tinyglobby
  tinypool@1.0.2:
    - node_modules\tinypool
  tinyrainbow@2.0.0:
    - node_modules\tinyrainbow
  tinyspy@3.0.2:
    - node_modules\tinyspy
  tldts-core@6.1.84:
    - node_modules\tldts-core
  tldts-core@7.0.6:
    - node_modules\tldts-experimental\node_modules\tldts-core
  tldts-experimental@7.0.6:
    - node_modules\tldts-experimental
  tldts@6.1.84:
    - node_modules\tldts
  tmp@0.0.33:
    - node_modules\tmp
  to-regex-range@5.0.1:
    - node_modules\to-regex-range
  toggle-selection@1.0.6:
    - node_modules\toggle-selection
  toidentifier@1.0.1:
    - node_modules\toidentifier
  token-types@4.2.1:
    - node_modules\token-types
  totalist@3.0.1:
    - node_modules\totalist
  tough-cookie@4.1.4:
    - node_modules\tough-cookie
  tough-cookie@5.1.2:
    - node_modules\jsdom\node_modules\tough-cookie
  tr46@0.0.3:
    - node_modules\node-fetch\node_modules\tr46
  tr46@5.1.0:
    - node_modules\tr46
  trim-lines@3.0.1:
    - node_modules\trim-lines
  trim-repeated@1.0.0:
    - node_modules\trim-repeated
  trough@2.2.0:
    - node_modules\trough
  ts-api-utils@1.4.3(typescript@5.7.3):
    - node_modules\ts-api-utils
  ts-easing@0.2.0:
    - node_modules\ts-easing
  ts-node@10.9.2(@types/node@20.17.17)(typescript@5.7.3):
    - node_modules\ts-node
  tsconfck@3.1.5(typescript@5.7.3):
    - node_modules\tsconfck
  tsconfig-paths@3.15.0:
    - node_modules\eslint-plugin-import\node_modules\tsconfig-paths
  tsconfig-paths@4.2.0:
    - node_modules\tsconfig-paths
  tslib@1.14.1:
    - node_modules\tsutils\node_modules\tslib
  tslib@2.8.1:
    - node_modules\tslib
  tsutils@3.21.0(typescript@5.7.3):
    - node_modules\tsutils
  tsx@4.19.2:
    - node_modules\tsx
  tsx@4.19.3:
    - packages\agent-infra\browser-use\node_modules\tsx
    - packages\agent-infra\mcp-benchmark\node_modules\tsx
    - packages\agent-infra\mcp-client\node_modules\tsx
    - packages\agent-infra\mcp-http-server\node_modules\tsx
    - packages\agent-infra\mcp-servers\browser\node_modules\tsx
    - packages\agent-infra\mcp-servers\commands\node_modules\tsx
    - packages\agent-infra\mcp-servers\filesystem\node_modules\tsx
    - packages\agent-infra\mcp-servers\search\node_modules\tsx
    - packages\agent-infra\mcp-shared\node_modules\tsx
    - packages\common\electron-build\node_modules\tsx
  tunnel@0.0.6:
    - node_modules\tunnel
  turbo-stream@2.4.0:
    - node_modules\turbo-stream
  turbo-windows-64@2.4.4:
    - node_modules\turbo-windows-64
  turbo@2.4.4:
    - node_modules\turbo
  turndown-plugin-gfm@1.0.2:
    - node_modules\turndown-plugin-gfm
  turndown@7.2.0:
    - node_modules\turndown
  tw-animate-css@1.2.5:
    - node_modules\tw-animate-css
  type-check@0.4.0:
    - node_modules\type-check
  type-fest@0.13.1:
    - node_modules\serialize-error\node_modules\type-fest
  type-fest@0.20.2:
    - node_modules\globals\node_modules\type-fest
  type-fest@0.21.3:
    - node_modules\ansi-escapes\node_modules\type-fest
  type-fest@1.4.0:
    - node_modules\log-update\node_modules\type-fest
  type-fest@4.34.1:
    - node_modules\type-fest
  type-fest@4.41.0:
    - node_modules\read-pkg\node_modules\parse-json\node_modules\type-fest
    - node_modules\msw\node_modules\type-fest
  type-flag@3.0.0:
    - node_modules\type-flag
  type-is@1.6.18:
    - node_modules\type-is
  type-is@2.0.1:
    - node_modules\@modelcontextprotocol\sdk\node_modules\type-is
    - node_modules\express-rate-limit\node_modules\type-is
    - packages\agent-infra\mcp-http-server\node_modules\type-is
  typed-array-buffer@1.0.3:
    - node_modules\typed-array-buffer
  typed-array-byte-length@1.0.3:
    - node_modules\typed-array-byte-length
  typed-array-byte-offset@1.0.4:
    - node_modules\typed-array-byte-offset
  typed-array-length@1.0.7:
    - node_modules\typed-array-length
  typed-query-selector@2.12.0:
    - node_modules\typed-query-selector
  typescript@4.9.4:
    - packages\agent-infra\logger\node_modules\typescript
    - packages\agent-infra\search\bing-search\node_modules\typescript
    - packages\agent-infra\search\browser-search\node_modules\typescript
    - packages\agent-infra\search\duckduckgo-search\node_modules\typescript
    - packages\agent-infra\search\search\node_modules\typescript
    - packages\agent-infra\shared\node_modules\typescript
  typescript@5.7.3:
    - node_modules\typescript
  uc.micro@2.1.0:
    - node_modules\uc.micro
  uint8array-extras@1.4.0:
    - node_modules\uint8array-extras
  unbox-primitive@1.1.0:
    - node_modules\unbox-primitive
  undici-types@5.26.5:
    - node_modules\undici-types
  undici-types@6.19.8:
    - node_modules\@types\node\node_modules\undici-types
  undici-types@6.20.0:
    - node_modules\@vitest\browser\node_modules\undici-types
    - node_modules\vitest-browser-react\node_modules\undici-types
    - packages\agent-infra\create-new-mcp\node_modules\undici-types
    - packages\agent-infra\mcp-benchmark\node_modules\undici-types
    - packages\agent-infra\mcp-http-server\node_modules\undici-types
    - packages\agent-infra\mcp-servers\browser\node_modules\undici-types
    - packages\agent-infra\mcp-servers\filesystem\node_modules\undici-types
    - packages\agent-infra\mcp-shared\node_modules\undici-types
    - packages\ui-tars\operators\browserbase\node_modules\undici-types
    - packages\ui-tars\sdk\node_modules\undici-types
  undici@5.28.5:
    - node_modules\undici
  unicorn-magic@0.1.0:
    - node_modules\unicorn-magic
  unicorn-magic@0.3.0:
    - node_modules\secretlint\node_modules\unicorn-magic
  unified@11.0.5:
    - node_modules\unified
  unique-filename@2.0.1:
    - node_modules\unique-filename
  unique-slug@3.0.0:
    - node_modules\unique-slug
  unist-util-is@6.0.0:
    - node_modules\unist-util-is
  unist-util-position@5.0.0:
    - node_modules\unist-util-position
  unist-util-stringify-position@4.0.0:
    - node_modules\unist-util-stringify-position
  unist-util-visit-parents@6.0.1:
    - node_modules\unist-util-visit-parents
  unist-util-visit@5.0.0:
    - node_modules\unist-util-visit
  universal-user-agent@6.0.1:
    - node_modules\universal-user-agent
  universalify@0.1.2:
    - node_modules\@changesets\cli\node_modules\universalify
    - node_modules\@changesets\pre\node_modules\universalify
    - node_modules\@changesets\read\node_modules\universalify
    - node_modules\@changesets\config\node_modules\universalify
    - node_modules\@changesets\write\node_modules\universalify
    - node_modules\@manypkg\get-packages\node_modules\universalify
    - node_modules\@changesets\apply-release-plan\node_modules\universalify
    - node_modules\electron\node_modules\universalify
    - node_modules\@manypkg\find-root\node_modules\universalify
    - node_modules\@electron\get\node_modules\universalify
    - node_modules\electron-winstaller\node_modules\universalify
    - packages\ui-tars\electron-ipc\node_modules\universalify
  universalify@0.2.0:
    - node_modules\tough-cookie\node_modules\universalify
  universalify@2.0.1:
    - node_modules\universalify
  unpipe@1.0.0:
    - node_modules\unpipe
  unzip-crx-3@0.2.0:
    - node_modules\unzip-crx-3
  update-browserslist-db@1.1.2(browserslist@4.24.4):
    - node_modules\update-browserslist-db
  uri-js@4.4.1:
    - node_modules\uri-js
  url-parse@1.5.10:
    - node_modules\url-parse
  use-callback-ref@1.3.3(@types/react@19.1.2)(react@19.1.0):
    - node_modules\use-callback-ref
  use-sidecar@1.1.3(@types/react@19.1.2)(react@19.1.0):
    - node_modules\use-sidecar
  use-sync-external-store@1.4.0(react@19.1.0):
    - node_modules\swr\node_modules\use-sync-external-store
  use-sync-external-store@1.5.0(react@19.1.0):
    - node_modules\use-sync-external-store
  username@5.1.0:
    - node_modules\username
  utf-8-validate@6.0.5:
    - node_modules\utf-8-validate
  utif2@4.1.0:
    - node_modules\utif2
  util-deprecate@1.0.2:
    - node_modules\util-deprecate
  utils-merge@1.0.1:
    - node_modules\utils-merge
  uuid@10.0.0:
    - node_modules\@langchain\core\node_modules\uuid
    - node_modules\langsmith\node_modules\uuid
  uuid@11.1.0:
    - node_modules\supergateway\node_modules\uuid
    - apps\ui-tars\src\renderer\node_modules\uuid
    - packages\agent-infra\mcp-client\node_modules\uuid
  uuid@8.3.2:
    - node_modules\node-notifier\node_modules\uuid
  uuid@9.0.1:
    - node_modules\uuid
  v8-compile-cache-lib@3.0.1:
    - node_modules\v8-compile-cache-lib
  validate-npm-package-license@3.0.4:
    - node_modules\validate-npm-package-license
  varint@6.0.0:
    - node_modules\varint
  vary@1.1.2:
    - node_modules\vary
  version-range@4.14.0:
    - node_modules\version-range
  vfile-message@4.0.2:
    - node_modules\vfile-message
  vfile@6.0.3:
    - node_modules\vfile
  vite-node@3.0.5(@types/node@22.13.10)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0):
    - node_modules\@vitest\browser\node_modules\vite-node
    - node_modules\vitest-browser-react\node_modules\vite-node
    - packages\ui-tars\operators\browserbase\node_modules\vite-node
    - packages\ui-tars\sdk\node_modules\vite-node
  vite-node@3.0.7(@types/node@20.14.8)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0):
    - packages\agent-infra\browser\node_modules\vite-node
    - packages\agent-infra\browser-use\node_modules\vite-node
    - packages\agent-infra\mcp-client\node_modules\vite-node
    - packages\agent-infra\mcp-servers\browser\node_modules\vite-node
    - packages\agent-infra\mcp-servers\commands\node_modules\vite-node
    - packages\agent-infra\mcp-servers\filesystem\node_modules\vite-node
    - packages\agent-infra\search\bing-search\node_modules\vite-node
    - packages\agent-infra\search\browser-search\node_modules\vite-node
    - packages\agent-infra\search\duckduckgo-search\node_modules\vite-node
    - packages\agent-infra\search\search\node_modules\vite-node
    - packages\agent-infra\shared\node_modules\vite-node
  vite-node@3.0.8(@types/node@20.17.17)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0):
    - node_modules\vite-node
  vite-tsconfig-paths@5.1.4(typescript@5.7.3)(vite@6.2.2(@types/node@20.17.17)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0)):
    - node_modules\vite-tsconfig-paths
  vite@6.2.2(@types/node@20.17.17)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0):
    - node_modules\vite
  vitest-browser-react@0.1.1(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(@vitest/browser@3.0.5(@types/node@22.13.10)(bufferutil@4.0.9)(playwright@1.50.1)(typescript@5.7.3)(utf-8-validate@6.0.5)(vite@6.2.2(@types/node@22.13.10)(jiti@2.4.2)(lightningcss@1.29.2)(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0))(vitest@3.0.5))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(vitest@3.0.5(@types/debug@4.1.12)(@types/node@22.13.10)(@vitest/browser@3.0.5)(happy-dom@17.1.1)(jiti@2.4.2)(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5))(lightningcss@1.29.2)(msw@2.7.0(@types/node@22.13.10)(typescript@5.7.3))(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0)):
    - node_modules\vitest-browser-react
  vitest@3.0.5(@types/debug@4.1.12)(@types/node@22.13.10)(@vitest/browser@3.0.5)(happy-dom@17.1.1)(jiti@2.4.2)(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5))(lightningcss@1.29.2)(msw@2.7.0(@types/node@22.13.10)(typescript@5.7.3))(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0):
    - node_modules\@vitest\browser\node_modules\vitest
    - node_modules\vitest-browser-react\node_modules\vitest
    - packages\ui-tars\operators\browserbase\node_modules\vitest
    - packages\ui-tars\sdk\node_modules\vitest
  vitest@3.0.7(@types/debug@4.1.12)(@types/node@20.14.8)(happy-dom@17.1.1)(jiti@2.4.2)(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5))(lightningcss@1.29.2)(msw@2.7.0(@types/node@20.14.8)(typescript@5.7.3))(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.3)(yaml@2.7.0):
    - packages\agent-infra\browser\node_modules\vitest
    - packages\agent-infra\browser-use\node_modules\vitest
    - packages\agent-infra\mcp-client\node_modules\vitest
    - packages\agent-infra\mcp-servers\browser\node_modules\vitest
    - packages\agent-infra\mcp-servers\commands\node_modules\vitest
    - packages\agent-infra\mcp-servers\filesystem\node_modules\vitest
    - packages\agent-infra\search\bing-search\node_modules\vitest
    - packages\agent-infra\search\browser-search\node_modules\vitest
    - packages\agent-infra\search\duckduckgo-search\node_modules\vitest
    - packages\agent-infra\search\search\node_modules\vitest
    - packages\agent-infra\shared\node_modules\vitest
  vitest@3.0.8(@types/debug@4.1.12)(@types/node@20.17.17)(happy-dom@17.1.1)(jiti@2.4.2)(jsdom@26.1.0(bufferutil@4.0.9)(utf-8-validate@6.0.5))(lightningcss@1.29.2)(msw@2.7.0(@types/node@20.17.17)(typescript@5.7.3))(sass-embedded@1.83.4)(sass@1.85.1)(tsx@4.19.2)(yaml@2.7.0):
    - node_modules\vitest
  w3c-xmlserializer@5.0.0:
    - node_modules\w3c-xmlserializer
  wcwidth@1.0.1:
    - node_modules\wcwidth
  web-streams-polyfill@3.3.3:
    - node_modules\@anthropic-ai\sdk\node_modules\web-streams-polyfill
  web-streams-polyfill@4.0.0-beta.3:
    - node_modules\web-streams-polyfill
  webidl-conversions@3.0.1:
    - node_modules\node-fetch\node_modules\webidl-conversions
  webidl-conversions@7.0.0:
    - node_modules\webidl-conversions
  whatwg-encoding@3.1.1:
    - node_modules\whatwg-encoding
  whatwg-fetch@3.6.20:
    - node_modules\whatwg-fetch
  whatwg-mimetype@3.0.0:
    - node_modules\happy-dom\node_modules\whatwg-mimetype
  whatwg-mimetype@4.0.0:
    - node_modules\whatwg-mimetype
  whatwg-url@14.2.0:
    - node_modules\whatwg-url
  whatwg-url@5.0.0:
    - node_modules\node-fetch\node_modules\whatwg-url
  when-exit@2.1.4:
    - node_modules\when-exit
  which-boxed-primitive@1.1.1:
    - node_modules\which-boxed-primitive
  which-builtin-type@1.2.1:
    - node_modules\which-builtin-type
  which-collection@1.0.2:
    - node_modules\which-collection
  which-typed-array@1.1.18:
    - node_modules\which-typed-array
  which@1.3.1:
    - node_modules\execa\node_modules\which
  which@2.0.2:
    - node_modules\which
  which@5.0.0:
    - packages\agent-infra\browser\node_modules\which
  why-is-node-running@2.3.0:
    - node_modules\why-is-node-running
  word-wrap@1.2.5:
    - node_modules\word-wrap
  wrap-ansi@6.2.0:
    - node_modules\inquirer\node_modules\wrap-ansi
    - node_modules\@inquirer\core\node_modules\wrap-ansi
  wrap-ansi@7.0.0:
    - node_modules\cliui\node_modules\wrap-ansi
    - node_modules\wrap-ansi-cjs
    - packages\ui-tars\operators\adb\node_modules\wrap-ansi
  wrap-ansi@8.1.0:
    - node_modules\wrap-ansi
  wrap-ansi@9.0.0:
    - node_modules\mcp-proxy\node_modules\wrap-ansi
  wrappy@1.0.2:
    - node_modules\wrappy
  ws@7.5.10(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\express-ws\node_modules\ws
  ws@8.18.1(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\puppeteer-core\node_modules\ws
    - node_modules\@browserbasehq\stagehand\node_modules\ws
  ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    - node_modules\ws
    - node_modules\@browserbasehq\stagehand\node_modules\openai\node_modules\ws
  xhr@2.6.0:
    - node_modules\xhr
  xml-name-validator@5.0.0:
    - node_modules\xml-name-validator
  xml-parse-from-string@1.0.1:
    - node_modules\xml-parse-from-string
  xml2js@0.5.0:
    - node_modules\xml2js
  xmlbuilder@11.0.1:
    - node_modules\xmlbuilder
  xmlbuilder@15.1.1:
    - node_modules\plist\node_modules\xmlbuilder
  xmlchars@2.2.0:
    - node_modules\xmlchars
  xtend@4.0.2:
    - node_modules\xtend
  xterm-addon-fit@0.5.0(xterm@4.19.0):
    - node_modules\xterm-addon-fit
  xterm-addon-search@0.8.2(xterm@4.19.0):
    - node_modules\xterm-addon-search
  xterm@4.19.0:
    - node_modules\xterm
  y18n@5.0.8:
    - node_modules\y18n
  yaku@0.16.7:
    - node_modules\yaku
  yallist@3.1.1:
    - node_modules\@babel\helper-compilation-targets\node_modules\yallist
  yallist@4.0.0:
    - node_modules\yallist
  yaml@1.10.2:
    - node_modules\babel-plugin-macros\node_modules\yaml
  yaml@2.3.1:
    - node_modules\lint-staged\node_modules\yaml
  yaml@2.7.0:
    - node_modules\yaml
  yargs-parser@21.1.1:
    - node_modules\yargs-parser
  yargs-parser@22.0.0:
    - node_modules\mcp-proxy\node_modules\yargs-parser
  yargs@17.7.2:
    - node_modules\yargs
  yargs@18.0.0:
    - node_modules\mcp-proxy\node_modules\yargs
  yauzl@2.10.0:
    - node_modules\yauzl
  yn@3.1.1:
    - node_modules\yn
  yocto-queue@0.1.0:
    - node_modules\find-up\node_modules\yocto-queue
    - node_modules\dir-compare\node_modules\yocto-queue
  yocto-queue@1.1.1:
    - node_modules\yocto-queue
  yoctocolors-cjs@2.1.2:
    - node_modules\yoctocolors-cjs
  zod-to-json-schema@3.24.1(zod@3.24.2):
    - node_modules\@mistralai\mistralai\node_modules\zod-to-json-schema
    - packages\agent-infra\mcp-servers\browser\node_modules\zod-to-json-schema
    - packages\agent-infra\mcp-servers\filesystem\node_modules\zod-to-json-schema
  zod-to-json-schema@3.24.3(zod@3.24.2):
    - node_modules\zod-to-json-schema
  zod@3.24.2:
    - node_modules\zod
  zod@3.25.76:
    - node_modules\supergateway\node_modules\zod
    - node_modules\@agent-infra\browser-context\node_modules\zod
    - node_modules\@browserbasehq\stagehand\node_modules\zod
    - node_modules\chromium-bidi\node_modules\zod
    - packages\ui-tars\sdk\node_modules\zod
  zustand@5.0.3(@types/react@19.1.2)(react@19.1.0)(use-sync-external-store@1.5.0(react@19.1.0)):
    - node_modules\zustand
  zwitch@2.0.4:
    - node_modules\zwitch
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: hoisted
packageManager: pnpm@9.10.0
pendingBuilds: []
prunedAt: Sun, 17 Aug 2025 17:01:35 GMT
publicHoistPattern:
  - '*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@ast-grep/napi-darwin-arm64@0.35.0'
  - '@ast-grep/napi-darwin-arm64@0.36.0'
  - '@ast-grep/napi-darwin-arm64@0.37.0'
  - '@ast-grep/napi-darwin-x64@0.35.0'
  - '@ast-grep/napi-darwin-x64@0.36.0'
  - '@ast-grep/napi-darwin-x64@0.37.0'
  - '@ast-grep/napi-linux-arm64-gnu@0.35.0'
  - '@ast-grep/napi-linux-arm64-gnu@0.36.0'
  - '@ast-grep/napi-linux-arm64-gnu@0.37.0'
  - '@ast-grep/napi-linux-arm64-musl@0.35.0'
  - '@ast-grep/napi-linux-arm64-musl@0.36.0'
  - '@ast-grep/napi-linux-arm64-musl@0.37.0'
  - '@ast-grep/napi-linux-x64-gnu@0.35.0'
  - '@ast-grep/napi-linux-x64-gnu@0.36.0'
  - '@ast-grep/napi-linux-x64-gnu@0.37.0'
  - '@ast-grep/napi-linux-x64-musl@0.35.0'
  - '@ast-grep/napi-linux-x64-musl@0.36.0'
  - '@ast-grep/napi-linux-x64-musl@0.37.0'
  - '@ast-grep/napi-win32-arm64-msvc@0.35.0'
  - '@ast-grep/napi-win32-arm64-msvc@0.36.0'
  - '@ast-grep/napi-win32-arm64-msvc@0.37.0'
  - '@ast-grep/napi-win32-ia32-msvc@0.35.0'
  - '@ast-grep/napi-win32-ia32-msvc@0.36.0'
  - '@ast-grep/napi-win32-ia32-msvc@0.37.0'
  - '@computer-use/node-mac-permissions@2.2.1'
  - '@emnapi/runtime@1.3.1'
  - '@esbuild/aix-ppc64@0.23.1'
  - '@esbuild/aix-ppc64@0.24.2'
  - '@esbuild/aix-ppc64@0.25.0'
  - '@esbuild/android-arm64@0.23.1'
  - '@esbuild/android-arm64@0.24.2'
  - '@esbuild/android-arm64@0.25.0'
  - '@esbuild/android-arm@0.23.1'
  - '@esbuild/android-arm@0.24.2'
  - '@esbuild/android-arm@0.25.0'
  - '@esbuild/android-x64@0.23.1'
  - '@esbuild/android-x64@0.24.2'
  - '@esbuild/android-x64@0.25.0'
  - '@esbuild/darwin-arm64@0.23.1'
  - '@esbuild/darwin-arm64@0.24.2'
  - '@esbuild/darwin-arm64@0.25.0'
  - '@esbuild/darwin-x64@0.23.1'
  - '@esbuild/darwin-x64@0.24.2'
  - '@esbuild/darwin-x64@0.25.0'
  - '@esbuild/freebsd-arm64@0.23.1'
  - '@esbuild/freebsd-arm64@0.24.2'
  - '@esbuild/freebsd-arm64@0.25.0'
  - '@esbuild/freebsd-x64@0.23.1'
  - '@esbuild/freebsd-x64@0.24.2'
  - '@esbuild/freebsd-x64@0.25.0'
  - '@esbuild/linux-arm64@0.23.1'
  - '@esbuild/linux-arm64@0.24.2'
  - '@esbuild/linux-arm64@0.25.0'
  - '@esbuild/linux-arm@0.23.1'
  - '@esbuild/linux-arm@0.24.2'
  - '@esbuild/linux-arm@0.25.0'
  - '@esbuild/linux-ia32@0.23.1'
  - '@esbuild/linux-ia32@0.24.2'
  - '@esbuild/linux-ia32@0.25.0'
  - '@esbuild/linux-loong64@0.23.1'
  - '@esbuild/linux-loong64@0.24.2'
  - '@esbuild/linux-loong64@0.25.0'
  - '@esbuild/linux-mips64el@0.23.1'
  - '@esbuild/linux-mips64el@0.24.2'
  - '@esbuild/linux-mips64el@0.25.0'
  - '@esbuild/linux-ppc64@0.23.1'
  - '@esbuild/linux-ppc64@0.24.2'
  - '@esbuild/linux-ppc64@0.25.0'
  - '@esbuild/linux-riscv64@0.23.1'
  - '@esbuild/linux-riscv64@0.24.2'
  - '@esbuild/linux-riscv64@0.25.0'
  - '@esbuild/linux-s390x@0.23.1'
  - '@esbuild/linux-s390x@0.24.2'
  - '@esbuild/linux-s390x@0.25.0'
  - '@esbuild/linux-x64@0.23.1'
  - '@esbuild/linux-x64@0.24.2'
  - '@esbuild/linux-x64@0.25.0'
  - '@esbuild/netbsd-arm64@0.24.2'
  - '@esbuild/netbsd-arm64@0.25.0'
  - '@esbuild/netbsd-x64@0.23.1'
  - '@esbuild/netbsd-x64@0.24.2'
  - '@esbuild/netbsd-x64@0.25.0'
  - '@esbuild/openbsd-arm64@0.23.1'
  - '@esbuild/openbsd-arm64@0.24.2'
  - '@esbuild/openbsd-arm64@0.25.0'
  - '@esbuild/openbsd-x64@0.23.1'
  - '@esbuild/openbsd-x64@0.24.2'
  - '@esbuild/openbsd-x64@0.25.0'
  - '@esbuild/sunos-x64@0.23.1'
  - '@esbuild/sunos-x64@0.24.2'
  - '@esbuild/sunos-x64@0.25.0'
  - '@esbuild/win32-arm64@0.23.1'
  - '@esbuild/win32-arm64@0.24.2'
  - '@esbuild/win32-arm64@0.25.0'
  - '@esbuild/win32-ia32@0.23.1'
  - '@esbuild/win32-ia32@0.24.2'
  - '@esbuild/win32-ia32@0.25.0'
  - '@img/sharp-darwin-arm64@0.33.3'
  - '@img/sharp-darwin-x64@0.33.3'
  - '@img/sharp-libvips-darwin-arm64@1.0.2'
  - '@img/sharp-libvips-darwin-x64@1.0.2'
  - '@img/sharp-libvips-linux-arm64@1.0.2'
  - '@img/sharp-libvips-linux-arm@1.0.2'
  - '@img/sharp-libvips-linux-s390x@1.0.2'
  - '@img/sharp-libvips-linux-x64@1.0.2'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.2'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.2'
  - '@img/sharp-linux-arm64@0.33.3'
  - '@img/sharp-linux-arm@0.33.3'
  - '@img/sharp-linux-s390x@0.33.3'
  - '@img/sharp-linux-x64@0.33.3'
  - '@img/sharp-linuxmusl-arm64@0.33.3'
  - '@img/sharp-linuxmusl-x64@0.33.3'
  - '@img/sharp-wasm32@0.33.3'
  - '@img/sharp-win32-ia32@0.33.3'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.34.6'
  - '@rollup/rollup-android-arm64@4.34.6'
  - '@rollup/rollup-darwin-arm64@4.34.6'
  - '@rollup/rollup-darwin-x64@4.34.6'
  - '@rollup/rollup-freebsd-arm64@4.34.6'
  - '@rollup/rollup-freebsd-x64@4.34.6'
  - '@rollup/rollup-linux-arm-gnueabihf@4.34.6'
  - '@rollup/rollup-linux-arm-musleabihf@4.34.6'
  - '@rollup/rollup-linux-arm64-gnu@4.34.6'
  - '@rollup/rollup-linux-arm64-musl@4.34.6'
  - '@rollup/rollup-linux-loongarch64-gnu@4.34.6'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.34.6'
  - '@rollup/rollup-linux-riscv64-gnu@4.34.6'
  - '@rollup/rollup-linux-s390x-gnu@4.34.6'
  - '@rollup/rollup-linux-x64-gnu@4.34.6'
  - '@rollup/rollup-linux-x64-musl@4.34.6'
  - '@rollup/rollup-win32-arm64-msvc@4.34.6'
  - '@rollup/rollup-win32-ia32-msvc@4.34.6'
  - '@rspack/binding-darwin-arm64@1.2.8'
  - '@rspack/binding-darwin-arm64@1.4.0-beta.0'
  - '@rspack/binding-darwin-x64@1.2.8'
  - '@rspack/binding-darwin-x64@1.4.0-beta.0'
  - '@rspack/binding-linux-arm64-gnu@1.2.8'
  - '@rspack/binding-linux-arm64-gnu@1.4.0-beta.0'
  - '@rspack/binding-linux-arm64-musl@1.2.8'
  - '@rspack/binding-linux-arm64-musl@1.4.0-beta.0'
  - '@rspack/binding-linux-x64-gnu@1.2.8'
  - '@rspack/binding-linux-x64-gnu@1.4.0-beta.0'
  - '@rspack/binding-linux-x64-musl@1.2.8'
  - '@rspack/binding-linux-x64-musl@1.4.0-beta.0'
  - '@rspack/binding-win32-arm64-msvc@1.2.8'
  - '@rspack/binding-win32-arm64-msvc@1.4.0-beta.0'
  - '@rspack/binding-win32-ia32-msvc@1.2.8'
  - '@rspack/binding-win32-ia32-msvc@1.4.0-beta.0'
  - '@tailwindcss/oxide-android-arm64@4.1.3'
  - '@tailwindcss/oxide-darwin-arm64@4.1.3'
  - '@tailwindcss/oxide-darwin-x64@4.1.3'
  - '@tailwindcss/oxide-freebsd-x64@4.1.3'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.3'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.3'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.3'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.3'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.3'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.3'
  - appdmg@0.6.6
  - async@1.5.2
  - base32-encode@1.2.0
  - bplist-creator@0.0.8
  - color-convert@0.5.3
  - ds-store@0.1.6
  - encode-utf8@1.0.3
  - fmix@0.1.0
  - fs-temp@1.2.1
  - fs-xattr@0.3.1
  - fsevents@2.3.2
  - fsevents@2.3.3
  - generate-function@2.3.1
  - generate-object-property@1.2.0
  - image-size@0.7.5
  - imul@1.0.1
  - is-my-ip-valid@1.0.1
  - is-my-json-valid@2.20.6
  - is-property@1.0.2
  - jsonpointer@5.0.1
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-win32-arm64-msvc@1.29.2
  - macos-alias@0.2.12
  - murmur-32@0.2.0
  - nan@2.22.0
  - parse-color@1.0.0
  - random-path@0.1.2
  - repeat-string@1.6.1
  - sass-embedded-android-arm64@1.83.4
  - sass-embedded-android-arm@1.83.4
  - sass-embedded-android-ia32@1.83.4
  - sass-embedded-android-riscv64@1.83.4
  - sass-embedded-android-x64@1.83.4
  - sass-embedded-darwin-arm64@1.83.4
  - sass-embedded-darwin-x64@1.83.4
  - sass-embedded-linux-arm64@1.83.4
  - sass-embedded-linux-arm@1.83.4
  - sass-embedded-linux-ia32@1.83.4
  - sass-embedded-linux-musl-arm64@1.83.4
  - sass-embedded-linux-musl-arm@1.83.4
  - sass-embedded-linux-musl-ia32@1.83.4
  - sass-embedded-linux-musl-riscv64@1.83.4
  - sass-embedded-linux-musl-x64@1.83.4
  - sass-embedded-linux-riscv64@1.83.4
  - sass-embedded-linux-x64@1.83.4
  - sass-embedded-win32-arm64@1.83.4
  - sass-embedded-win32-ia32@1.83.4
  - stream-buffers@2.2.0
  - tn1150@0.1.0
  - to-data-view@1.1.0
  - turbo-darwin-64@2.4.4
  - turbo-darwin-arm64@2.4.4
  - turbo-linux-64@2.4.4
  - turbo-linux-arm64@2.4.4
  - turbo-windows-arm64@2.4.4
  - unorm@1.6.0
storeDir: D:\.pnpm-store\v3
virtualStoreDir: D:\PRJ\open-I\UI-TARS-desktop\node_modules\.pnpm
virtualStoreDirMaxLength: 120
