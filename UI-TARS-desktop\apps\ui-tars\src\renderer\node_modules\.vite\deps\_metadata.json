{"hash": "a9a77702", "configHash": "e8b0a197", "lockfileHash": "cdcd4eb3", "browserHash": "d456a055", "optimized": {"react": {"src": "../../../../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "7a821cdb", "needsInterop": true}, "react-dom": {"src": "../../../../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "439ff1a0", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "383cb7a2", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "1fc7b3e6", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../../../../../../node_modules/@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "817dfa95", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../../../../../../node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "574a70fa", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../../../../../../node_modules/@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "a8cac0a0", "needsInterop": false}, "@radix-ui/react-collapsible": {"src": "../../../../../../../node_modules/@radix-ui/react-collapsible/dist/index.mjs", "file": "@radix-ui_react-collapsible.js", "fileHash": "f48bf348", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../../../../../../node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "d66e5d18", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "2c99e72b", "needsInterop": false}, "@radix-ui/react-hover-card": {"src": "../../../../../../../node_modules/@radix-ui/react-hover-card/dist/index.mjs", "file": "@radix-ui_react-hover-card.js", "fileHash": "459eec30", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../../../../../../node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "02c3e8b5", "needsInterop": false}, "@radix-ui/react-scroll-area": {"src": "../../../../../../../node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "file": "@radix-ui_react-scroll-area.js", "fileHash": "47693fcc", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "7d8d5e38", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../../../../../../node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "255e891f", "needsInterop": false}, "@radix-ui/react-slider": {"src": "../../../../../../../node_modules/@radix-ui/react-slider/dist/index.mjs", "file": "@radix-ui_react-slider.js", "fileHash": "b8d3043f", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "d86d6eb4", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../../../../../../node_modules/@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "92409ed6", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "4d61da89", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../../../../../../node_modules/@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "ebd43c0c", "needsInterop": false}, "async-retry": {"src": "../../../../../../../node_modules/async-retry/lib/index.js", "file": "async-retry.js", "fileHash": "cc9c19d3", "needsInterop": true}, "class-variance-authority": {"src": "../../../../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "fdbc6d3f", "needsInterop": false}, "clsx": {"src": "../../../../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "57d99202", "needsInterop": false}, "dayjs": {"src": "../../../../../../../node_modules/dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "91354999", "needsInterop": true}, "framer-motion": {"src": "../../../../../../../node_modules/framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "047b557c", "needsInterop": false}, "idb-keyval": {"src": "../../../../../../../node_modules/idb-keyval/dist/index.js", "file": "idb-keyval.js", "fileHash": "42c0b7a7", "needsInterop": false}, "jimp": {"src": "../../../../../../../node_modules/jimp/dist/browser/index.js", "file": "jimp.js", "fileHash": "4eb1ce7b", "needsInterop": false}, "lodash-es": {"src": "../../../../../../../node_modules/lodash-es/lodash.js", "file": "lodash-es.js", "fileHash": "b11ef328", "needsInterop": false}, "lodash.isnumber": {"src": "../../../../../../../node_modules/lodash.isnumber/index.js", "file": "lodash__isnumber.js", "fileHash": "0d3a99e3", "needsInterop": true}, "lucide-react": {"src": "../../../../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1ba3c5ef", "needsInterop": false}, "medium-zoom": {"src": "../../../../../../../node_modules/medium-zoom/dist/medium-zoom.esm.js", "file": "medium-zoom.js", "fileHash": "e0df7f31", "needsInterop": false}, "ms": {"src": "../../../../../../../node_modules/ms/index.js", "file": "ms.js", "fileHash": "b4836c9b", "needsInterop": true}, "openai": {"src": "../../../../../../../packages/ui-tars/sdk/node_modules/openai/index.mjs", "file": "openai.js", "fileHash": "3d3eb6b1", "needsInterop": false}, "react-countup": {"src": "../../../../../../../node_modules/react-countup/build/index.js", "file": "react-countup.js", "fileHash": "a4a97c45", "needsInterop": true}, "react-dom/client": {"src": "../../../../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6d216877", "needsInterop": true}, "react-hook-form": {"src": "../../../../../../../node_modules/react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "1b76cb2b", "needsInterop": false}, "react-markdown": {"src": "../../../../../../../node_modules/react-markdown/index.js", "file": "react-markdown.js", "fileHash": "21574001", "needsInterop": false}, "react-router": {"src": "../../../../../../../node_modules/react-router/dist/development/index.mjs", "file": "react-router.js", "fileHash": "a26135da", "needsInterop": false}, "sonner": {"src": "../../../../../../../node_modules/sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "7c9c4fb5", "needsInterop": false}, "swr": {"src": "../../../../../../../node_modules/swr/dist/index/index.mjs", "file": "swr.js", "fileHash": "3971ca01", "needsInterop": false}, "tailwind-merge": {"src": "../../../../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "6ac89e80", "needsInterop": false}, "uuid": {"src": "../../../../../../../node_modules/uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "5b1373c5", "needsInterop": false}, "zod": {"src": "../../../../../../../node_modules/zod/lib/index.mjs", "file": "zod.js", "fileHash": "db1f457c", "needsInterop": false}, "zustand": {"src": "../../../../../../../node_modules/zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "e72a2b03", "needsInterop": false}, "zustand/vanilla": {"src": "../../../../../../../node_modules/zustand/esm/vanilla.mjs", "file": "zustand_vanilla.js", "fileHash": "a7eb479f", "needsInterop": false}}, "chunks": {"chunk-WBESN2JC": {"file": "chunk-WBESN2JC.js"}, "chunk-RN3YDZDU": {"file": "chunk-RN3YDZDU.js"}, "chunk-5WWLHQIB": {"file": "chunk-5WWLHQIB.js"}, "chunk-3CRP4VJ5": {"file": "chunk-3CRP4VJ5.js"}, "chunk-TADUHA4Y": {"file": "chunk-TADUHA4Y.js"}, "chunk-6CLDPCPC": {"file": "chunk-6CLDPCPC.js"}, "chunk-ENMKCMUE": {"file": "chunk-ENMKCMUE.js"}, "chunk-DM53QVP3": {"file": "chunk-DM53QVP3.js"}, "chunk-6W7PPX7S": {"file": "chunk-6W7PPX7S.js"}, "chunk-BYBZV7ZF": {"file": "chunk-BYBZV7ZF.js"}, "chunk-XQASN7G3": {"file": "chunk-XQASN7G3.js"}, "chunk-AHTUJLFV": {"file": "chunk-AHTUJLFV.js"}, "chunk-3URYQRPV": {"file": "chunk-3URYQRPV.js"}, "chunk-AY3PMZF2": {"file": "chunk-AY3PMZF2.js"}, "chunk-EDAMMDAC": {"file": "chunk-EDAMMDAC.js"}, "chunk-ODCOWAB4": {"file": "chunk-ODCOWAB4.js"}, "chunk-TCM645KY": {"file": "chunk-TCM645KY.js"}, "chunk-7RMKQOBW": {"file": "chunk-7RMKQOBW.js"}, "chunk-AXEJUI5U": {"file": "chunk-AXEJUI5U.js"}, "chunk-VYSTHKXS": {"file": "chunk-VYSTHKXS.js"}, "chunk-IYY5MMZB": {"file": "chunk-IYY5MMZB.js"}, "chunk-SBIWQEZ5": {"file": "chunk-SBIWQEZ5.js"}, "chunk-AOQLE6VY": {"file": "chunk-AOQLE6VY.js"}, "chunk-LQVNYZOU": {"file": "chunk-LQVNYZOU.js"}, "chunk-VVFCWJBN": {"file": "chunk-VVFCWJBN.js"}, "chunk-Q4C2C7KI": {"file": "chunk-Q4C2C7KI.js"}, "chunk-2NMYR4XR": {"file": "chunk-2NMYR4XR.js"}, "chunk-T4OUZ3EK": {"file": "chunk-T4OUZ3EK.js"}, "chunk-JVSHQ3XX": {"file": "chunk-JVSHQ3XX.js"}, "chunk-EWTE5DHJ": {"file": "chunk-EWTE5DHJ.js"}}}