{"name": "@omni-tars/agent", "version": "0.0.1", "description": "", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "test": "vitest", "dev:agent": "tarko --agent ./"}, "dependencies": {"@omni-tars/code-agent": "workspace:*", "@omni-tars/core": "workspace:*", "@omni-tars/gui-agent": "workspace:*", "@omni-tars/mcp-agent": "workspace:*", "@tarko/agent": "workspace:*"}, "devDependencies": {"@rslib/core": "0.10.0", "@tarko/agent-cli": "workspace:*", "@tarko/agent-interface": "workspace:*", "@types/node": "22.15.30", "openai": "4.93.0", "typescript": "^5.5.3", "vitest": "^1.0.0"}}