import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/lodash.isnumber/index.js
var require_lodash = __commonJS({
  "../../node_modules/lodash.isnumber/index.js"(exports, module) {
    var numberTag = "[object Number]";
    var objectProto = Object.prototype;
    var objectToString = objectProto.toString;
    function isObjectLike(value) {
      return !!value && typeof value == "object";
    }
    function isNumber(value) {
      return typeof value == "number" || isObjectLike(value) && objectToString.call(value) == numberTag;
    }
    module.exports = isNumber;
  }
});
export default require_lodash();
//# sourceMappingURL=lodash__isnumber.js.map
