{"name": "@tarko/agent", "version": "0.3.0-beta.1", "description": "An event-stream driven meta agent framework for building effective multimodal Agents.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./_config": {"types": "./dist/_config/index.d.ts", "import": "./dist/_config/index.mjs", "require": "./dist/_config/index.js"}}, "typesVersions": {"*": {"_config": ["dist/_config/index.d.ts"]}}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "files": ["dist"], "scripts": {"dev": "rslib build --watch", "build": "rslib build", "prepublishOnly": "pnpm run build", "test": "vitest run", "test:watch": "vitest", "agent:snapshot:genreate": "npx tsx snapshot/runner.ts generate all", "agent:snapshot:test": "npx vitest snapshot/index.test.ts"}, "dependencies": {"uuid": "^9.0.0", "jsonrepair": "3.12.0", "zod-to-json-schema": "3.24.3", "@tarko/llm-client": "workspace:*", "@tarko/shared-utils": "workspace:*", "@tarko/model-provider": "workspace:*", "@tarko/agent-interface": "workspace:*"}, "devDependencies": {"@types/uuid": "^9.0.8", "@agent-infra/shared": "0.0.2", "@agent-infra/browser": "0.1.1", "@agent-infra/browser-search": "0.0.5", "@ui-tars/operator-browser": "1.2.2", "@rslib/core": "0.10.0", "@types/node": "22.15.30", "tsx": "^4.19.2", "typescript": "^5.5.3", "vitest": "3.2.4"}}