<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="
        default-src 'self' https://computer-use.console.volcengine.com/ https://*.apigateway-cn-beijing.volceapi.com/;
        frame-src 'self' https://computer-use.console.volcengine.com/ https://*.apigateway-cn-beijing.volceapi.com/;
        connect-src 'self' https: wss: ws:;
        script-src 'self' 'unsafe-eval' node:*;
        style-src 'self' 'unsafe-inline';
        img-src 'self' data: blob: https:"
    />
    <meta
      http-equiv="X-Content-Security-Policy"
      content="default-src 'self'; connect-src 'self' *; script-src 'self' 'unsafe-eval' node:*; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:"
    />
    <title>UI-TARS Desktop</title>
    <style>
      .draggable-area {
          user-select: none;
          -webkit-app-region: drag;
          cursor: grab;
      }

      .non-draggable-area {
          user-select: auto;
          -webkit-app-region: none;
      }

      ::-webkit-scrollbar {
          width: 6px;
      }

      ::-webkit-scrollbar-track {
          background-color: var(--scrollbar-background-color);
      }

      ::-webkit-scrollbar-thumb {
          background-color: var(--scrollbar-foreground-color);
          border-radius: 4px;
      }

      body {
        overflow: hidden;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-spinner {
        display: inline-block;
        width: 30px;
        height: 30px;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #767676;
        animation: spin 1s ease-in-out infinite;
        margin: 20px;
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }

  </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
