{"name": "@gui-agent/operator-browser", "description": "Native-browser operator for GUI Agent", "version": "0.3.0-beta.1", "repository": {"type": "git", "url": "https://github.com/bytedance/UI-TARS-desktop"}, "bugs": {"url": "https://github.com/bytedance/UI-TARS-desktop/issues"}, "keywords": ["AI", "Core", "SDK", "Operator", "UI-TARS"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"dev": "rslib build --watch", "build": "rslib build", "build:watch": "rslib build --watch", "test": "vitest"}, "license": "Apache-2.0", "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "dependencies": {"@agent-infra/browser": "0.1.1", "@agent-infra/logger": "0.0.2-beta.2", "@ui-tars/sdk": "1.2.3"}, "devDependencies": {"@rslib/core": "0.10.0", "typescript": "^5.7.2", "vitest": "^3.0.2", "ts-node": "^10.9.2"}}