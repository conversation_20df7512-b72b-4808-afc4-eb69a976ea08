<!DOCTYPE html><html lang="en-US" prefix="og: https://ogp.me/ns#"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="shortcut icon" href="https://developer.mozilla.org/favicon.ico"><link rel="alternate icon" type="image/svg+xml" href="https://developer.mozilla.org/favicon.svg"><link rel="apple-touch-icon" href="https://developer.mozilla.org/apple-touch-icon.528534bba673c38049c2.png"><meta name="theme-color" content="#ffffff"><link rel="manifest" href="https://developer.mozilla.org/manifest.f42880861b394dd4dc9b.json"><link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="MDN Web Docs"><title>Promise - JavaScript | MDN</title><link rel="alternate" title="Promise" href="https://developer.mozilla.org/de/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="de"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/es/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="es"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/fr/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="fr"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/ja/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="ja"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/ko/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="ko"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/pt-BR/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="pt"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/ru/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="ru"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="zh"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/zh-TW/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="zh-Hant"><link rel="alternate" title="Promise" href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise" hreflang="en"><link rel="preload" as="font" type="font/woff2" href="/static/media/Inter.var.c2fe3cb2b7c746f7966a.woff2" crossorigin=""><link rel="alternate" type="application/rss+xml" title="MDN Blog RSS Feed" href="https://developer.mozilla.org/en-US/blog/rss.xml"><meta name="description" content="The Promise object represents the eventual completion (or failure) of an asynchronous operation and its resulting value."><meta property="og:url" content="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise"><meta property="og:title" content="Promise - JavaScript | MDN"><meta property="og:type" content="website"><meta property="og:locale" content="en_US"><meta property="og:description" content="The Promise object represents the eventual completion (or failure) of an asynchronous operation and its resulting value."><meta property="og:image" content="https://developer.mozilla.org/mdn-social-share.d893525a4fb5fb1f67a2.png"><meta property="og:image:type" content="image/png"><meta property="og:image:height" content="1080"><meta property="og:image:width" content="1920"><meta property="og:image:alt" content="The MDN Web Docs logo, featuring a blue accent color, displayed on a solid black background."><meta property="og:site_name" content="MDN Web Docs"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:creator" content="MozDevNet"><link rel="canonical" href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise"><style media="print">.article-actions-container,.document-toc-container,.language-menu,.main-menu-toggle,.on-github,.page-footer,.place,.sidebar,.top-banner,.top-navigation-main,ul.prev-next{display:none!important}.main-page-content,.main-page-content pre{padding:2px}.main-page-content pre{border-left-width:2px}</style><script src="/static/js/gtag.js" defer=""></script><script defer="" src="/static/js/main.16b4b835.js"></script><link href="/static/css/main.da45ac8e.css" rel="stylesheet"><script async="" src="https://www.googletagmanager.com/gtag/js?id=UA-36116321-5"></script><script charset="utf-8" data-webpack="client:chunk-6713" src="/static/js/construct-style-sheets-polyfill.6f12cbf2.chunk.js"></script></head><body><script>if(document.body.addEventListener("load",(t=>{t.target.classList.contains("interactive")&&t.target.setAttribute("data-readystate","complete")}),{capture:!0}),window&&document.documentElement){const t={light:"#ffffff",dark:"#1b1b1b"};try{const e=window.localStorage.getItem("theme");e&&(document.documentElement.className=e,document.documentElement.style.backgroundColor=t[e]);const o=window.localStorage.getItem("nop");o&&(document.documentElement.dataset.nop=o)}catch(t){console.warn("Unable to read theme from localStorage",t)}}</script><div id="root"><ul id="nav-access" class="a11y-nav"><li><a id="skip-main" href="#content">Skip to main content</a></li><li><a id="skip-search" href="#top-nav-search-input">Skip to search</a></li><li><a id="skip-select-language" href="#languages-switcher-button">Skip to select language</a></li></ul><div class="page-wrapper  category-javascript document-page"><div class="top-banner loading"><section class="place top container"></section></div><div class="sticky-header-container"><header class="top-navigation 
      
      "><div class="container "><div class="top-navigation-wrap"><a href="/en-US/" class="logo" aria-label="MDN homepage"><svg id="mdn-docs-logo" xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 694.9 104.4" style="enable-background:new 0 0 694.9 104.4" xml:space="preserve" role="img"><title>MDN Web Docs</title><path d="M40.3 0 11.7 92.1H0L28.5 0h11.8zm10.4 0v92.1H40.3V0h10.4zM91 0 62.5 92.1H50.8L79.3 0H91zm10.4 0v92.1H91V0h10.4z" class="logo-m"></path><path d="M627.9 95.6h67v8.8h-67v-8.8z" class="logo-_"></path><path d="M367 42h-4l-10.7 30.8h-5.5l-10.8-26h-.4l-10.5 26h-5.2L308.7 42h-3.8v-5.6H323V42h-6.5l6.8 20.4h.4l10.3-26h4.7l11.2 26h.5l5.7-20.3h-6.2v-5.6H367V42zm34.9 20c-.4 3.2-2 5.9-4.7 8.2-2.8 2.3-6.5 3.4-11.3 3.4-5.4 0-9.7-1.6-13.1-4.7-3.3-3.2-5-7.7-5-13.7 0-5.7 1.6-10.3 4.7-14s7.4-5.5 12.9-5.5c5.1 0 9.1 1.6 11.9 4.7s4.3 6.9 4.3 11.3c0 1.5-.2 3-.5 4.7h-25.6c.3 7.7 4 11.6 10.9 11.6 2.9 0 5.1-.7 6.5-2 1.5-1.4 2.5-3 3-4.9l6 .9zM394 51.3c.2-2.4-.4-4.7-1.8-6.9s-3.8-3.3-7-3.3c-3.1 0-5.3 1-6.9 3-1.5 2-2.5 4.4-2.8 7.2H394zm51 2.4c0 5-1.3 9.5-4 13.7s-6.9 6.2-12.7 6.2c-6 0-10.3-2.2-12.7-6.7-.1.4-.2 1.4-.4 2.9s-.3 2.5-.4 2.9h-7.3c.3-1.7.6-3.5.8-5.3.3-1.8.4-3.7.4-5.5V22.3h-6v-5.6H416v27c1.1-2.2 2.7-4.1 4.7-5.7 2-1.6 4.8-2.4 8.4-2.4 4.6 0 8.4 1.6 11.4 4.7 3 3.2 4.5 7.6 4.5 13.4zm-7.7.6c0-4.2-1-7.4-3-9.5-2-2.2-4.4-3.3-7.4-3.3-3.4 0-6 1.2-8 3.7-1.9 2.4-2.9 5-3 7.7V57c0 3 1 5.6 3 7.7s4.5 3.1 7.6 3.1c3.6 0 6.3-1.3 8.1-3.9 1.8-2.7 2.7-5.9 2.7-9.6zm69.2 18.5h-13.2v-7.2c-1.2 2.2-2.8 4.1-4.9 5.6-2.1 1.6-4.8 2.4-8.3 2.4-4.8 0-8.7-1.6-11.6-4.9-2.9-3.2-4.3-7.7-4.3-13.3 0-5 1.3-9.6 4-13.7 2.6-4.1 6.9-6.2 12.8-6.2 5.7 0 9.8 2.2 12.3 6.5V22.3h-8.6v-5.6h15.8v50.6h6v5.5zM493.2 56v-4.4c-.1-3-1.2-5.5-3.2-7.3s-4.4-2.8-7.2-2.8c-3.6 0-6.3 1.3-8.2 3.9-1.9 2.6-2.8 5.8-2.8 9.6 0 4.1 1 7.3 3 9.5s4.5 3.3 7.4 3.3c3.2 0 5.8-1.3 7.8-3.8 2.1-2.6 3.1-5.3 3.2-8zm53.1-1.4c0 5.6-1.8 10.2-5.3 13.7s-8.2 5.3-13.9 5.3-10.1-1.7-13.4-5.1c-3.3-3.4-5-7.9-5-13.5 0-5.3 1.6-9.9 4.7-13.7 3.2-3.8 7.9-5.7 14.2-5.7s11 1.9 14.1 5.7c3 3.7 4.6 8.1 4.6 13.3zm-7.7-.2c0-4-1-7.2-3-9.5s-4.8-3.5-8.2-3.5c-3.6 0-6.4 1.2-8.3 3.7s-2.9 5.6-2.9 9.5c0 3.7.9 6.8 2.8 9.4 1.9 2.6 4.6 3.9 8.3 3.9 3.6 0 6.4-1.3 8.4-3.8 1.9-2.6 2.9-5.8 2.9-9.7zm45 5.8c-.4 3.2-1.9 6.3-4.4 9.1-2.5 2.9-6.4 4.3-11.8 4.3-5.2 0-9.4-1.6-12.6-4.8-3.2-3.2-4.8-7.7-4.8-13.7 0-5.5 1.6-10.1 4.7-13.9 3.2-3.8 7.6-5.7 13.2-5.7 2.3 0 4.6.3 6.7.8 2.2.5 4.2 1.5 6.2 2.9l1.5 9.5-5.9.7-1.3-6.1c-2.1-1.2-4.5-1.8-7.2-1.8-3.5 0-6.1 1.2-7.7 3.7-1.7 2.5-2.5 5.7-2.5 9.6 0 4.1.9 7.3 2.7 9.5 1.8 2.3 4.4 3.4 7.8 3.4 5.2 0 8.2-2.9 9.2-8.8l6.2 1.3zm34.7 1.9c0 3.6-1.5 6.5-4.6 8.5s-7 3-11.7 3c-5.7 0-10.6-1.2-14.6-3.6l1.2-8.8 5.7.6-.2 4.7c1.1.5 2.3.9 3.6 1.1s2.6.3 3.9.3c2.4 0 4.5-.4 6.5-1.3 1.9-.9 2.9-2.2 2.9-4.1 0-1.8-.8-3.1-2.3-3.8s-3.5-1.3-5.8-1.7-4.6-.9-6.9-1.4c-2.3-.6-4.2-1.6-5.7-2.9-1.6-1.4-2.3-3.5-2.3-6.3 0-4.1 1.5-6.9 4.6-8.5s6.4-2.4 9.9-2.4c2.6 0 5 .3 7.2.9 2.2.6 4.3 1.4 6.1 2.4l.8 8.8-5.8.7-.8-5.7c-2.3-1-4.7-1.6-7.2-1.6-2.1 0-3.7.4-5.1 1.1-1.3.8-2 2-2 3.8 0 1.7.8 2.9 2.3 3.6 1.5.7 3.4 1.2 5.7 1.6 2.2.4 4.5.8 6.7 1.4 2.2.6 4.1 1.6 5.7 3 1.4 1.6 2.2 3.7 2.2 6.6zM197.6 73.2h-17.1v-5.5h3.8V51.9c0-3.7-.7-6.3-2.1-7.9-1.4-1.6-3.3-2.3-5.7-2.3-3.2 0-5.6 1.1-7.2 3.4s-2.4 4.6-2.5 6.9v15.6h6v5.5h-17.1v-5.5h3.8V51.9c0-3.8-.7-6.4-2.1-7.9-1.4-1.5-3.3-2.3-5.6-2.3-3.2 0-5.5 1.1-7.2 3.3-1.6 2.2-2.4 4.5-2.5 6.9v15.8h6.9v5.5h-20.2v-5.5h6V42.4h-6.1v-5.6h13.4v6.4c1.2-2.1 2.7-3.8 4.7-5.2 2-1.3 4.4-2 7.3-2s5.3.7 7.5 2.1c2.2 1.4 3.7 3.5 4.5 6.4 1.1-2.5 2.7-4.5 4.9-6.1s4.8-2.4 7.9-2.4c3.5 0 6.5 1.1 8.9 3.3s3.7 5.6 3.7 10.2v18.2h6.1v5.5zm42.5 0h-13.2V66c-1.2 2.2-2.8 4.1-4.9 5.6-2.1 1.6-4.8 2.4-8.3 2.4-4.8 0-8.7-1.6-11.6-4.9-2.9-3.2-4.3-7.7-4.3-13.3 0-5 1.3-9.6 4-13.7 2.6-4.1 6.9-6.2 12.8-6.2s9.8 2.2 12.3 6.5V22.7h-8.6v-5.6h15.8v50.6h6v5.5zm-13.3-16.8V52c-.1-3-1.2-5.5-3.2-7.3s-4.4-2.8-7.2-2.8c-3.6 0-6.3 1.3-8.2 3.9-1.9 2.6-2.8 5.8-2.8 9.6 0 4.1 1 7.3 3 9.5s4.5 3.3 7.4 3.3c3.2 0 5.8-1.3 7.8-3.8 2.1-2.6 3.1-5.3 3.2-8zm61.5 16.8H269v-5.5h6V51.9c0-3.7-.7-6.3-2.2-7.9-1.4-1.6-3.4-2.3-5.7-2.3-3.1 0-5.6 1-7.4 3s-2.8 4.4-2.9 7v15.9h6v5.5h-19.3v-5.5h6V42.4h-6.2v-5.6h13.6V43c2.6-4.6 6.8-6.9 12.7-6.9 3.6 0 6.7 1.1 9.2 3.3s3.7 5.6 3.7 10.2v18.2h6v5.4h-.2z" class="logo-text"></path></svg></a><button title="Open main menu" type="button" class="button action has-icon main-menu-toggle" aria-haspopup="menu" aria-label="Open main menu" aria-expanded="false"><span class="button-wrap"><span class="icon icon-menu "></span><span class="visually-hidden">Open main menu</span></span></button></div><div class="top-navigation-main"><nav class="main-nav" aria-label="Main menu"><ul class="main-menu nojs"><li class="top-level-entry-container active"><button type="button" id="references-button" class="top-level-entry menu-toggle" aria-controls="references-menu" aria-expanded="false">References</button><a href="/en-US/docs/Web" class="top-level-entry">References</a><ul id="references-menu" class="submenu references hidden inline-submenu-lg" aria-labelledby="references-button"><li class="apis-link-container mobile-only "><a href="/en-US/docs/Web" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Overview / Web Technology</div><p class="submenu-item-description">Web technology reference for developers</p></div></a></li><li class="html-link-container "><a href="/en-US/docs/Web/HTML" class="submenu-item "><div class="submenu-icon html"></div><div class="submenu-content-container"><div class="submenu-item-heading">HTML</div><p class="submenu-item-description">Structure of content on the web</p></div></a></li><li class="css-link-container "><a href="/en-US/docs/Web/CSS" class="submenu-item "><div class="submenu-icon css"></div><div class="submenu-content-container"><div class="submenu-item-heading">CSS</div><p class="submenu-item-description">Code used to describe document style</p></div></a></li><li class="javascript-link-container "><a href="/en-US/docs/Web/JavaScript" class="submenu-item "><div class="submenu-icon javascript"></div><div class="submenu-content-container"><div class="submenu-item-heading">JavaScript</div><p class="submenu-item-description">General-purpose scripting language</p></div></a></li><li class="http-link-container "><a href="/en-US/docs/Web/HTTP" class="submenu-item "><div class="submenu-icon http"></div><div class="submenu-content-container"><div class="submenu-item-heading">HTTP</div><p class="submenu-item-description">Protocol for transmitting web resources</p></div></a></li><li class="apis-link-container "><a href="/en-US/docs/Web/API" class="submenu-item "><div class="submenu-icon apis"></div><div class="submenu-content-container"><div class="submenu-item-heading">Web APIs</div><p class="submenu-item-description">Interfaces for building web applications</p></div></a></li><li class="apis-link-container "><a href="/en-US/docs/Mozilla/Add-ons/WebExtensions" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Web Extensions</div><p class="submenu-item-description">Developing extensions for web browsers</p></div></a></li><li class=" "><a href="/en-US/docs/Web/Accessibility" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Accessibility</div><p class="submenu-item-description">Build web projects usable for all</p></div></a></li><li class="apis-link-container desktop-only "><a href="/en-US/docs/Web" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Web Technology</div><p class="submenu-item-description">Web technology reference for developers</p></div></a></li></ul></li><li class="top-level-entry-container "><button type="button" id="learn-button" class="top-level-entry menu-toggle" aria-controls="learn-menu" aria-expanded="false">Learn</button><a href="/en-US/docs/Learn_web_development" class="top-level-entry">Learn</a><ul id="learn-menu" class="submenu learn hidden inline-submenu-lg" aria-labelledby="learn-button"><li class="apis-link-container mobile-only "><a href="/en-US/docs/Learn_web_development" class="submenu-item "><div class="submenu-icon learn"></div><div class="submenu-content-container"><div class="submenu-item-heading">Overview / MDN Learning Area</div><p class="submenu-item-description">Learn web development</p></div></a></li><li class="apis-link-container desktop-only "><a href="/en-US/docs/Learn_web_development" class="submenu-item "><div class="submenu-icon learn"></div><div class="submenu-content-container"><div class="submenu-item-heading">MDN Learning Area</div><p class="submenu-item-description">Learn web development</p></div></a></li><li class="html-link-container "><a href="/en-US/docs/Learn_web_development/Core/Structuring_content" class="submenu-item "><div class="submenu-icon html"></div><div class="submenu-content-container"><div class="submenu-item-heading">HTML</div><p class="submenu-item-description">Learn to structure web content with HTML</p></div></a></li><li class="css-link-container "><a href="/en-US/docs/Learn_web_development/Core/Styling_basics" class="submenu-item "><div class="submenu-icon css"></div><div class="submenu-content-container"><div class="submenu-item-heading">CSS</div><p class="submenu-item-description">Learn to style content using CSS</p></div></a></li><li class="javascript-link-container "><a href="/en-US/docs/Learn_web_development/Core/Scripting" class="submenu-item "><div class="submenu-icon javascript"></div><div class="submenu-content-container"><div class="submenu-item-heading">JavaScript</div><p class="submenu-item-description">Learn to run scripts in the browser</p></div></a></li><li class=" "><a href="/en-US/docs/Learn_web_development/Core/Accessibility" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Accessibility</div><p class="submenu-item-description">Learn to make the web accessible to all</p></div></a></li></ul></li><li class="top-level-entry-container "><button type="button" id="mdn-plus-button" class="top-level-entry menu-toggle" aria-controls="mdn-plus-menu" aria-expanded="false">Plus</button><a href="/en-US/plus" class="top-level-entry">Plus</a><ul id="mdn-plus-menu" class="submenu mdn-plus hidden inline-submenu-lg" aria-labelledby="mdn-plus-button"><li class=" "><a href="/en-US/plus" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Overview</div><p class="submenu-item-description">A customized MDN experience</p></div></a></li><li class=" "><a href="/en-US/plus/ai-help" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">AI Help</div><p class="submenu-item-description">Get real-time assistance and support</p></div></a></li><li class=" "><a href="/en-US/plus/updates" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Updates</div><p class="submenu-item-description">All browser compatibility updates at a glance</p></div></a></li><li class=" "><a href="/en-US/plus/docs/features/overview" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Documentation</div><p class="submenu-item-description">Learn how to use MDN Plus</p></div></a></li><li class=" "><a href="/en-US/plus/docs/faq" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">FAQ</div><p class="submenu-item-description">Frequently asked questions about MDN Plus</p></div></a></li></ul></li><li class="top-level-entry-container "><a class="top-level-entry menu-link" href="/en-US/curriculum/">Curriculum <sup class="new">New</sup></a></li><li class="top-level-entry-container "><a class="top-level-entry menu-link" href="/en-US/blog/">Blog</a></li><li class="top-level-entry-container "><button type="button" id="tools-button" class="top-level-entry menu-toggle" aria-controls="tools-menu" aria-expanded="false">Tools</button><ul id="tools-menu" class="submenu tools hidden inline-submenu-lg" aria-labelledby="tools-button"><li class=" "><a href="/en-US/play" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">Playground</div><p class="submenu-item-description">Write, test and share your code</p></div></a></li><li class=" "><a href="/en-US/observatory" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">HTTP Observatory</div><p class="submenu-item-description">Scan a website for free</p></div></a></li><li class=" "><a href="/en-US/plus/ai-help" class="submenu-item "><div class="submenu-icon"></div><div class="submenu-content-container"><div class="submenu-item-heading">AI Help</div><p class="submenu-item-description">Get real-time assistance and support</p></div></a></li></ul></li></ul></nav><div class="header-search"><form action="/en-US/search" class="search-form search-widget" id="top-nav-search-form" role="search"><label id="top-nav-search-label" for="top-nav-search-input" class="visually-hidden">Search MDN</label><input aria-activedescendant="" aria-autocomplete="list" aria-controls="top-nav-search-menu" aria-expanded="false" aria-labelledby="top-nav-search-label" autocomplete="off" id="top-nav-search-input" role="combobox" type="search" class="search-input-field" name="q" placeholder="   " required="" value=""><button type="button" class="button action has-icon clear-search-button"><span class="button-wrap"><span class="icon icon-cancel "></span><span class="visually-hidden">Clear search input</span></span></button><button type="submit" class="button action has-icon search-button"><span class="button-wrap"><span class="icon icon-search "></span><span class="visually-hidden">Search</span></span></button><div id="top-nav-search-menu" role="listbox" aria-labelledby="top-nav-search-label"></div></form></div><div class="theme-switcher-menu"><button type="button" class="button action has-icon theme-switcher-menu small" aria-haspopup="menu"><span class="button-wrap"><span class="icon icon-theme-os-default "></span>Theme</span></button></div><ul class="auth-container"><li><a href="/users/fxa/login/authenticate/?next=%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FGlobal_Objects%2FPromise" class="login-link" rel="nofollow">Log in</a></li><li><a href="/users/fxa/login/authenticate/?next=%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FGlobal_Objects%2FPromise" target="_self" rel="nofollow" class="button primary mdn-plus-subscribe-link"><span class="button-wrap">Sign up for free</span></a></li></ul></div></div></header><div class="article-actions-container"><div class="container"><button type="button" class="button action has-icon sidebar-button" aria-label="Expand sidebar" aria-expanded="false" aria-controls="sidebar-quicklinks"><span class="button-wrap"><span class="icon icon-sidebar "></span></span></button><nav class="breadcrumbs-container" aria-label="Breadcrumb"><ol typeof="BreadcrumbList" vocab="https://schema.org/" aria-label="breadcrumbs"><li property="itemListElement" typeof="ListItem"><a href="/en-US/docs/Web" class="breadcrumb" property="item" typeof="WebPage"><span property="name">References</span></a><meta property="position" content="1"></li><li property="itemListElement" typeof="ListItem"><a href="/en-US/docs/Web/JavaScript" class="breadcrumb" property="item" typeof="WebPage"><span property="name">JavaScript</span></a><meta property="position" content="2"></li><li property="itemListElement" typeof="ListItem"><a href="/en-US/docs/Web/JavaScript/Reference" class="breadcrumb" property="item" typeof="WebPage"><span property="name">Reference</span></a><meta property="position" content="3"></li><li property="itemListElement" typeof="ListItem"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects" class="breadcrumb" property="item" typeof="WebPage"><span property="name">Standard built-in objects</span></a><meta property="position" content="4"></li><li property="itemListElement" typeof="ListItem"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="breadcrumb-current-page" property="item" typeof="WebPage"><span property="name">Promise</span></a><meta property="position" content="5"></li></ol></nav><div class="article-actions"><button type="button" class="button action has-icon article-actions-toggle" aria-label="Article actions"><span class="button-wrap"><span class="icon icon-ellipses "></span><span class="article-actions-dialog-heading">Article Actions</span></span></button><ul class="article-actions-entries"><li class="article-actions-entry"><div class="languages-switcher-menu open-on-focus-within"><button id="languages-switcher-button" type="button" class="button action small has-icon languages-switcher-menu" aria-haspopup="menu"><span class="button-wrap"><span class="icon icon-language "></span>English (US)</span></button><div class="hidden"><ul class="submenu language-menu  " aria-labelledby="language-menu-button"><li class=" "><form class="submenu-item locale-redirect-setting"><div class="group"><label class="switch"><input type="checkbox" name="locale-redirect"><span class="slider"></span><span class="label">Remember language</span></label><a href="https://github.com/orgs/mdn/discussions/739" rel="external noopener noreferrer" target="_blank" title="Enable this setting to automatically switch to this language when it's available. (Click to learn more.)"><span class="icon icon-question-mark "></span></a></div></form></li><li class=" "><a data-locale="de" href="/de/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>Deutsch</span><span title="Diese Übersetzung ist Teil eines Experiments."><span class="icon icon-experimental "></span></span></a></li><li class=" "><a data-locale="es" href="/es/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>Español</span></a></li><li class=" "><a data-locale="fr" href="/fr/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>Français</span></a></li><li class=" "><a data-locale="ja" href="/ja/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>日本語</span></a></li><li class=" "><a data-locale="ko" href="/ko/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>한국어</span></a></li><li class=" "><a data-locale="pt-BR" href="/pt-BR/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>Português (do&nbsp;Brasil)</span></a></li><li class=" "><a data-locale="ru" href="/ru/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>Русский</span></a></li><li class=" "><a data-locale="zh-CN" href="/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>中文 (简体)</span></a></li><li class=" "><a data-locale="zh-TW" href="/zh-TW/docs/Web/JavaScript/Reference/Global_Objects/Promise" class="button submenu-item"><span>正體中文 (繁體)</span></a></li></ul></div></div></li></ul></div></div></div></div><div class="main-wrapper"><div class="sidebar-container"><aside id="sidebar-quicklinks" class="sidebar"><button type="button" class="button action backdrop" aria-label="Collapse sidebar"><span class="button-wrap"></span></button><nav aria-label="Related Topics" class="sidebar-inner"><header class="sidebar-actions"><section class="sidebar-filter-container"><div class="sidebar-filter "><label id="sidebar-filter-label" class="sidebar-filter-label" for="sidebar-filter-input"><span class="icon icon-filter"></span><span class="visually-hidden">Filter sidebar</span></label><input id="sidebar-filter-input" autocomplete="off" class="sidebar-filter-input-field false" type="text" placeholder="Filter" value=""><button type="button" class="button action has-icon clear-sidebar-filter-button"><span class="button-wrap"><span class="icon icon-cancel "></span><span class="visually-hidden">Clear filter input</span></span></button></div></section></header><div class="sidebar-inner-nav"><div class="in-nav-toc"><div class="document-toc-container"><section class="document-toc"><header><h2 class="document-toc-heading">In this article</h2></header><ul class="document-toc-list"><li class="document-toc-item "><a class="document-toc-link" href="#description">Description</a></li><li class="document-toc-item "><a class="document-toc-link" href="#constructor">Constructor</a></li><li class="document-toc-item "><a class="document-toc-link" href="#static_properties">Static properties</a></li><li class="document-toc-item "><a class="document-toc-link" href="#static_methods">Static methods</a></li><li class="document-toc-item "><a class="document-toc-link" href="#instance_properties">Instance properties</a></li><li class="document-toc-item "><a class="document-toc-link" href="#instance_methods">Instance methods</a></li><li class="document-toc-item "><a class="document-toc-link" href="#examples">Examples</a></li><li class="document-toc-item "><a class="document-toc-link" href="#specifications">Specifications</a></li><li class="document-toc-item "><a class="document-toc-link" href="#browser_compatibility">Browser compatibility</a></li><li class="document-toc-item "><a class="document-toc-link" href="#see_also">See also</a></li></ul></section></div></div><div class="sidebar-body"><ol><li class="section"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects">Standard built-in objects</a></li><li class="section"><em><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise" aria-current="page"><code>Promise</code></a></em></li><li class="toggle"><details open=""><summary>Constructor</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise"><code>Promise() constructor</code></a></li></ol></details></li><li class="toggle"><details open=""><summary>Static methods</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all"><code>Promise.all()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled"><code>Promise.allSettled()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/any"><code>Promise.any()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race"><code>Promise.race()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/reject"><code>Promise.reject()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve"><code>Promise.resolve()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/try"><code>Promise.try()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers"><code>Promise.withResolvers()</code></a></li></ol></details></li><li class="toggle"><details open=""><summary>Static properties</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Symbol.species"><code>Promise[Symbol.species]</code></a></li></ol></details></li><li class="toggle"><details open=""><summary>Instance methods</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/catch"><code>Promise.prototype.catch()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/finally"><code>Promise.prototype.finally()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then"><code>Promise.prototype.then()</code></a></li></ol></details></li><li class="section">Inheritance</li><li class="section"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object"><code>Object/Function</code></a></li><li class="toggle"><details><summary>Static methods</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/apply"><code>Function.prototype.apply()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/bind"><code>Function.prototype.bind()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/call"><code>Function.prototype.call()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/toString"><code>Function.prototype.toString()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/Symbol.hasInstance"><code>Function.prototype[Symbol.hasInstance]()</code></a></li></ol></details></li><li class="toggle"><details><summary>Static properties</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/displayName"><code>Function: displayName</code></a><abbr class="icon icon-nonstandard" title="Non-standard. Check cross-browser support before using.">
<span class="visually-hidden">Non-standard</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/length"><code>Function: length</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/name"><code>Function: name</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/prototype"><code>Function: prototype</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/arguments"><code>Function.prototype.arguments</code></a><abbr class="icon icon-nonstandard" title="Non-standard. Check cross-browser support before using.">
<span class="visually-hidden">Non-standard</span>
</abbr><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
<span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/caller"><code>Function.prototype.caller</code></a><abbr class="icon icon-nonstandard" title="Non-standard. Check cross-browser support before using.">
<span class="visually-hidden">Non-standard</span>
</abbr><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
<span class="visually-hidden">Deprecated</span>
</abbr></li></ol></details></li><li class="toggle"><details><summary>Instance methods</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__defineGetter__"><code>Object.prototype.__defineGetter__()</code></a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
<span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__defineSetter__"><code>Object.prototype.__defineSetter__()</code></a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
<span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__lookupGetter__"><code>Object.prototype.__lookupGetter__()</code></a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
<span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__lookupSetter__"><code>Object.prototype.__lookupSetter__()</code></a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
<span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/hasOwnProperty"><code>Object.prototype.hasOwnProperty()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/isPrototypeOf"><code>Object.prototype.isPrototypeOf()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/propertyIsEnumerable"><code>Object.prototype.propertyIsEnumerable()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/toLocaleString"><code>Object.prototype.toLocaleString()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/toString"><code>Object.prototype.toString()</code></a></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/valueOf"><code>Object.prototype.valueOf()</code></a></li></ol></details></li><li class="toggle"><details><summary>Instance properties</summary><ol><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/proto"><code>Object.prototype.__proto__</code></a><abbr class="icon icon-deprecated" title="Deprecated. Not for use in new websites.">
<span class="visually-hidden">Deprecated</span>
</abbr></li><li><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/constructor"><code>Object.prototype.constructor</code></a></li></ol></details></li></ol></div></div><section class="place side"></section></nav></aside><div class="toc-container"><aside class="toc"><nav><div class="document-toc-container"><section class="document-toc"><header><h2 class="document-toc-heading">In this article</h2></header><ul class="document-toc-list"><li class="document-toc-item "><a class="document-toc-link" href="#description">Description</a></li><li class="document-toc-item "><a class="document-toc-link" href="#constructor">Constructor</a></li><li class="document-toc-item "><a class="document-toc-link" href="#static_properties">Static properties</a></li><li class="document-toc-item "><a class="document-toc-link" href="#static_methods">Static methods</a></li><li class="document-toc-item "><a class="document-toc-link" href="#instance_properties">Instance properties</a></li><li class="document-toc-item "><a class="document-toc-link" href="#instance_methods">Instance methods</a></li><li class="document-toc-item "><a class="document-toc-link" href="#examples">Examples</a></li><li class="document-toc-item "><a class="document-toc-link" href="#specifications">Specifications</a></li><li class="document-toc-item "><a class="document-toc-link" href="#browser_compatibility">Browser compatibility</a></li><li class="document-toc-item "><a class="document-toc-link" href="#see_also">See also</a></li></ul></section></div></nav></aside><section class="place side"></section></div></div><main id="content" class="main-content  "><article class="main-page-content" lang="en-US"><header><h1>Promise</h1><details class="baseline-indicator high"><summary><span class="indicator" role="img" aria-label="Baseline Check"></span><div class="status-title">Baseline<!-- --> <span class="not-bold">Widely available</span> *</div><div class="browsers"><span class="engine" title="Supported in Chrome and Edge"><span class="browser chrome supported" role="img" aria-label="Chrome check"></span><span class="browser edge supported" role="img" aria-label="Edge check"></span></span><span class="engine" title="Supported in Firefox"><span class="browser firefox supported" role="img" aria-label="Firefox check"></span></span><span class="engine" title="Supported in Safari"><span class="browser safari supported" role="img" aria-label="Safari check"></span></span></div><span class="icon icon-chevron "></span></summary><div class="extra"><p>This feature is well established and works across many devices and browser versions. It’s been available across browsers since<!-- --> <!-- -->July 2015<!-- -->.</p><p>* Some parts of this feature may have varying levels of support.</p><ul><li><a href="/en-US/docs/Glossary/Baseline/Compatibility" data-glean="baseline_link_learn_more" target="_blank" class="learn-more">Learn more</a></li><li><a href="#browser_compatibility" data-glean="baseline_link_bcd_table">See full compatibility</a></li><li><a href="https://survey.alchemer.com/s3/7634825/MDN-baseline-feedback?page=%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FGlobal_Objects%2FPromise&amp;level=high" data-glean="baseline_link_feedback" class="feedback-link" target="_blank" rel="noreferrer">Report feedback</a></li></ul></div></details></header><div class="section-content"><p>The <strong><code>Promise</code></strong> object represents the eventual completion (or failure) of an asynchronous operation and its resulting value.</p>
<p>To learn about the way promises work and how you can use them, we advise you to read <a href="/en-US/docs/Web/JavaScript/Guide/Using_promises">Using promises</a> first.</p></div><section aria-labelledby="description"><h2 id="description"><a href="#description">Description</a></h2><div class="section-content"><p>A <code>Promise</code> is a proxy for a value not necessarily known when the promise is created. It allows you to associate handlers with an asynchronous action's eventual success value or failure reason. This lets asynchronous methods return values like synchronous methods: instead of immediately returning the final value, the asynchronous method returns a <em>promise</em> to supply the value at some point in the future.</p>
<p>A <code>Promise</code> is in one of these states:</p>
<ul>
<li><em>pending</em>: initial state, neither fulfilled nor rejected.</li>
<li><em>fulfilled</em>: meaning that the operation was completed successfully.</li>
<li><em>rejected</em>: meaning that the operation failed.</li>
</ul>
<p>The <em>eventual state</em> of a pending promise can either be <em>fulfilled</em> with a value or <em>rejected</em> with a reason (error).
When either of these options occur, the associated handlers queued up by a promise's <code>then</code> method are called. If the promise has already been fulfilled or rejected when a corresponding handler is attached, the handler will be called, so there is no race condition between an asynchronous operation completing and its handlers being attached.</p>
<p>A promise is said to be <em>settled</em> if it is either fulfilled or rejected, but not pending.</p>
<p><img src="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/promises.png" alt="Flowchart showing how the Promise state transitions between pending, fulfilled, and rejected via then/catch handlers. A pending promise can become either fulfilled or rejected. If fulfilled, the &quot;on fulfillment&quot; handler, or first parameter of the then() method, is executed and carries out further asynchronous actions. If rejected, the error handler, either passed as the second parameter of the then() method or as the sole parameter of the catch() method, gets executed." width="801" height="297" loading="lazy"></p>
<p>You will also hear the term <em>resolved</em> used with promises — this means that the promise is settled or "locked-in" to match the eventual state of another promise, and further resolving or rejecting it has no effect. The <a href="https://github.com/domenic/promises-unwrapping/blob/master/docs/states-and-fates.md" class="external" target="_blank">States and fates</a> document from the original Promise proposal contains more details about promise terminology. Colloquially, "resolved" promises are often equivalent to "fulfilled" promises, but as illustrated in "States and fates", resolved promises can be pending or rejected as well. For example:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>new Promise((resolveOuter) =&gt; {
  resolveOuter(
    new Promise((resolveInner) =&gt; {
      setTimeout(resolveInner, 1000);
    }),
  );
});
</code></pre></div>
<p>This promise is already <em>resolved</em> at the time when it's created (because the <code>resolveOuter</code> is called synchronously), but it is resolved with another promise, and therefore won't be <em>fulfilled</em> until 1 second later, when the inner promise fulfills. In practice, the "resolution" is often done behind the scenes and not observable, and only its fulfillment or rejection are.</p>
<div class="notecard note">
<p><strong>Note:</strong>
Several other languages have mechanisms for lazy evaluation and deferring a computation, which they also call "promises", e.g., Scheme. Promises in JavaScript represent processes that are already happening, which can be chained with callback functions. If you are looking to lazily evaluate an expression, consider using a function with no arguments e.g., <code>f = () =&gt; expression</code> to create the lazily-evaluated expression, and <code>f()</code> to evaluate the expression immediately.</p>
</div>
<p><code>Promise</code> itself has no first-class protocol for cancellation, but you may be able to directly cancel the underlying asynchronous operation, typically using <a href="/en-US/docs/Web/API/AbortController"><code>AbortController</code></a>.</p></div></section><section aria-labelledby="chained_promises"><h3 id="chained_promises"><a href="#chained_promises">Chained Promises</a></h3><div class="section-content"><p>The promise methods <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then"><code>then()</code></a>, <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/catch"><code>catch()</code></a>, and <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/finally"><code>finally()</code></a> are used to associate further action with a promise that becomes settled. The <code>then()</code> method takes up to two arguments; the first argument is a callback function for the fulfilled case of the promise, and the second argument is a callback function for the rejected case. The <code>catch()</code> and <code>finally()</code> methods call <code>then()</code> internally and make error handling less verbose. For example, a <code>catch()</code> is really just a <code>then()</code> without passing the fulfillment handler. As these methods return promises, they can be chained. For example:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>const myPromise = new Promise((resolve, reject) =&gt; {
  setTimeout(() =&gt; {
    resolve("foo");
  }, 300);
});

myPromise
  .then(handleFulfilledA, handleRejectedA)
  .then(handleFulfilledB, handleRejectedB)
  .then(handleFulfilledC, handleRejectedC);
</code></pre></div>
<p>We will use the following terminology: <em>initial promise</em> is the promise on which <code>then</code> is called; <em>new promise</em> is the promise returned by <code>then</code>. The two callbacks passed to <code>then</code> are called <em>fulfillment handler</em> and <em>rejection handler</em>, respectively.</p>
<p>The settled state of the initial promise determines which handler to execute.</p>
<ul>
<li>If the initial promise is fulfilled, the fulfillment handler is called with the fulfillment value.</li>
<li>If the initial promise is rejected, the rejection handler is called with the rejection reason.</li>
</ul>
<p>The completion of the handler determines the settled state of the new promise.</p>
<ul>
<li>If the handler returns a <a href="#thenables">thenable</a> value, the new promise settles in the same state as the returned value.</li>
<li>If the handler returns a non-thenable value, the new promise is fulfilled with the returned value.</li>
<li>If the handler throws an error, the new promise is rejected with the thrown error.</li>
<li>If the initial promise has no corresponding handler attached, the new promise will settle to the same state as the initial promise — that is, without a rejection handler, a rejected promise stays rejected with the same reason.</li>
</ul>
<p>For example, in the code above, if <code>myPromise</code> rejects, <code>handleRejectedA</code> will be called, and if <code>handleRejectedA</code> completes normally (without throwing or returning a rejected promise), the promise returned by the first <code>then</code> will be fulfilled instead of staying rejected. Therefore, if an error must be handled immediately, but we want to maintain the error state down the chain, we must throw an error of some type in the rejection handler. On the other hand, in the absence of an immediate need, we can leave out error handling until the final <code>catch()</code> handler.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>myPromise
  .then(handleFulfilledA)
  .then(handleFulfilledB)
  .then(handleFulfilledC)
  .catch(handleRejectedAny);
</code></pre></div>
<p>Using <a href="/en-US/docs/Web/JavaScript/Reference/Functions/Arrow_functions">arrow functions</a> for the callback functions, implementation of the promise chain might look something like this:</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>myPromise
  .then((value) =&gt; `${value} and bar`)
  .then((value) =&gt; `${value} and bar again`)
  .then((value) =&gt; `${value} and again`)
  .then((value) =&gt; `${value} and again`)
  .then((value) =&gt; {
    console.log(value);
  })
  .catch((err) =&gt; {
    console.error(err);
  });
</code></pre></div>
<div class="notecard note">
<p><strong>Note:</strong>
For faster execution, all synchronous actions should preferably be done within one handler, otherwise it would take several ticks to execute all handlers in sequence.</p>
</div>
<p>JavaScript maintains a <a href="/en-US/docs/Web/JavaScript/Reference/Execution_model">job queue</a>. Each time, JavaScript picks a job from the queue and executes it to completion. The jobs are defined by the executor of the <code>Promise()</code> constructor, the handlers passed to <code>then</code>, or any platform API that returns a promise. The promises in a chain represent the dependency relationship between these jobs. When a promise settles, the respective handlers associated with it are added to the back of the job queue.</p>
<p>A promise can participate in more than one chain. For the following code, the fulfillment of <code>promiseA</code> will cause both <code>handleFulfilled1</code> and <code>handleFulfilled2</code> to be added to the job queue. Because <code>handleFulfilled1</code> is registered first, it will be invoked first.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>const promiseA = new Promise(myExecutorFunc);
const promiseB = promiseA.then(handleFulfilled1, handleRejected1);
const promiseC = promiseA.then(handleFulfilled2, handleRejected2);
</code></pre></div>
<p>An action can be assigned to an already settled promise. In this case, the action is added immediately to the back of the job queue and will be performed when all existing jobs are completed. Therefore, an action for an already "settled" promise will occur only after the current synchronous code completes and at least one loop-tick has passed. This guarantees that promise actions are asynchronous.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>const promiseA = new Promise((resolve, reject) =&gt; {
  resolve(777);
});
// At this point, "promiseA" is already settled.
promiseA.then((val) =&gt; console.log("asynchronous logging has val:", val));
console.log("immediate logging");

// produces output in this order:
// immediate logging
// asynchronous logging has val: 777
</code></pre></div></div></section><section aria-labelledby="thenables"><h3 id="thenables"><a href="#thenables">Thenables</a></h3><div class="section-content"><p>The JavaScript ecosystem had made multiple Promise implementations long before it became part of the language. Despite being represented differently internally, at the minimum, all Promise-like objects implement the <em>Thenable</em> interface. A thenable implements the <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then"><code>.then()</code></a> method, which is called with two callbacks: one for when the promise is fulfilled, one for when it's rejected. Promises are thenables as well.</p>
<p>To interoperate with the existing Promise implementations, the language allows using thenables in place of promises. For example, <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve"><code>Promise.resolve</code></a> will not only resolve promises, but also trace thenables.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>const aThenable = {
  then(onFulfilled, onRejected) {
    onFulfilled({
      // The thenable is fulfilled with another thenable
      then(onFulfilled, onRejected) {
        onFulfilled(42);
      },
    });
  },
};

Promise.resolve(aThenable); // A promise fulfilled with 42
</code></pre></div></div></section><section aria-labelledby="promise_concurrency"><h3 id="promise_concurrency"><a href="#promise_concurrency">Promise concurrency</a></h3><div class="section-content"><p>The <code>Promise</code> class offers four static methods to facilitate async task <a href="https://en.wikipedia.org/wiki/Concurrent_computing" class="external" target="_blank">concurrency</a>:</p>
<dl>
<dt id="promise.all"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all"><code>Promise.all()</code></a></dt>
<dd>
<p>Fulfills when <strong>all</strong> of the promises fulfill; rejects when <strong>any</strong> of the promises rejects.</p>
</dd>
<dt id="promise.allsettled"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled"><code>Promise.allSettled()</code></a></dt>
<dd>
<p>Fulfills when <strong>all</strong> promises settle.</p>
</dd>
<dt id="promise.any"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/any"><code>Promise.any()</code></a></dt>
<dd>
<p>Fulfills when <strong>any</strong> of the promises fulfills; rejects when <strong>all</strong> of the promises reject.</p>
</dd>
<dt id="promise.race"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race"><code>Promise.race()</code></a></dt>
<dd>
<p>Settles when <strong>any</strong> of the promises settles. In other words, fulfills when any of the promises fulfills; rejects when any of the promises rejects.</p>
</dd>
</dl>
<p>All these methods take an <a href="/en-US/docs/Web/JavaScript/Reference/Iteration_protocols#the_iterable_protocol">iterable</a> of promises (<a href="#thenables">thenables</a>, to be exact) and return a new promise. They all support subclassing, which means they can be called on subclasses of <code>Promise</code>, and the result will be a promise of the subclass type. To do so, the subclass's constructor must implement the same signature as the <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise"><code>Promise()</code></a> constructor — accepting a single <code>executor</code> function that can be called with the <code>resolve</code> and <code>reject</code> callbacks as parameters. The subclass must also have a <code>resolve</code> static method that can be called like <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve"><code>Promise.resolve()</code></a> to resolve values to promises.</p>
<p>Note that JavaScript is <a href="/en-US/docs/Glossary/Thread">single-threaded</a> by nature, so at a given instant, only one task will be executing, although control can shift between different promises, making execution of the promises appear concurrent. <a href="https://en.wikipedia.org/wiki/Parallel_computing" class="external" target="_blank">Parallel execution</a> in JavaScript can only be achieved through <a href="/en-US/docs/Web/API/Web_Workers_API">worker threads</a>.</p></div></section><section aria-labelledby="constructor"><h2 id="constructor"><a href="#constructor">Constructor</a></h2><div class="section-content"><dl>
<dt id="promise"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise"><code>Promise()</code></a></dt>
<dd>
<p>Creates a new <code>Promise</code> object. The constructor is primarily used to wrap functions that do not already support promises.</p>
</dd>
</dl></div></section><section aria-labelledby="static_properties"><h2 id="static_properties"><a href="#static_properties">Static properties</a></h2><div class="section-content"><dl>
<dt id="promisesymbol.species"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Symbol.species"><code>Promise[Symbol.species]</code></a></dt>
<dd>
<p>Returns the constructor used to construct return values from promise methods.</p>
</dd>
</dl></div></section><section aria-labelledby="static_methods"><h2 id="static_methods"><a href="#static_methods">Static methods</a></h2><div class="section-content"><dl>
<dt id="promise.all_2"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all"><code>Promise.all()</code></a></dt>
<dd>
<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise fulfills when all of the input's promises fulfill (including when an empty iterable is passed), with an array of the fulfillment values. It rejects when any of the input's promises reject, with this first rejection reason.</p>
</dd>
<dt id="promise.allsettled_2"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled"><code>Promise.allSettled()</code></a></dt>
<dd>
<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise fulfills when all of the input's promises settle (including when an empty iterable is passed), with an array of objects that describe the outcome of each promise.</p>
</dd>
<dt id="promise.any_2"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/any"><code>Promise.any()</code></a></dt>
<dd>
<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise fulfills when any of the input's promises fulfill, with this first fulfillment value. It rejects when all of the input's promises reject (including when an empty iterable is passed), with an <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError"><code>AggregateError</code></a> containing an array of rejection reasons.</p>
</dd>
<dt id="promise.race_2"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race"><code>Promise.race()</code></a></dt>
<dd>
<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise settles with the eventual state of the first promise that settles.</p>
</dd>
<dt id="promise.reject"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/reject"><code>Promise.reject()</code></a></dt>
<dd>
<p>Returns a new <code>Promise</code> object that is rejected with the given reason.</p>
</dd>
<dt id="promise.resolve"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve"><code>Promise.resolve()</code></a></dt>
<dd>
<p>Returns a <code>Promise</code> object that is resolved with the given value. If the value is a thenable (i.e., has a <code>then</code> method), the returned promise will "follow" that thenable, adopting its eventual state; otherwise, the returned promise will be fulfilled with the value.</p>
</dd>
<dt id="promise.try"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/try"><code>Promise.try()</code></a></dt>
<dd>
<p>Takes a callback of any kind (returns or throws, synchronously or asynchronously) and wraps its result in a <code>Promise</code>.</p>
</dd>
<dt id="promise.withresolvers"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers"><code>Promise.withResolvers()</code></a></dt>
<dd>
<p>Returns an object containing a new <code>Promise</code> object and two functions to resolve or reject it, corresponding to the two parameters passed to the executor of the <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise"><code>Promise()</code></a> constructor.</p>
</dd>
</dl></div></section><section aria-labelledby="instance_properties"><h2 id="instance_properties"><a href="#instance_properties">Instance properties</a></h2><div class="section-content"><p>These properties are defined on <code>Promise.prototype</code> and shared by all <code>Promise</code> instances.</p>
<dl>
<dt id="promise.prototype.constructor"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/constructor"><code>Promise.prototype.constructor</code></a></dt>
<dd>
<p>The constructor function that created the instance object. For <code>Promise</code> instances, the initial value is the <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise"><code>Promise</code></a> constructor.</p>
</dd>
<dt id="promise.prototypesymbol.tostringtag"><a href="#promise.prototypesymbol.tostringtag"><code>Promise.prototype[Symbol.toStringTag]</code></a></dt>
<dd>
<p>The initial value of the <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol/toStringTag"><code>[Symbol.toStringTag]</code></a> property is the string <code>"Promise"</code>. This property is used in <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/toString"><code>Object.prototype.toString()</code></a>.</p>
</dd>
</dl></div></section><section aria-labelledby="instance_methods"><h2 id="instance_methods"><a href="#instance_methods">Instance methods</a></h2><div class="section-content"><dl>
<dt id="promise.prototype.catch"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/catch"><code>Promise.prototype.catch()</code></a></dt>
<dd>
<p>Appends a rejection handler callback to the promise, and returns a new promise resolving to the return value of the callback if it is called, or to its original fulfillment value if the promise is instead fulfilled.</p>
</dd>
<dt id="promise.prototype.finally"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/finally"><code>Promise.prototype.finally()</code></a></dt>
<dd>
<p>Appends a handler to the promise, and returns a new promise that is resolved when the original promise is resolved. The handler is called when the promise is settled, whether fulfilled or rejected.</p>
</dd>
<dt id="promise.prototype.then"><a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then"><code>Promise.prototype.then()</code></a></dt>
<dd>
<p>Appends fulfillment and rejection handlers to the promise, and returns a new promise resolving to the return value of the called handler, or to its original settled value if the promise was not handled (i.e., if the relevant handler <code>onFulfilled</code> or <code>onRejected</code> is not a function).</p>
</dd>
</dl></div></section><section aria-labelledby="examples"><h2 id="examples"><a href="#examples">Examples</a></h2><div class="section-content"></div></section><section aria-labelledby="basic_example"><h3 id="basic_example"><a href="#basic_example">Basic Example</a></h3><div class="section-content"><p>In this example, we use <code>setTimeout(...)</code> to simulate async code.
In reality, you will probably be using something like XHR or an HTML API.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>const myFirstPromise = new Promise((resolve, reject) =&gt; {
  // We call resolve(...) when what we were doing asynchronously
  // was successful, and reject(...) when it failed.
  setTimeout(() =&gt; {
    resolve("Success!"); // Yay! Everything went well!
  }, 250);
});

myFirstPromise.then((successMessage) =&gt; {
  // successMessage is whatever we passed in the resolve(...) function above.
  // It doesn't have to be a string, but if it is only a succeed message, it probably will be.
  console.log(`Yay! ${successMessage}`);
});
</code></pre></div></div></section><section aria-labelledby="example_with_diverse_situations"><h3 id="example_with_diverse_situations"><a href="#example_with_diverse_situations">Example with diverse situations</a></h3><div class="section-content"><p>This example shows diverse techniques for using Promise capabilities and diverse situations that can occur. To understand this, start by scrolling to the bottom of the code block, and examine the promise chain. Upon provision of an initial promise, a chain of promises can follow. The chain is composed of <code>.then()</code> calls, and typically (but not necessarily) has a single <code>.catch()</code> at the end, optionally followed by <code>.finally()</code>. In this example, the promise chain is initiated by a custom-written <code>new Promise()</code> construct; but in actual practice, promise chains more typically start with an API function (written by someone else) that returns a promise.</p>
<p>The example function <code>tetheredGetNumber()</code> shows that a promise generator will utilize <code>reject()</code> while setting up an asynchronous call, or within the call-back, or both. The function <code>promiseGetWord()</code> illustrates how an API function might generate and return a promise in a self-contained manner.</p>
<p>Note that the function <code>troubleWithGetNumber()</code> ends with a <code>throw</code>. That is forced because a promise chain goes through all the <code>.then()</code> promises, even after an error, and without the <code>throw</code>, the error would seem "fixed". This is a hassle, and for this reason, it is common to omit <code>onRejected</code> throughout the chain of <code>.then()</code> promises, and just have a single <code>onRejected</code> in the final <code>catch()</code>.</p>
<p>This code can be run under NodeJS. Comprehension is enhanced by seeing the errors actually occur. To force more errors, change the <code>threshold</code> values.</p>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate"><code>// To experiment with error handling, "threshold" values cause errors randomly
const THRESHOLD_A = 8; // can use zero 0 to guarantee error

function tetheredGetNumber(resolve, reject) {
  setTimeout(() =&gt; {
    const randomInt = Date.now();
    const value = randomInt % 10;
    if (value &lt; THRESHOLD_A) {
      resolve(value);
    } else {
      reject(new RangeError(`Too large: ${value}`));
    }
  }, 500);
}

function determineParity(value) {
  const isOdd = value % 2 === 1;
  return { value, isOdd };
}

function troubleWithGetNumber(reason) {
  const err = new Error("Trouble getting number", { cause: reason });
  console.error(err);
  throw err;
}

function promiseGetWord(parityInfo) {
  return new Promise((resolve, reject) =&gt; {
    const { value, isOdd } = parityInfo;
    if (value &gt;= THRESHOLD_A - 1) {
      reject(new RangeError(`Still too large: ${value}`));
    } else {
      parityInfo.wordEvenOdd = isOdd ? "odd" : "even";
      resolve(parityInfo);
    }
  });
}

new Promise(tetheredGetNumber)
  .then(determineParity, troubleWithGetNumber)
  .then(promiseGetWord)
  .then((info) =&gt; {
    console.log(`Got: ${info.value}, ${info.wordEvenOdd}`);
    return info;
  })
  .catch((reason) =&gt; {
    if (reason.cause) {
      console.error("Had previously handled error");
    } else {
      console.error(`Trouble with promiseGetWord(): ${reason}`);
    }
  })
  .finally((info) =&gt; console.log("All done"));
</code></pre></div></div></section><section aria-labelledby="advanced_example"><h3 id="advanced_example"><a href="#advanced_example">Advanced Example</a></h3><div class="section-content"><p>This small example shows the mechanism of a <code>Promise</code>. The <code>testPromise()</code> method is called each time the <a href="/en-US/docs/Web/HTML/Reference/Elements/button"><code>&lt;button&gt;</code></a> is clicked. It creates a promise that will be fulfilled, using <a href="/en-US/docs/Web/API/Window/setTimeout" title="setTimeout()"><code>setTimeout()</code></a>, to the promise count (number starting from 1) every 1-3 seconds, at random. The <code>Promise()</code> constructor is used to create the promise.</p>
<p>The fulfillment of the promise is logged, via a fulfill callback set using <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then"><code>p1.then()</code></a>. A few logs show how the synchronous part of the method is decoupled from the asynchronous completion of the promise.</p>
<p>By clicking the button several times in a short amount of time, you'll even see the different promises being fulfilled one after another.</p>
<h4 id="html">HTML</h4>
<div class="code-example"><div class="example-header"><span class="language-name">html</span></div><pre class="brush: html notranslate live-sample---advanced_example"><code>&lt;button id="make-promise"&gt;Make a promise!&lt;/button&gt;
&lt;div id="log"&gt;&lt;/div&gt;
</code></pre></div>
<h4 id="javascript">JavaScript</h4>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js notranslate live-sample---advanced_example"><code>"use strict";

let promiseCount = 0;

function testPromise() {
  const thisPromiseCount = ++promiseCount;
  const log = document.getElementById("log");
  // begin
  log.insertAdjacentHTML("beforeend", `${thisPromiseCount}) Started&lt;br&gt;`);
  // We make a new promise: we promise a numeric count of this promise,
  // starting from 1 (after waiting 3s)
  const p1 = new Promise((resolve, reject) =&gt; {
    // The executor function is called with the ability
    // to resolve or reject the promise
    log.insertAdjacentHTML(
      "beforeend",
      `${thisPromiseCount}) Promise constructor&lt;br&gt;`,
    );
    // This is only an example to create asynchronism
    setTimeout(
      () =&gt; {
        // We fulfill the promise
        resolve(thisPromiseCount);
      },
      Math.random() * 2000 + 1000,
    );
  });

  // We define what to do when the promise is resolved with the then() call,
  // and what to do when the promise is rejected with the catch() call
  p1.then((val) =&gt; {
    // Log the fulfillment value
    log.insertAdjacentHTML("beforeend", `${val}) Promise fulfilled&lt;br&gt;`);
  }).catch((reason) =&gt; {
    // Log the rejection reason
    console.log(`Handle rejected promise (${reason}) here.`);
  });
  // end
  log.insertAdjacentHTML("beforeend", `${thisPromiseCount}) Promise made&lt;br&gt;`);
}

const btn = document.getElementById("make-promise");
btn.addEventListener("click", testPromise);
</code></pre></div>
<h4 id="result">Result</h4>
<div class="code-example"><div class="example-header"></div><iframe class="sample-code-frame" title="Advanced Example sample" id="frame_advanced_example" width="500" height="200" src="about:blank" data-live-path="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/" data-live-id="advanced_example" sandbox="allow-same-origin allow-scripts" loading="lazy"></iframe></div></div></section><section aria-labelledby="loading_an_image_with_xhr"><h3 id="loading_an_image_with_xhr"><a href="#loading_an_image_with_xhr">Loading an image with XHR</a></h3><div class="section-content"><p>Another example using <code>Promise</code> and <a href="/en-US/docs/Web/API/XMLHttpRequest"><code>XMLHttpRequest</code></a> to load an image is shown below.
Each step is commented on and allows you to follow the Promise and XHR architecture closely.</p>
<div class="code-example"><pre class="brush: html hidden live-sample___promises notranslate live-sample---promises"><code>&lt;h1&gt;Promise example&lt;/h1&gt;
</code></pre></div>
<div class="code-example"><div class="example-header"><span class="language-name">js</span></div><pre class="brush: js live-sample___promises notranslate live-sample---promises"><code>function imgLoad(url) {
  // Create new promise with the Promise() constructor;
  // This has as its argument a function with two parameters, resolve and reject
  return new Promise((resolve, reject) =&gt; {
    // XHR to load an image
    const request = new XMLHttpRequest();
    request.open("GET", url);
    request.responseType = "blob";
    // When the request loads, check whether it was successful
    request.onload = () =&gt; {
      if (request.status === 200) {
        // If successful, resolve the promise by passing back the request response
        resolve(request.response);
      } else {
        // If it fails, reject the promise with an error message
        reject(
          Error(
            `Image didn't load successfully; error code: + ${request.statusText}`,
          ),
        );
      }
    };
    // Handle network errors
    request.onerror = () =&gt; reject(new Error("There was a network error."));
    // Send the request
    request.send();
  });
}

// Get a reference to the body element, and create a new image object
const body = document.querySelector("body");
const myImage = new Image();
const imgUrl =
  "https://mdn.github.io/shared-assets/images/examples/round-balloon.png";

// Call the function with the URL we want to load, then chain the
// promise then() method with two callbacks
imgLoad(imgUrl).then(
  (response) =&gt; {
    // The first runs when the promise resolves, with the request.response
    // specified within the resolve() method.
    const imageURL = URL.createObjectURL(response);
    myImage.src = imageURL;
    body.appendChild(myImage);
  },
  (error) =&gt; {
    // The second runs when the promise
    // is rejected, and logs the Error specified with the reject() method.
    console.log(error);
  },
);
</code></pre></div>
<div class="code-example"><div class="example-header"></div><iframe class="sample-code-frame" title="promises sample" id="frame_promises" height="240px" src="about:blank" data-live-path="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/" data-live-id="promises" sandbox="allow-same-origin allow-scripts" loading="lazy"></iframe></div></div></section><section aria-labelledby="incumbent_settings_object_tracking"><h3 id="incumbent_settings_object_tracking"><a href="#incumbent_settings_object_tracking">Incumbent settings object tracking</a></h3><div class="section-content"><p>A settings object is an <a href="https://html.spec.whatwg.org/multipage/webappapis.html#environment-settings-object" class="external" target="_blank">environment</a> that provides additional information when JavaScript code is running. This includes the realm and module map, as well as HTML specific information such as the origin. The incumbent settings object is tracked in order to ensure that the browser knows which one to use for a given piece of user code.</p>
<p>To better picture this, we can take a closer look at how the realm might be an issue. A <strong>realm</strong> can be roughly thought of as the global object. What is unique about realms is that they hold all of the necessary information to run JavaScript code. This includes objects like <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array"><code>Array</code></a> and <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error"><code>Error</code></a>. Each settings object has its own "copy" of these and they are not shared. That can cause some unexpected behavior in relation to promises. In order to get around this, we track something called the <strong>incumbent settings object</strong>. This represents information specific to the context of the user code responsible for a certain function call.</p>
<p>To illustrate this a bit further we can take a look at how an <a href="/en-US/docs/Web/HTML/Reference/Elements/iframe"><code>&lt;iframe&gt;</code></a> embedded in a document communicates with its host. Since all web APIs are aware of the incumbent settings object, the following will work in all browsers:</p>
<div class="code-example"><div class="example-header"><span class="language-name">html</span></div><pre class="brush: html notranslate"><code>&lt;!doctype html&gt; &lt;iframe&gt;&lt;/iframe&gt;
&lt;!-- we have a realm here --&gt;
&lt;script&gt;
  // we have a realm here as well
  const bound = frames[0].postMessage.bind(frames[0], "some data", "*");
  // bound is a built-in function — there is no user
  // code on the stack, so which realm do we use?
  setTimeout(bound);
  // this still works, because we use the youngest
  // realm (the incumbent) on the stack
&lt;/script&gt;
</code></pre></div>
<p>The same concept applies to promises. If we modify the above example a little bit, we get this:</p>
<div class="code-example"><div class="example-header"><span class="language-name">html</span></div><pre class="brush: html notranslate"><code>&lt;!doctype html&gt; &lt;iframe&gt;&lt;/iframe&gt;
&lt;!-- we have a realm here --&gt;
&lt;script&gt;
  // we have a realm here as well
  const bound = frames[0].postMessage.bind(frames[0], "some data", "*");
  // bound is a built in function — there is no user
  // code on the stack — which realm do we use?
  Promise.resolve(undefined).then(bound);
  // this still works, because we use the youngest
  // realm (the incumbent) on the stack
&lt;/script&gt;
</code></pre></div>
<p>If we change this so that the <code>&lt;iframe&gt;</code> in the document is listening to post messages, we can observe the effect of the incumbent settings object:</p>
<div class="code-example"><div class="example-header"><span class="language-name">html</span></div><pre class="brush: html notranslate"><code>&lt;!-- y.html --&gt;
&lt;!doctype html&gt;
&lt;iframe src="x.html"&gt;&lt;/iframe&gt;
&lt;script&gt;
  const bound = frames[0].postMessage.bind(frames[0], "some data", "*");
  Promise.resolve(undefined).then(bound);
&lt;/script&gt;
</code></pre></div>
<div class="code-example"><div class="example-header"><span class="language-name">html</span></div><pre class="brush: html notranslate"><code>&lt;!-- x.html --&gt;
&lt;!doctype html&gt;
&lt;script&gt;
  window.addEventListener(
    "message",
    (event) =&gt; {
      document.querySelector("#text").textContent = "hello";
      // this code will only run in browsers that track the incumbent settings object
      console.log(event);
    },
    false,
  );
&lt;/script&gt;
</code></pre></div>
<p>In the above example, the inner text of the <code>&lt;iframe&gt;</code> will be updated only if the incumbent settings object is tracked. This is because without tracking the incumbent, we may end up using the wrong environment to send the message.</p>
<div class="notecard note">
<p><strong>Note:</strong>
Currently, incumbent realm tracking is fully implemented in Firefox, and has partial implementations in Chrome and Safari.</p>
</div></div></section><h2 id="specifications"><a href="#specifications">Specifications</a></h2><table class="standard-table"><thead><tr><th scope="col">Specification</th></tr></thead><tbody><tr><td><a href="https://tc39.es/ecma262/multipage/control-abstraction-objects.html#sec-promise-objects">ECMAScript® 2026 Language&nbsp;Specification<!-- --> <br><small># <!-- -->sec-promise-objects</small></a></td></tr></tbody></table><!--$--><h2 id="browser_compatibility"><a href="#browser_compatibility">Browser compatibility</a></h2><lazy-compat-table></lazy-compat-table><!--/$--><section aria-labelledby="see_also"><h2 id="see_also"><a href="#see_also">See also</a></h2><div class="section-content"><ul>
<li><a href="https://github.com/zloirock/core-js#ecmascript-promise" class="external" target="_blank">Polyfill of <code>Promise</code> in <code>core-js</code></a></li>
<li><a href="/en-US/docs/Web/JavaScript/Guide/Using_promises">Using promises</a> guide</li>
<li><a href="https://promisesaplus.com/" class="external" target="_blank">Promises/A+ specification</a></li>
<li><a href="https://web.dev/articles/promises" class="external" target="_blank">JavaScript Promises: an introduction</a> on web.dev (2013)</li>
<li><a href="https://www.slideshare.net/slideshow/callbacks-promises-and-coroutines-oh-my-the-evolution-of-asynchronicity-in-javascript/9953720" class="external" target="_blank">Callbacks, Promises, and Coroutines: Asynchronous Programming Patterns in JavaScript</a> slide show by Domenic Denicola (2011)</li>
</ul></div></section></article><aside class="article-footer"><div class="article-footer-inner"><div class="svg-container"><svg xmlns="http://www.w3.org/2000/svg" width="162" height="162" viewBox="0 0 162 162" fill="none" role="none"><mask id="b" fill="#fff"><path d="M97.203 47.04c8.113-7.886 18.004-13.871 28.906-17.492a78 78 0 0 1 33.969-3.39c11.443 1.39 22.401 5.295 32.024 11.411s17.656 14.28 23.476 23.86c5.819 9.579 9.269 20.318 10.083 31.385a69.85 69.85 0 0 1-5.387 32.44c-4.358 10.272-11.115 19.443-19.747 26.801-8.632 7.359-18.908 12.709-30.034 15.637l-6.17-21.698c7.666-2.017 14.746-5.703 20.694-10.773 5.948-5.071 10.603-11.389 13.606-18.467a48.14 48.14 0 0 0 3.712-22.352c-.561-7.625-2.938-15.025-6.948-21.625s-9.544-12.226-16.175-16.44-14.181-6.904-22.065-7.863a53.75 53.75 0 0 0-23.405 2.336c-7.513 2.495-14.327 6.62-19.918 12.053z"></path></mask><path stroke="url(#a)" stroke-dasharray="6, 6" stroke-width="2" d="M97.203 47.04c8.113-7.886 18.004-13.871 28.906-17.492a78 78 0 0 1 33.969-3.39c11.443 1.39 22.401 5.295 32.024 11.411s17.656 14.28 23.476 23.86c5.819 9.579 9.269 20.318 10.083 31.385a69.85 69.85 0 0 1-5.387 32.44c-4.358 10.272-11.115 19.443-19.747 26.801-8.632 7.359-18.908 12.709-30.034 15.637l-6.17-21.698c7.666-2.017 14.746-5.703 20.694-10.773 5.948-5.071 10.603-11.389 13.606-18.467a48.14 48.14 0 0 0 3.712-22.352c-.561-7.625-2.938-15.025-6.948-21.625s-9.544-12.226-16.175-16.44-14.181-6.904-22.065-7.863a53.75 53.75 0 0 0-23.405 2.336c-7.513 2.495-14.327 6.62-19.918 12.053z" mask="url(#b)" style="stroke:url(#a)" transform="translate(-63.992 -25.587)"></path><ellipse cx="8.066" cy="111.597" fill="var(--background-tertiary)" rx="53.677" ry="53.699" transform="matrix(.71707 -.697 .7243 .6895 0 0)"></ellipse><g clip-path="url(#c)" transform="translate(-63.992 -25.587)"><path fill="#9abff5" d="m144.256 137.379 32.906 12.434a4.41 4.41 0 0 1 2.559 5.667l-9.326 24.679a4.41 4.41 0 0 1-5.667 2.559l-8.226-3.108-2.332 6.17c-.466 1.233-.375 1.883-1.609 1.417l-2.253-.527c-.411-.155-.95-.594-1.206-1.161l-4.734-10.484-12.545-4.741a4.41 4.41 0 0 1-2.559-5.667l9.325-24.679a4.41 4.41 0 0 1 5.667-2.559m9.961 29.617 8.227 3.108 3.264-8.638-.498-6.768-4.113-1.555.548 7.258-4.319-1.632zm-12.339-4.663 8.226 3.108 3.264-8.637-.498-6.769-4.113-1.554.548 7.257-4.319-1.632z"></path></g><g clip-path="url(#d)" transform="translate(-63.992 -25.587)"><path fill="#81b0f3" d="M135.35 60.136 86.67 41.654c-3.346-1.27-7.124.428-8.394 3.775L64.414 81.938c-1.27 3.347.428 7.125 3.774 8.395l12.17 4.62-3.465 9.128c-.693 1.826-1.432 2.457.394 3.15l3.014 1.625c.609.231 1.637.274 2.477-.104l15.53-6.983 18.56 7.047c3.346 1.27 7.124-.428 8.395-3.775l13.862-36.51c1.27-3.346-.428-7.124-3.775-8.395M95.261 83.207l-12.17-4.62 4.852-12.779 7.19-7.017 6.085 2.31-7.725 7.51 6.389 2.426zm18.255 6.93-12.17-4.62 4.852-12.778 7.189-7.017 6.085 2.31-7.725 7.51 6.39 2.426z"></path></g><defs><clipPath id="c"><path fill="#fff" d="m198.638 146.586-65.056-24.583-24.583 65.057 65.056 24.582z"></path></clipPath><clipPath id="d"><path fill="#fff" d="m66.438 14.055 96.242 36.54-36.54 96.243-96.243-36.54z"></path></clipPath><linearGradient id="a" x1="97.203" x2="199.995" y1="47.04" y2="152.793" gradientUnits="userSpaceOnUse"><stop stop-color="#086DFC"></stop><stop offset="0.246" stop-color="#2C81FA"></stop><stop offset="0.516" stop-color="#5497F8"></stop><stop offset="0.821" stop-color="#80B0F6"></stop><stop offset="1" stop-color="#9ABFF5"></stop></linearGradient></defs></svg></div><h2>Help improve MDN</h2><fieldset class="feedback"><label>Was this page helpful to you?</label><div class="button-container"><button type="button" class="button primary has-icon yes"><span class="button-wrap"><span class="icon icon-thumbs-up "></span>Yes</span></button><button type="button" class="button primary has-icon no"><span class="button-wrap"><span class="icon icon-thumbs-down "></span>No</span></button></div></fieldset><a class="contribute" href="https://github.com/mdn/content/blob/main/CONTRIBUTING.md" title="This will take you to our contribution guidelines on GitHub." target="_blank" rel="noopener noreferrer">Learn how to contribute</a>.<p class="last-modified-date">This page was last modified on<!-- --> <time datetime="2025-05-23T04:35:27.000Z">May 23, 2025</time> by<!-- --> <a href="/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/contributors.txt" rel="nofollow">MDN contributors</a>.</p><div id="on-github" class="on-github"><a href="https://github.com/mdn/content/blob/main/files/en-us/web/javascript/reference/global_objects/promise/index.md?plain=1" title="Folder: en-us/web/javascript/reference/global_objects/promise (Opens in a new tab)" target="_blank" rel="noopener noreferrer">View this page on GitHub</a> <!-- -->•<!-- --> <a href="https://github.com/mdn/content/issues/new?template=page-report.yml&amp;mdn-url=https%3A%2F%2Fdeveloper.mozilla.org%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FGlobal_Objects%2FPromise&amp;metadata=%3C%21--+Do+not+make+changes+below+this+line+--%3E%0A%3Cdetails%3E%0A%3Csummary%3EPage+report+details%3C%2Fsummary%3E%0A%0A*+Folder%3A+%60en-us%2Fweb%2Fjavascript%2Freference%2Fglobal_objects%2Fpromise%60%0A*+MDN+URL%3A+https%3A%2F%2Fdeveloper.mozilla.org%2Fen-US%2Fdocs%2FWeb%2FJavaScript%2FReference%2FGlobal_Objects%2FPromise%0A*+GitHub+URL%3A+https%3A%2F%2Fgithub.com%2Fmdn%2Fcontent%2Fblob%2Fmain%2Ffiles%2Fen-us%2Fweb%2Fjavascript%2Freference%2Fglobal_objects%2Fpromise%2Findex.md%0A*+Last+commit%3A+https%3A%2F%2Fgithub.com%2Fmdn%2Fcontent%2Fcommit%2F364a4d02b10854ab7cef4ff4b0ec3616d4e1c8ab%0A*+Document+last+modified%3A+2025-05-23T04%3A35%3A27.000Z%0A%0A%3C%2Fdetails%3E" title="This will take you to GitHub to file a new issue." target="_blank" rel="noopener noreferrer">Report a problem with this content</a></div></div></aside></main></div></div><footer id="nav-footer" class="page-footer"><div class="page-footer-grid"><div class="page-footer-logo-col"><a href="/" class="mdn-footer-logo" aria-label="MDN homepage"><svg width="48" height="17" viewBox="0 0 48 17" fill="none" xmlns="http://www.w3.org/2000/svg"><title id="mdn-footer-logo-svg">MDN logo</title><path d="M20.04 16.512H15.504V10.416C15.504 9.488 15.344 8.824 15.024 8.424C14.72 8.024 14.264 7.824 13.656 7.824C12.92 7.824 12.384 8.064 12.048 8.544C11.728 9.024 11.568 9.64 11.568 10.392V14.184H13.008V16.512H8.472V10.416C8.472 9.488 8.312 8.824 7.992 8.424C7.688 8.024 7.232 7.824 6.624 7.824C5.872 7.824 5.336 8.064 5.016 8.544C4.696 9.024 4.536 9.64 4.536 10.392V14.184H6.6V16.512H0V14.184H1.44V8.04H0.024V5.688H4.536V7.32C5.224 6.088 6.32 5.472 7.824 5.472C8.608 5.472 9.328 5.664 9.984 6.048C10.64 6.432 11.096 7.016 11.352 7.8C11.992 6.248 13.168 5.472 14.88 5.472C15.856 5.472 16.72 5.776 17.472 6.384C18.224 6.992 18.6 7.936 18.6 9.216V14.184H20.04V16.512Z" fill="currentColor"></path><path d="M33.6714 16.512H29.1354V14.496C28.8314 15.12 28.3834 15.656 27.7914 16.104C27.1994 16.536 26.4154 16.752 25.4394 16.752C24.0154 16.752 22.8954 16.264 22.0794 15.288C21.2634 14.312 20.8554 12.984 20.8554 11.304C20.8554 9.688 21.2554 8.312 22.0554 7.176C22.8554 6.04 24.0634 5.472 25.6794 5.472C26.5594 5.472 27.2794 5.648 27.8394 6C28.3994 6.352 28.8314 6.8 29.1354 7.344V2.352H26.9754V0H32.2314V14.184H33.6714V16.512ZM29.1354 11.04V10.776C29.1354 9.88 28.8954 9.184 28.4154 8.688C27.9514 8.176 27.3674 7.92 26.6634 7.92C25.9754 7.92 25.3674 8.176 24.8394 8.688C24.3274 9.2 24.0714 10.008 24.0714 11.112C24.0714 12.152 24.3114 12.944 24.7914 13.488C25.2714 14.032 25.8394 14.304 26.4954 14.304C27.3114 14.304 27.9514 13.96 28.4154 13.272C28.8954 12.584 29.1354 11.84 29.1354 11.04Z" fill="currentColor"></path><path d="M47.9589 16.512H41.9829V14.184H43.4229V10.416C43.4229 9.488 43.2629 8.824 42.9429 8.424C42.6389 8.024 42.1829 7.824 41.5749 7.824C40.8389 7.824 40.2709 8.056 39.8709 8.52C39.4709 8.968 39.2629 9.56 39.2469 10.296V14.184H40.6869V16.512H34.7109V14.184H36.1509V8.04H34.5909V5.688H39.2469V7.344C39.9669 6.096 41.1269 5.472 42.7269 5.472C43.7509 5.472 44.6389 5.776 45.3909 6.384C46.1429 6.992 46.5189 7.936 46.5189 9.216V14.184H47.9589V16.512Z" fill="currentColor"></path></svg></a><p>Your blueprint for a better internet.</p><ul class="social-icons"><li><a href="https://bsky.app/profile/developer.mozilla.org" target="_blank" rel="me noopener noreferrer"><span class="icon icon-bluesky"></span><span class="visually-hidden">MDN on Bluesky</span></a></li><li><a href="https://mastodon.social/@mdn" target="_blank" rel="me noopener noreferrer"><span class="icon icon-mastodon"></span><span class="visually-hidden">MDN on Mastodon</span></a></li><li><a href="https://twitter.com/mozdevnet" target="_blank" rel="noopener noreferrer"><span class="icon icon-twitter-x"></span><span class="visually-hidden">MDN on X (formerly Twitter)</span></a></li><li><a href="https://github.com/mdn/" target="_blank" rel="noopener noreferrer"><span class="icon icon-github-mark-small"></span><span class="visually-hidden">MDN on GitHub</span></a></li><li><a href="/en-US/blog/rss.xml" target="_blank"><span class="icon icon-feed"></span><span class="visually-hidden">MDN Blog RSS Feed</span></a></li></ul></div><div class="page-footer-nav-col-1"><h2 class="footer-nav-heading">MDN</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a href="/en-US/about">About</a></li><li class="footer-nav-item"><a href="/en-US/blog/">Blog</a></li><li class="footer-nav-item"><a href="https://www.mozilla.org/en-US/careers/listings/?team=ProdOps" target="_blank" rel="noopener noreferrer">Careers</a></li><li class="footer-nav-item"><a href="/en-US/advertising">Advertise with us</a></li></ul></div><div class="page-footer-nav-col-2"><h2 class="footer-nav-heading">Support</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a class="footer-nav-link" href="https://support.mozilla.org/products/mdn-plus">Product help</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/docs/MDN/Community/Issues">Report an issue</a></li></ul></div><div class="page-footer-nav-col-3"><h2 class="footer-nav-heading">Our communities</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/community">MDN Community</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="https://discourse.mozilla.org/c/mdn/236" target="_blank" rel="noopener noreferrer">MDN Forum</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/discord" target="_blank" rel="noopener noreferrer">MDN Chat</a></li></ul></div><div class="page-footer-nav-col-4"><h2 class="footer-nav-heading">Developers</h2><ul class="footer-nav-list"><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/docs/Web">Web Technologies</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/docs/Learn">Learn Web Development</a></li><li class="footer-nav-item"><a class="footer-nav-link" href="/en-US/plus">MDN Plus</a></li><li class="footer-nav-item"><a href="https://hacks.mozilla.org/" target="_blank" rel="noopener noreferrer">Hacks Blog</a></li></ul></div><div class="page-footer-moz"><a href="https://www.mozilla.org/" class="footer-moz-logo-link" target="_blank" rel="noopener noreferrer"><svg xmlns="http://www.w3.org/2000/svg" width="137" height="32" fill="none" viewBox="0 0 267.431 62.607"><path fill="currentColor" d="m13.913 23.056 5.33 25.356h2.195l5.33-25.356h14.267v38.976h-7.578V29.694h-2.194l-7.264 32.337h-7.343L9.418 29.694H7.223v32.337H-.354V23.056Zm47.137 9.123c9.12 0 14.423 5.385 14.423 15.214s-5.33 15.214-14.423 15.214c-9.12 0-14.423-5.385-14.423-15.214 0-9.855 5.304-15.214 14.423-15.214m0 24.363c4.285 0 6.428-2.196 6.428-7.032v-4.287c0-4.836-2.143-7.032-6.428-7.032s-6.428 2.196-6.428 7.032v4.287c0 4.836 2.143 7.032 6.428 7.032m18.473-.157 15.47-18.01h-15.26v-5.647h24.352v5.646L88.616 56.385h15.704v5.646H79.523Zm29.318-23.657h11.183V62.03h-7.578V38.375h-3.632v-5.646zm3.605-9.672h7.578v5.646h-7.578zm13.17 0h11.21v38.976h-7.578v-33.33h-3.632zm16.801 0H153.6v38.976h-7.577v-33.33h-3.632v-5.646zm29.03 9.123c4.442 0 7.394 2.143 8.231 5.881h2.194v-5.332h9.276v5.646h-3.632v18.011h3.632v5.646h-4.442c-3.135 0-4.834-1.699-4.834-4.836V56.7h-2.194c-.81 3.738-3.789 5.881-8.23 5.881-6.978 0-11.916-5.829-11.916-15.214 0-9.384 4.938-15.187 11.915-15.187m2.3 24.363c4.284 0 6.192-2.196 6.192-7.032v-4.287c0-4.836-1.908-7.032-6.193-7.032-4.18 0-6.193 2.196-6.193 7.032v4.287c0 4.836 2.012 7.032 6.193 7.032m48.34 5.489h-7.577V0h7.577zm6.585-29.643h32.165v-2.196l-21.295-7.634v-6.143l21.295-7.633V6.588h-25.345V0h32.165v12.522l-17.35 5.881V20.6l17.35 5.882v12.521h-38.985zm0-25.801h6.794v6.796h-6.794z"></path></svg></a><ul class="footer-moz-list"><li class="footer-moz-item"><a href="https://www.mozilla.org/privacy/websites/" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Website Privacy Notice</a></li><li class="footer-moz-item"><a href="https://www.mozilla.org/privacy/websites/#cookies" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Cookies</a></li><li class="footer-moz-item"><a href="https://www.mozilla.org/about/legal/terms/mozilla" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Legal</a></li><li class="footer-moz-item"><a href="https://www.mozilla.org/about/governance/policies/participation/" class="footer-moz-link" target="_blank" rel="noopener noreferrer">Community Participation Guidelines</a></li></ul></div><div class="page-footer-legal"><p id="license" class="page-footer-legal-text">Visit<!-- --> <a href="https://www.mozilla.org" target="_blank" rel="noopener noreferrer">Mozilla Corporation’s</a> <!-- -->not-for-profit parent, the<!-- --> <a target="_blank" rel="noopener noreferrer" href="https://foundation.mozilla.org/">Mozilla Foundation</a>.<br>Portions of this content are ©1998–<!-- -->2025<!-- --> by individual mozilla.org contributors. Content available under<!-- --> <a href="/en-US/docs/MDN/Writing_guidelines/Attrib_copyright_license">a Creative Commons license</a>.</p></div></div></footer></div><script type="application/json" id="hydration">{"url":"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise","doc":{"body":[{"type":"prose","value":{"id":null,"title":null,"isH3":false,"content":"<p>The <strong><code>Promise</code></strong> object represents the eventual completion (or failure) of an asynchronous operation and its resulting value.</p>\n<p>To learn about the way promises work and how you can use them, we advise you to read <a href=\"/en-US/docs/Web/JavaScript/Guide/Using_promises\">Using promises</a> first.</p>"}},{"type":"prose","value":{"id":"description","title":"Description","isH3":false,"content":"<p>A <code>Promise</code> is a proxy for a value not necessarily known when the promise is created. It allows you to associate handlers with an asynchronous action's eventual success value or failure reason. This lets asynchronous methods return values like synchronous methods: instead of immediately returning the final value, the asynchronous method returns a <em>promise</em> to supply the value at some point in the future.</p>\n<p>A <code>Promise</code> is in one of these states:</p>\n<ul>\n<li><em>pending</em>: initial state, neither fulfilled nor rejected.</li>\n<li><em>fulfilled</em>: meaning that the operation was completed successfully.</li>\n<li><em>rejected</em>: meaning that the operation failed.</li>\n</ul>\n<p>The <em>eventual state</em> of a pending promise can either be <em>fulfilled</em> with a value or <em>rejected</em> with a reason (error).\nWhen either of these options occur, the associated handlers queued up by a promise's <code>then</code> method are called. If the promise has already been fulfilled or rejected when a corresponding handler is attached, the handler will be called, so there is no race condition between an asynchronous operation completing and its handlers being attached.</p>\n<p>A promise is said to be <em>settled</em> if it is either fulfilled or rejected, but not pending.</p>\n<p><img src=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/promises.png\" alt=\"Flowchart showing how the Promise state transitions between pending, fulfilled, and rejected via then/catch handlers. A pending promise can become either fulfilled or rejected. If fulfilled, the &quot;on fulfillment&quot; handler, or first parameter of the then() method, is executed and carries out further asynchronous actions. If rejected, the error handler, either passed as the second parameter of the then() method or as the sole parameter of the catch() method, gets executed.\" width=\"801\" height=\"297\" loading=\"lazy\"></p>\n<p>You will also hear the term <em>resolved</em> used with promises — this means that the promise is settled or \"locked-in\" to match the eventual state of another promise, and further resolving or rejecting it has no effect. The <a href=\"https://github.com/domenic/promises-unwrapping/blob/master/docs/states-and-fates.md\" class=\"external\" target=\"_blank\">States and fates</a> document from the original Promise proposal contains more details about promise terminology. Colloquially, \"resolved\" promises are often equivalent to \"fulfilled\" promises, but as illustrated in \"States and fates\", resolved promises can be pending or rejected as well. For example:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>new Promise((resolveOuter) =&gt; {\n  resolveOuter(\n    new Promise((resolveInner) =&gt; {\n      setTimeout(resolveInner, 1000);\n    }),\n  );\n});\n</code></pre></div>\n<p>This promise is already <em>resolved</em> at the time when it's created (because the <code>resolveOuter</code> is called synchronously), but it is resolved with another promise, and therefore won't be <em>fulfilled</em> until 1 second later, when the inner promise fulfills. In practice, the \"resolution\" is often done behind the scenes and not observable, and only its fulfillment or rejection are.</p>\n<div class=\"notecard note\">\n<p><strong>Note:</strong>\nSeveral other languages have mechanisms for lazy evaluation and deferring a computation, which they also call \"promises\", e.g., Scheme. Promises in JavaScript represent processes that are already happening, which can be chained with callback functions. If you are looking to lazily evaluate an expression, consider using a function with no arguments e.g., <code>f = () =&gt; expression</code> to create the lazily-evaluated expression, and <code>f()</code> to evaluate the expression immediately.</p>\n</div>\n<p><code>Promise</code> itself has no first-class protocol for cancellation, but you may be able to directly cancel the underlying asynchronous operation, typically using <a href=\"/en-US/docs/Web/API/AbortController\"><code>AbortController</code></a>.</p>"}},{"type":"prose","value":{"id":"chained_promises","title":"Chained Promises","isH3":true,"content":"<p>The promise methods <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then\"><code>then()</code></a>, <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/catch\"><code>catch()</code></a>, and <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/finally\"><code>finally()</code></a> are used to associate further action with a promise that becomes settled. The <code>then()</code> method takes up to two arguments; the first argument is a callback function for the fulfilled case of the promise, and the second argument is a callback function for the rejected case. The <code>catch()</code> and <code>finally()</code> methods call <code>then()</code> internally and make error handling less verbose. For example, a <code>catch()</code> is really just a <code>then()</code> without passing the fulfillment handler. As these methods return promises, they can be chained. For example:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>const myPromise = new Promise((resolve, reject) =&gt; {\n  setTimeout(() =&gt; {\n    resolve(\"foo\");\n  }, 300);\n});\n\nmyPromise\n  .then(handleFulfilledA, handleRejectedA)\n  .then(handleFulfilledB, handleRejectedB)\n  .then(handleFulfilledC, handleRejectedC);\n</code></pre></div>\n<p>We will use the following terminology: <em>initial promise</em> is the promise on which <code>then</code> is called; <em>new promise</em> is the promise returned by <code>then</code>. The two callbacks passed to <code>then</code> are called <em>fulfillment handler</em> and <em>rejection handler</em>, respectively.</p>\n<p>The settled state of the initial promise determines which handler to execute.</p>\n<ul>\n<li>If the initial promise is fulfilled, the fulfillment handler is called with the fulfillment value.</li>\n<li>If the initial promise is rejected, the rejection handler is called with the rejection reason.</li>\n</ul>\n<p>The completion of the handler determines the settled state of the new promise.</p>\n<ul>\n<li>If the handler returns a <a href=\"#thenables\">thenable</a> value, the new promise settles in the same state as the returned value.</li>\n<li>If the handler returns a non-thenable value, the new promise is fulfilled with the returned value.</li>\n<li>If the handler throws an error, the new promise is rejected with the thrown error.</li>\n<li>If the initial promise has no corresponding handler attached, the new promise will settle to the same state as the initial promise — that is, without a rejection handler, a rejected promise stays rejected with the same reason.</li>\n</ul>\n<p>For example, in the code above, if <code>myPromise</code> rejects, <code>handleRejectedA</code> will be called, and if <code>handleRejectedA</code> completes normally (without throwing or returning a rejected promise), the promise returned by the first <code>then</code> will be fulfilled instead of staying rejected. Therefore, if an error must be handled immediately, but we want to maintain the error state down the chain, we must throw an error of some type in the rejection handler. On the other hand, in the absence of an immediate need, we can leave out error handling until the final <code>catch()</code> handler.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>myPromise\n  .then(handleFulfilledA)\n  .then(handleFulfilledB)\n  .then(handleFulfilledC)\n  .catch(handleRejectedAny);\n</code></pre></div>\n<p>Using <a href=\"/en-US/docs/Web/JavaScript/Reference/Functions/Arrow_functions\">arrow functions</a> for the callback functions, implementation of the promise chain might look something like this:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>myPromise\n  .then((value) =&gt; `${value} and bar`)\n  .then((value) =&gt; `${value} and bar again`)\n  .then((value) =&gt; `${value} and again`)\n  .then((value) =&gt; `${value} and again`)\n  .then((value) =&gt; {\n    console.log(value);\n  })\n  .catch((err) =&gt; {\n    console.error(err);\n  });\n</code></pre></div>\n<div class=\"notecard note\">\n<p><strong>Note:</strong>\nFor faster execution, all synchronous actions should preferably be done within one handler, otherwise it would take several ticks to execute all handlers in sequence.</p>\n</div>\n<p>JavaScript maintains a <a href=\"/en-US/docs/Web/JavaScript/Reference/Execution_model\">job queue</a>. Each time, JavaScript picks a job from the queue and executes it to completion. The jobs are defined by the executor of the <code>Promise()</code> constructor, the handlers passed to <code>then</code>, or any platform API that returns a promise. The promises in a chain represent the dependency relationship between these jobs. When a promise settles, the respective handlers associated with it are added to the back of the job queue.</p>\n<p>A promise can participate in more than one chain. For the following code, the fulfillment of <code>promiseA</code> will cause both <code>handleFulfilled1</code> and <code>handleFulfilled2</code> to be added to the job queue. Because <code>handleFulfilled1</code> is registered first, it will be invoked first.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>const promiseA = new Promise(myExecutorFunc);\nconst promiseB = promiseA.then(handleFulfilled1, handleRejected1);\nconst promiseC = promiseA.then(handleFulfilled2, handleRejected2);\n</code></pre></div>\n<p>An action can be assigned to an already settled promise. In this case, the action is added immediately to the back of the job queue and will be performed when all existing jobs are completed. Therefore, an action for an already \"settled\" promise will occur only after the current synchronous code completes and at least one loop-tick has passed. This guarantees that promise actions are asynchronous.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>const promiseA = new Promise((resolve, reject) =&gt; {\n  resolve(777);\n});\n// At this point, \"promiseA\" is already settled.\npromiseA.then((val) =&gt; console.log(\"asynchronous logging has val:\", val));\nconsole.log(\"immediate logging\");\n\n// produces output in this order:\n// immediate logging\n// asynchronous logging has val: 777\n</code></pre></div>"}},{"type":"prose","value":{"id":"thenables","title":"Thenables","isH3":true,"content":"<p>The JavaScript ecosystem had made multiple Promise implementations long before it became part of the language. Despite being represented differently internally, at the minimum, all Promise-like objects implement the <em>Thenable</em> interface. A thenable implements the <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then\"><code>.then()</code></a> method, which is called with two callbacks: one for when the promise is fulfilled, one for when it's rejected. Promises are thenables as well.</p>\n<p>To interoperate with the existing Promise implementations, the language allows using thenables in place of promises. For example, <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve\"><code>Promise.resolve</code></a> will not only resolve promises, but also trace thenables.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>const aThenable = {\n  then(onFulfilled, onRejected) {\n    onFulfilled({\n      // The thenable is fulfilled with another thenable\n      then(onFulfilled, onRejected) {\n        onFulfilled(42);\n      },\n    });\n  },\n};\n\nPromise.resolve(aThenable); // A promise fulfilled with 42\n</code></pre></div>"}},{"type":"prose","value":{"id":"promise_concurrency","title":"Promise concurrency","isH3":true,"content":"<p>The <code>Promise</code> class offers four static methods to facilitate async task <a href=\"https://en.wikipedia.org/wiki/Concurrent_computing\" class=\"external\" target=\"_blank\">concurrency</a>:</p>\n<dl>\n<dt id=\"promise.all\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all\"><code>Promise.all()</code></a></dt>\n<dd>\n<p>Fulfills when <strong>all</strong> of the promises fulfill; rejects when <strong>any</strong> of the promises rejects.</p>\n</dd>\n<dt id=\"promise.allsettled\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled\"><code>Promise.allSettled()</code></a></dt>\n<dd>\n<p>Fulfills when <strong>all</strong> promises settle.</p>\n</dd>\n<dt id=\"promise.any\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/any\"><code>Promise.any()</code></a></dt>\n<dd>\n<p>Fulfills when <strong>any</strong> of the promises fulfills; rejects when <strong>all</strong> of the promises reject.</p>\n</dd>\n<dt id=\"promise.race\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race\"><code>Promise.race()</code></a></dt>\n<dd>\n<p>Settles when <strong>any</strong> of the promises settles. In other words, fulfills when any of the promises fulfills; rejects when any of the promises rejects.</p>\n</dd>\n</dl>\n<p>All these methods take an <a href=\"/en-US/docs/Web/JavaScript/Reference/Iteration_protocols#the_iterable_protocol\">iterable</a> of promises (<a href=\"#thenables\">thenables</a>, to be exact) and return a new promise. They all support subclassing, which means they can be called on subclasses of <code>Promise</code>, and the result will be a promise of the subclass type. To do so, the subclass's constructor must implement the same signature as the <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise\"><code>Promise()</code></a> constructor — accepting a single <code>executor</code> function that can be called with the <code>resolve</code> and <code>reject</code> callbacks as parameters. The subclass must also have a <code>resolve</code> static method that can be called like <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve\"><code>Promise.resolve()</code></a> to resolve values to promises.</p>\n<p>Note that JavaScript is <a href=\"/en-US/docs/Glossary/Thread\">single-threaded</a> by nature, so at a given instant, only one task will be executing, although control can shift between different promises, making execution of the promises appear concurrent. <a href=\"https://en.wikipedia.org/wiki/Parallel_computing\" class=\"external\" target=\"_blank\">Parallel execution</a> in JavaScript can only be achieved through <a href=\"/en-US/docs/Web/API/Web_Workers_API\">worker threads</a>.</p>"}},{"type":"prose","value":{"id":"constructor","title":"Constructor","isH3":false,"content":"<dl>\n<dt id=\"promise\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise\"><code>Promise()</code></a></dt>\n<dd>\n<p>Creates a new <code>Promise</code> object. The constructor is primarily used to wrap functions that do not already support promises.</p>\n</dd>\n</dl>"}},{"type":"prose","value":{"id":"static_properties","title":"Static properties","isH3":false,"content":"<dl>\n<dt id=\"promisesymbol.species\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Symbol.species\"><code>Promise[Symbol.species]</code></a></dt>\n<dd>\n<p>Returns the constructor used to construct return values from promise methods.</p>\n</dd>\n</dl>"}},{"type":"prose","value":{"id":"static_methods","title":"Static methods","isH3":false,"content":"<dl>\n<dt id=\"promise.all_2\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all\"><code>Promise.all()</code></a></dt>\n<dd>\n<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise fulfills when all of the input's promises fulfill (including when an empty iterable is passed), with an array of the fulfillment values. It rejects when any of the input's promises reject, with this first rejection reason.</p>\n</dd>\n<dt id=\"promise.allsettled_2\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled\"><code>Promise.allSettled()</code></a></dt>\n<dd>\n<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise fulfills when all of the input's promises settle (including when an empty iterable is passed), with an array of objects that describe the outcome of each promise.</p>\n</dd>\n<dt id=\"promise.any_2\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/any\"><code>Promise.any()</code></a></dt>\n<dd>\n<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise fulfills when any of the input's promises fulfill, with this first fulfillment value. It rejects when all of the input's promises reject (including when an empty iterable is passed), with an <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError\"><code>AggregateError</code></a> containing an array of rejection reasons.</p>\n</dd>\n<dt id=\"promise.race_2\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race\"><code>Promise.race()</code></a></dt>\n<dd>\n<p>Takes an iterable of promises as input and returns a single <code>Promise</code>. This returned promise settles with the eventual state of the first promise that settles.</p>\n</dd>\n<dt id=\"promise.reject\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/reject\"><code>Promise.reject()</code></a></dt>\n<dd>\n<p>Returns a new <code>Promise</code> object that is rejected with the given reason.</p>\n</dd>\n<dt id=\"promise.resolve\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve\"><code>Promise.resolve()</code></a></dt>\n<dd>\n<p>Returns a <code>Promise</code> object that is resolved with the given value. If the value is a thenable (i.e., has a <code>then</code> method), the returned promise will \"follow\" that thenable, adopting its eventual state; otherwise, the returned promise will be fulfilled with the value.</p>\n</dd>\n<dt id=\"promise.try\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/try\"><code>Promise.try()</code></a></dt>\n<dd>\n<p>Takes a callback of any kind (returns or throws, synchronously or asynchronously) and wraps its result in a <code>Promise</code>.</p>\n</dd>\n<dt id=\"promise.withresolvers\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers\"><code>Promise.withResolvers()</code></a></dt>\n<dd>\n<p>Returns an object containing a new <code>Promise</code> object and two functions to resolve or reject it, corresponding to the two parameters passed to the executor of the <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise\"><code>Promise()</code></a> constructor.</p>\n</dd>\n</dl>"}},{"type":"prose","value":{"id":"instance_properties","title":"Instance properties","isH3":false,"content":"<p>These properties are defined on <code>Promise.prototype</code> and shared by all <code>Promise</code> instances.</p>\n<dl>\n<dt id=\"promise.prototype.constructor\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/constructor\"><code>Promise.prototype.constructor</code></a></dt>\n<dd>\n<p>The constructor function that created the instance object. For <code>Promise</code> instances, the initial value is the <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise\"><code>Promise</code></a> constructor.</p>\n</dd>\n<dt id=\"promise.prototypesymbol.tostringtag\"><a href=\"#promise.prototypesymbol.tostringtag\"><code>Promise.prototype[Symbol.toStringTag]</code></a></dt>\n<dd>\n<p>The initial value of the <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol/toStringTag\"><code>[Symbol.toStringTag]</code></a> property is the string <code>\"Promise\"</code>. This property is used in <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/toString\"><code>Object.prototype.toString()</code></a>.</p>\n</dd>\n</dl>"}},{"type":"prose","value":{"id":"instance_methods","title":"Instance methods","isH3":false,"content":"<dl>\n<dt id=\"promise.prototype.catch\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/catch\"><code>Promise.prototype.catch()</code></a></dt>\n<dd>\n<p>Appends a rejection handler callback to the promise, and returns a new promise resolving to the return value of the callback if it is called, or to its original fulfillment value if the promise is instead fulfilled.</p>\n</dd>\n<dt id=\"promise.prototype.finally\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/finally\"><code>Promise.prototype.finally()</code></a></dt>\n<dd>\n<p>Appends a handler to the promise, and returns a new promise that is resolved when the original promise is resolved. The handler is called when the promise is settled, whether fulfilled or rejected.</p>\n</dd>\n<dt id=\"promise.prototype.then\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then\"><code>Promise.prototype.then()</code></a></dt>\n<dd>\n<p>Appends fulfillment and rejection handlers to the promise, and returns a new promise resolving to the return value of the called handler, or to its original settled value if the promise was not handled (i.e., if the relevant handler <code>onFulfilled</code> or <code>onRejected</code> is not a function).</p>\n</dd>\n</dl>"}},{"type":"prose","value":{"id":"examples","title":"Examples","isH3":false,"content":""}},{"type":"prose","value":{"id":"basic_example","title":"Basic Example","isH3":true,"content":"<p>In this example, we use <code>setTimeout(...)</code> to simulate async code.\nIn reality, you will probably be using something like XHR or an HTML API.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>const myFirstPromise = new Promise((resolve, reject) =&gt; {\n  // We call resolve(...) when what we were doing asynchronously\n  // was successful, and reject(...) when it failed.\n  setTimeout(() =&gt; {\n    resolve(\"Success!\"); // Yay! Everything went well!\n  }, 250);\n});\n\nmyFirstPromise.then((successMessage) =&gt; {\n  // successMessage is whatever we passed in the resolve(...) function above.\n  // It doesn't have to be a string, but if it is only a succeed message, it probably will be.\n  console.log(`Yay! ${successMessage}`);\n});\n</code></pre></div>"}},{"type":"prose","value":{"id":"example_with_diverse_situations","title":"Example with diverse situations","isH3":true,"content":"<p>This example shows diverse techniques for using Promise capabilities and diverse situations that can occur. To understand this, start by scrolling to the bottom of the code block, and examine the promise chain. Upon provision of an initial promise, a chain of promises can follow. The chain is composed of <code>.then()</code> calls, and typically (but not necessarily) has a single <code>.catch()</code> at the end, optionally followed by <code>.finally()</code>. In this example, the promise chain is initiated by a custom-written <code>new Promise()</code> construct; but in actual practice, promise chains more typically start with an API function (written by someone else) that returns a promise.</p>\n<p>The example function <code>tetheredGetNumber()</code> shows that a promise generator will utilize <code>reject()</code> while setting up an asynchronous call, or within the call-back, or both. The function <code>promiseGetWord()</code> illustrates how an API function might generate and return a promise in a self-contained manner.</p>\n<p>Note that the function <code>troubleWithGetNumber()</code> ends with a <code>throw</code>. That is forced because a promise chain goes through all the <code>.then()</code> promises, even after an error, and without the <code>throw</code>, the error would seem \"fixed\". This is a hassle, and for this reason, it is common to omit <code>onRejected</code> throughout the chain of <code>.then()</code> promises, and just have a single <code>onRejected</code> in the final <code>catch()</code>.</p>\n<p>This code can be run under NodeJS. Comprehension is enhanced by seeing the errors actually occur. To force more errors, change the <code>threshold</code> values.</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate\"><code>// To experiment with error handling, \"threshold\" values cause errors randomly\nconst THRESHOLD_A = 8; // can use zero 0 to guarantee error\n\nfunction tetheredGetNumber(resolve, reject) {\n  setTimeout(() =&gt; {\n    const randomInt = Date.now();\n    const value = randomInt % 10;\n    if (value &lt; THRESHOLD_A) {\n      resolve(value);\n    } else {\n      reject(new RangeError(`Too large: ${value}`));\n    }\n  }, 500);\n}\n\nfunction determineParity(value) {\n  const isOdd = value % 2 === 1;\n  return { value, isOdd };\n}\n\nfunction troubleWithGetNumber(reason) {\n  const err = new Error(\"Trouble getting number\", { cause: reason });\n  console.error(err);\n  throw err;\n}\n\nfunction promiseGetWord(parityInfo) {\n  return new Promise((resolve, reject) =&gt; {\n    const { value, isOdd } = parityInfo;\n    if (value &gt;= THRESHOLD_A - 1) {\n      reject(new RangeError(`Still too large: ${value}`));\n    } else {\n      parityInfo.wordEvenOdd = isOdd ? \"odd\" : \"even\";\n      resolve(parityInfo);\n    }\n  });\n}\n\nnew Promise(tetheredGetNumber)\n  .then(determineParity, troubleWithGetNumber)\n  .then(promiseGetWord)\n  .then((info) =&gt; {\n    console.log(`Got: ${info.value}, ${info.wordEvenOdd}`);\n    return info;\n  })\n  .catch((reason) =&gt; {\n    if (reason.cause) {\n      console.error(\"Had previously handled error\");\n    } else {\n      console.error(`Trouble with promiseGetWord(): ${reason}`);\n    }\n  })\n  .finally((info) =&gt; console.log(\"All done\"));\n</code></pre></div>"}},{"type":"prose","value":{"id":"advanced_example","title":"Advanced Example","isH3":true,"content":"<p>This small example shows the mechanism of a <code>Promise</code>. The <code>testPromise()</code> method is called each time the <a href=\"/en-US/docs/Web/HTML/Reference/Elements/button\"><code>&lt;button&gt;</code></a> is clicked. It creates a promise that will be fulfilled, using <a href=\"/en-US/docs/Web/API/Window/setTimeout\" title=\"setTimeout()\"><code>setTimeout()</code></a>, to the promise count (number starting from 1) every 1-3 seconds, at random. The <code>Promise()</code> constructor is used to create the promise.</p>\n<p>The fulfillment of the promise is logged, via a fulfill callback set using <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then\"><code>p1.then()</code></a>. A few logs show how the synchronous part of the method is decoupled from the asynchronous completion of the promise.</p>\n<p>By clicking the button several times in a short amount of time, you'll even see the different promises being fulfilled one after another.</p>\n<h4 id=\"html\">HTML</h4>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">html</span></div><pre class=\"brush: html notranslate live-sample---advanced_example\"><code>&lt;button id=\"make-promise\"&gt;Make a promise!&lt;/button&gt;\n&lt;div id=\"log\"&gt;&lt;/div&gt;\n</code></pre></div>\n<h4 id=\"javascript\">JavaScript</h4>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js notranslate live-sample---advanced_example\"><code>\"use strict\";\n\nlet promiseCount = 0;\n\nfunction testPromise() {\n  const thisPromiseCount = ++promiseCount;\n  const log = document.getElementById(\"log\");\n  // begin\n  log.insertAdjacentHTML(\"beforeend\", `${thisPromiseCount}) Started&lt;br&gt;`);\n  // We make a new promise: we promise a numeric count of this promise,\n  // starting from 1 (after waiting 3s)\n  const p1 = new Promise((resolve, reject) =&gt; {\n    // The executor function is called with the ability\n    // to resolve or reject the promise\n    log.insertAdjacentHTML(\n      \"beforeend\",\n      `${thisPromiseCount}) Promise constructor&lt;br&gt;`,\n    );\n    // This is only an example to create asynchronism\n    setTimeout(\n      () =&gt; {\n        // We fulfill the promise\n        resolve(thisPromiseCount);\n      },\n      Math.random() * 2000 + 1000,\n    );\n  });\n\n  // We define what to do when the promise is resolved with the then() call,\n  // and what to do when the promise is rejected with the catch() call\n  p1.then((val) =&gt; {\n    // Log the fulfillment value\n    log.insertAdjacentHTML(\"beforeend\", `${val}) Promise fulfilled&lt;br&gt;`);\n  }).catch((reason) =&gt; {\n    // Log the rejection reason\n    console.log(`Handle rejected promise (${reason}) here.`);\n  });\n  // end\n  log.insertAdjacentHTML(\"beforeend\", `${thisPromiseCount}) Promise made&lt;br&gt;`);\n}\n\nconst btn = document.getElementById(\"make-promise\");\nbtn.addEventListener(\"click\", testPromise);\n</code></pre></div>\n<h4 id=\"result\">Result</h4>\n<div class=\"code-example\"><div class=\"example-header\"></div><iframe class=\"sample-code-frame\" title=\"Advanced Example sample\" id=\"frame_advanced_example\" width=\"500\" height=\"200\" src=\"about:blank\" data-live-path=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/\" data-live-id=\"advanced_example\" sandbox=\"allow-same-origin allow-scripts\" loading=\"lazy\"></iframe></div>"}},{"type":"prose","value":{"id":"loading_an_image_with_xhr","title":"Loading an image with XHR","isH3":true,"content":"<p>Another example using <code>Promise</code> and <a href=\"/en-US/docs/Web/API/XMLHttpRequest\"><code>XMLHttpRequest</code></a> to load an image is shown below.\nEach step is commented on and allows you to follow the Promise and XHR architecture closely.</p>\n<div class=\"code-example\"><pre class=\"brush: html hidden live-sample___promises notranslate live-sample---promises\"><code>&lt;h1&gt;Promise example&lt;/h1&gt;\n</code></pre></div>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">js</span></div><pre class=\"brush: js live-sample___promises notranslate live-sample---promises\"><code>function imgLoad(url) {\n  // Create new promise with the Promise() constructor;\n  // This has as its argument a function with two parameters, resolve and reject\n  return new Promise((resolve, reject) =&gt; {\n    // XHR to load an image\n    const request = new XMLHttpRequest();\n    request.open(\"GET\", url);\n    request.responseType = \"blob\";\n    // When the request loads, check whether it was successful\n    request.onload = () =&gt; {\n      if (request.status === 200) {\n        // If successful, resolve the promise by passing back the request response\n        resolve(request.response);\n      } else {\n        // If it fails, reject the promise with an error message\n        reject(\n          Error(\n            `Image didn't load successfully; error code: + ${request.statusText}`,\n          ),\n        );\n      }\n    };\n    // Handle network errors\n    request.onerror = () =&gt; reject(new Error(\"There was a network error.\"));\n    // Send the request\n    request.send();\n  });\n}\n\n// Get a reference to the body element, and create a new image object\nconst body = document.querySelector(\"body\");\nconst myImage = new Image();\nconst imgUrl =\n  \"https://mdn.github.io/shared-assets/images/examples/round-balloon.png\";\n\n// Call the function with the URL we want to load, then chain the\n// promise then() method with two callbacks\nimgLoad(imgUrl).then(\n  (response) =&gt; {\n    // The first runs when the promise resolves, with the request.response\n    // specified within the resolve() method.\n    const imageURL = URL.createObjectURL(response);\n    myImage.src = imageURL;\n    body.appendChild(myImage);\n  },\n  (error) =&gt; {\n    // The second runs when the promise\n    // is rejected, and logs the Error specified with the reject() method.\n    console.log(error);\n  },\n);\n</code></pre></div>\n<div class=\"code-example\"><div class=\"example-header\"></div><iframe class=\"sample-code-frame\" title=\"promises sample\" id=\"frame_promises\" height=\"240px\" src=\"about:blank\" data-live-path=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/\" data-live-id=\"promises\" sandbox=\"allow-same-origin allow-scripts\" loading=\"lazy\"></iframe></div>"}},{"type":"prose","value":{"id":"incumbent_settings_object_tracking","title":"Incumbent settings object tracking","isH3":true,"content":"<p>A settings object is an <a href=\"https://html.spec.whatwg.org/multipage/webappapis.html#environment-settings-object\" class=\"external\" target=\"_blank\">environment</a> that provides additional information when JavaScript code is running. This includes the realm and module map, as well as HTML specific information such as the origin. The incumbent settings object is tracked in order to ensure that the browser knows which one to use for a given piece of user code.</p>\n<p>To better picture this, we can take a closer look at how the realm might be an issue. A <strong>realm</strong> can be roughly thought of as the global object. What is unique about realms is that they hold all of the necessary information to run JavaScript code. This includes objects like <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array\"><code>Array</code></a> and <a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error\"><code>Error</code></a>. Each settings object has its own \"copy\" of these and they are not shared. That can cause some unexpected behavior in relation to promises. In order to get around this, we track something called the <strong>incumbent settings object</strong>. This represents information specific to the context of the user code responsible for a certain function call.</p>\n<p>To illustrate this a bit further we can take a look at how an <a href=\"/en-US/docs/Web/HTML/Reference/Elements/iframe\"><code>&lt;iframe&gt;</code></a> embedded in a document communicates with its host. Since all web APIs are aware of the incumbent settings object, the following will work in all browsers:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">html</span></div><pre class=\"brush: html notranslate\"><code>&lt;!doctype html&gt; &lt;iframe&gt;&lt;/iframe&gt;\n&lt;!-- we have a realm here --&gt;\n&lt;script&gt;\n  // we have a realm here as well\n  const bound = frames[0].postMessage.bind(frames[0], \"some data\", \"*\");\n  // bound is a built-in function — there is no user\n  // code on the stack, so which realm do we use?\n  setTimeout(bound);\n  // this still works, because we use the youngest\n  // realm (the incumbent) on the stack\n&lt;/script&gt;\n</code></pre></div>\n<p>The same concept applies to promises. If we modify the above example a little bit, we get this:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">html</span></div><pre class=\"brush: html notranslate\"><code>&lt;!doctype html&gt; &lt;iframe&gt;&lt;/iframe&gt;\n&lt;!-- we have a realm here --&gt;\n&lt;script&gt;\n  // we have a realm here as well\n  const bound = frames[0].postMessage.bind(frames[0], \"some data\", \"*\");\n  // bound is a built in function — there is no user\n  // code on the stack — which realm do we use?\n  Promise.resolve(undefined).then(bound);\n  // this still works, because we use the youngest\n  // realm (the incumbent) on the stack\n&lt;/script&gt;\n</code></pre></div>\n<p>If we change this so that the <code>&lt;iframe&gt;</code> in the document is listening to post messages, we can observe the effect of the incumbent settings object:</p>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">html</span></div><pre class=\"brush: html notranslate\"><code>&lt;!-- y.html --&gt;\n&lt;!doctype html&gt;\n&lt;iframe src=\"x.html\"&gt;&lt;/iframe&gt;\n&lt;script&gt;\n  const bound = frames[0].postMessage.bind(frames[0], \"some data\", \"*\");\n  Promise.resolve(undefined).then(bound);\n&lt;/script&gt;\n</code></pre></div>\n<div class=\"code-example\"><div class=\"example-header\"><span class=\"language-name\">html</span></div><pre class=\"brush: html notranslate\"><code>&lt;!-- x.html --&gt;\n&lt;!doctype html&gt;\n&lt;script&gt;\n  window.addEventListener(\n    \"message\",\n    (event) =&gt; {\n      document.querySelector(\"#text\").textContent = \"hello\";\n      // this code will only run in browsers that track the incumbent settings object\n      console.log(event);\n    },\n    false,\n  );\n&lt;/script&gt;\n</code></pre></div>\n<p>In the above example, the inner text of the <code>&lt;iframe&gt;</code> will be updated only if the incumbent settings object is tracked. This is because without tracking the incumbent, we may end up using the wrong environment to send the message.</p>\n<div class=\"notecard note\">\n<p><strong>Note:</strong>\nCurrently, incumbent realm tracking is fully implemented in Firefox, and has partial implementations in Chrome and Safari.</p>\n</div>"}},{"type":"specifications","value":{"id":"specifications","title":"Specifications","isH3":false,"specifications":[{"bcdSpecificationURL":"https://tc39.es/ecma262/multipage/control-abstraction-objects.html#sec-promise-objects","title":"ECMAScript® 2026 Language Specification"}],"query":"javascript.builtins.Promise"}},{"type":"browser_compatibility","value":{"id":"browser_compatibility","title":"Browser compatibility","isH3":false,"query":"javascript.builtins.Promise"}},{"type":"prose","value":{"id":"see_also","title":"See also","isH3":false,"content":"<ul>\n<li><a href=\"https://github.com/zloirock/core-js#ecmascript-promise\" class=\"external\" target=\"_blank\">Polyfill of <code>Promise</code> in <code>core-js</code></a></li>\n<li><a href=\"/en-US/docs/Web/JavaScript/Guide/Using_promises\">Using promises</a> guide</li>\n<li><a href=\"https://promisesaplus.com/\" class=\"external\" target=\"_blank\">Promises/A+ specification</a></li>\n<li><a href=\"https://web.dev/articles/promises\" class=\"external\" target=\"_blank\">JavaScript Promises: an introduction</a> on web.dev (2013)</li>\n<li><a href=\"https://www.slideshare.net/slideshow/callbacks-promises-and-coroutines-oh-my-the-evolution-of-asynchronicity-in-javascript/9953720\" class=\"external\" target=\"_blank\">Callbacks, Promises, and Coroutines: Asynchronous Programming Patterns in JavaScript</a> slide show by Domenic Denicola (2011)</li>\n</ul>"}}],"isActive":true,"isMarkdown":true,"isTranslated":false,"locale":"en-US","mdn_url":"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise","modified":"2025-05-23T04:35:27.000Z","native":"English (US)","noIndexing":false,"other_translations":[{"locale":"de","title":"Promise","native":"Deutsch"},{"locale":"es","title":"Promise","native":"Español"},{"locale":"fr","title":"Promise","native":"Français"},{"locale":"ja","title":"Promise","native":"日本語"},{"locale":"ko","title":"Promise","native":"한국어"},{"locale":"pt-BR","title":"Promise","native":"Português (do Brasil)"},{"locale":"ru","title":"Promise","native":"Русский"},{"locale":"zh-CN","title":"Promise","native":"中文 (简体)"},{"locale":"zh-TW","title":"Promise","native":"正體中文 (繁體)"}],"pageTitle":"Promise - JavaScript | MDN","parents":[{"uri":"/en-US/docs/Web","title":"References"},{"uri":"/en-US/docs/Web/JavaScript","title":"JavaScript"},{"uri":"/en-US/docs/Web/JavaScript/Reference","title":"Reference"},{"uri":"/en-US/docs/Web/JavaScript/Reference/Global_Objects","title":"Standard built-in objects"},{"uri":"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise","title":"Promise"}],"popularity":null,"short_title":"Promise","sidebarHTML":"<ol><li class=\"section\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects\">Standard built-in objects</a></li><li class=\"section\"><em><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise\" aria-current=\"page\"><code>Promise</code></a></em></li><li class=\"toggle\"><details open=\"\"><summary>Constructor</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise\"><code>Promise() constructor</code></a></li></ol></details></li><li class=\"toggle\"><details open=\"\"><summary>Static methods</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/all\"><code>Promise.all()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/allSettled\"><code>Promise.allSettled()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/any\"><code>Promise.any()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race\"><code>Promise.race()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/reject\"><code>Promise.reject()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/resolve\"><code>Promise.resolve()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/try\"><code>Promise.try()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers\"><code>Promise.withResolvers()</code></a></li></ol></details></li><li class=\"toggle\"><details open=\"\"><summary>Static properties</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Symbol.species\"><code>Promise[Symbol.species]</code></a></li></ol></details></li><li class=\"toggle\"><details open=\"\"><summary>Instance methods</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/catch\"><code>Promise.prototype.catch()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/finally\"><code>Promise.prototype.finally()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/then\"><code>Promise.prototype.then()</code></a></li></ol></details></li><li class=\"section\">Inheritance</li><li class=\"section\"><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object\"><code>Object/Function</code></a></li><li class=\"toggle\"><details><summary>Static methods</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/apply\"><code>Function.prototype.apply()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/bind\"><code>Function.prototype.bind()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/call\"><code>Function.prototype.call()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/toString\"><code>Function.prototype.toString()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/Symbol.hasInstance\"><code>Function.prototype[Symbol.hasInstance]()</code></a></li></ol></details></li><li class=\"toggle\"><details><summary>Static properties</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/displayName\"><code>Function: displayName</code></a><abbr class=\"icon icon-nonstandard\" title=\"Non-standard. Check cross-browser support before using.\">\n<span class=\"visually-hidden\">Non-standard</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/length\"><code>Function: length</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/name\"><code>Function: name</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/prototype\"><code>Function: prototype</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/arguments\"><code>Function.prototype.arguments</code></a><abbr class=\"icon icon-nonstandard\" title=\"Non-standard. Check cross-browser support before using.\">\n<span class=\"visually-hidden\">Non-standard</span>\n</abbr><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n<span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/caller\"><code>Function.prototype.caller</code></a><abbr class=\"icon icon-nonstandard\" title=\"Non-standard. Check cross-browser support before using.\">\n<span class=\"visually-hidden\">Non-standard</span>\n</abbr><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n<span class=\"visually-hidden\">Deprecated</span>\n</abbr></li></ol></details></li><li class=\"toggle\"><details><summary>Instance methods</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__defineGetter__\"><code>Object.prototype.__defineGetter__()</code></a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n<span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__defineSetter__\"><code>Object.prototype.__defineSetter__()</code></a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n<span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__lookupGetter__\"><code>Object.prototype.__lookupGetter__()</code></a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n<span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/__lookupSetter__\"><code>Object.prototype.__lookupSetter__()</code></a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n<span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/hasOwnProperty\"><code>Object.prototype.hasOwnProperty()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/isPrototypeOf\"><code>Object.prototype.isPrototypeOf()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/propertyIsEnumerable\"><code>Object.prototype.propertyIsEnumerable()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/toLocaleString\"><code>Object.prototype.toLocaleString()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/toString\"><code>Object.prototype.toString()</code></a></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/valueOf\"><code>Object.prototype.valueOf()</code></a></li></ol></details></li><li class=\"toggle\"><details><summary>Instance properties</summary><ol><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/proto\"><code>Object.prototype.__proto__</code></a><abbr class=\"icon icon-deprecated\" title=\"Deprecated. Not for use in new websites.\">\n<span class=\"visually-hidden\">Deprecated</span>\n</abbr></li><li><a href=\"/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/constructor\"><code>Object.prototype.constructor</code></a></li></ol></details></li></ol>","source":{"folder":"en-us/web/javascript/reference/global_objects/promise","github_url":"https://github.com/mdn/content/blob/main/files/en-us/web/javascript/reference/global_objects/promise/index.md","last_commit_url":"https://github.com/mdn/content/commit/364a4d02b10854ab7cef4ff4b0ec3616d4e1c8ab","filename":"index.md"},"summary":"The Promise object represents the eventual completion (or failure) of an asynchronous operation and its resulting value.","title":"Promise","toc":[{"text":"Description","id":"description"},{"text":"Constructor","id":"constructor"},{"text":"Static properties","id":"static_properties"},{"text":"Static methods","id":"static_methods"},{"text":"Instance properties","id":"instance_properties"},{"text":"Instance methods","id":"instance_methods"},{"text":"Examples","id":"examples"},{"text":"Specifications","id":"specifications"},{"text":"Browser compatibility","id":"browser_compatibility"},{"text":"See also","id":"see_also"}],"baseline":{"baseline":"high","baseline_low_date":"2015-07-29","baseline_high_date":"2018-01-29","support":{"chrome":"32","chrome_android":"32","edge":"12","firefox":"29","firefox_android":"29","safari":"8","safari_ios":"8"},"asterisk":true,"feature":{"status":{"baseline":"high","baseline_low_date":"2015-07-29","baseline_high_date":"2018-01-29","support":{"chrome":"32","chrome_android":"32","edge":"12","firefox":"29","firefox_android":"29","safari":"8","safari_ios":"8"}},"description_html":"A promise represents an asynchronous operation which eventually succeeds or fails.","name":"Promise (initial support)"}},"browserCompat":["javascript.builtins.Promise"],"pageType":"javascript-class"}}</script></body></html>